/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  REASON_TYPE_REASON_CANCEL,
  REASON_TYPE_REASON_DELAY,
} from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import parseTimeSpan from '@/src/helper/parseTime'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getAttribute } from '@/src/service/attribute'
import {
  createReason,
  getReasonDetail,
  getReasonList,
  updateReason,
} from '@/src/service/reasons'
import { getReasonType } from '@/src/service/reasonType'
import { getServiceDept } from '@/src/service/serviceDept'
import {
  closeModalReasonDetail,
  setSelectedReasonId,
} from '@/src/store/ModalArrivalFlightSlice'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Select,
  TreeSelect,
} from 'antd'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

const ModalReasonDetail = () => {
  const dispatch = useAppDispatch()
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const { visibleModalReasonDetail, selectFlightModalId, selectedReasonId } =
    useAppSelector(state => state.modalArrivalFlight)
  const { data: reasonDetail, isFetching } = useQuery({
    queryKey: [
      'reason-detail-delay-cancel',
      selectFlightModalId,
      selectedReasonId,
    ],
    queryFn: () =>
      getReasonDetail(
        selectFlightModalId as string,
        selectedReasonId as string
      ),
    enabled:
      !!selectedReasonId && !!selectFlightModalId && !!visibleModalReasonDetail,
  })

  const { refetch } = useQuery({
    queryKey: ['reasons', selectFlightModalId],
    queryFn: () => getReasonList(selectFlightModalId as string),
    enabled: !!selectFlightModalId,
  })

  const { data: serviceDept }: any = useQuery({
    queryKey: ['service-dept'],
    queryFn: () => getServiceDept(),
  })

  const { data: attribute }: any = useQuery({
    queryKey: ['attribute'],
    queryFn: () => getAttribute(),
  })

  const { data: reasonsType }: any = useQuery({
    queryKey: ['reasons-types'],
    queryFn: () => getReasonType(),
  })

  const mutation = useMutation({
    mutationFn: (values: any) =>
      selectedReasonId && selectFlightModalId
        ? updateReason(
            selectFlightModalId as string,
            selectedReasonId as string,
            values
          )
        : createReason(selectFlightModalId as string, values),
    onSuccess() {
      refetch()
      dispatch(setSelectedReasonId(null))
      dispatch(closeModalReasonDetail())
      form.resetFields()
    },
    onError(error) {
      handleApiError(error)
    },
  })

  const onSubmit = async (values: any) => {
    await form.validateFields()
    const hour = values.hour || 0
    const minutes = values.minutes || 0
    if (hour === 0 && minutes === 0) {
      message.error('Please enter time > 0')
      return
    }
    const totalMinutes = hour * 60 + minutes
    await mutation.mutateAsync({
      ...values,
      delayTime: totalMinutes,
    })
  }

  useEffect(() => {
    if (reasonDetail) {
      const { delayTime } = reasonDetail
      const { h, m } = parseTimeSpan(delayTime)
      form.setFieldsValue({
        ...reasonDetail,
        hour: h,
        minutes: m,
      })
    }
  }, [reasonDetail])

  return (
    <Modal
      open={visibleModalReasonDetail}
      width="60%"
      centered
      title={<div className="text-primary font-bold text-xl">REASON</div>}
      cancelButtonProps={{ style: { display: 'none' } }}
      okButtonProps={{ style: { display: 'none' } }}
      onCancel={() => {
        dispatch(closeModalReasonDetail())
        form.resetFields()
        dispatch(setSelectedReasonId(null))
      }}
      loading={isFetching}
    >
      <Form
        form={form}
        labelCol={{ flex: '120px' }}
        labelAlign="left"
        initialValues={{
          type: REASON_TYPE_REASON_DELAY,
        }}
        onFinish={onSubmit}
      >
        <Form.Item label="" name="type">
          <Radio.Group>
            <Radio
              value={REASON_TYPE_REASON_DELAY}
              className="font-bold !text-xl !text-primary uppercase"
            >
              Delay
            </Radio>
            <Radio
              value={REASON_TYPE_REASON_CANCEL}
              className="font-bold !text-xl !text-primary uppercase"
            >
              Cancel
            </Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item label="Delay time" rules={[{ required: true }]}>
          <div className="flex items-center gap-x-1">
            <Form.Item
              name="hour"
              noStyle
              rules={[
                { required: true, message: 'Hour is required' },
                {
                  type: 'number',
                  min: 0,
                  message: 'Hour must be > 0',
                },
              ]}
            >
              <InputNumber className="!w-20 text-center" placeholder="HH" />
            </Form.Item>
            <span>:</span>
            <Form.Item
              name="minutes"
              noStyle
              rules={[
                { required: true, message: 'Minute is required' },
                {
                  type: 'number',
                  min: 0,
                  max: 59,
                  message: 'Minute must be 0–59',
                },
              ]}
            >
              <InputNumber className="!w-20 text-center" placeholder="MM" />
            </Form.Item>
          </div>
        </Form.Item>
        <Form.Item
          label="Reason"
          name="reasons"
          rules={[{ required: true, message: 'Please select reason' }]}
        >
          <TreeSelect
            treeData={reasonsType?.map((item: any) => ({
              title: `${item.description}`,
              value: item.code,
              selectable: false,
              children: item.children.map((child: any) => ({
                title: `${child.description}`,
                value: child.code,
                selectable: true,
              })),
            }))}
            treeDefaultExpandAll
            placeholder="Select reason"
          />
        </Form.Item>
        <div className="flex items-center gap-x-4">
          <Form.Item
            label="Service Dept"
            className="w-full"
            name="serviceDept"
            rules={[{ required: true, message: 'Please select service dept' }]}
          >
            <Select
              options={serviceDept?.map((item: any) => ({
                value: item.value,
                label: `${item.name} - ${item.displayName}`,
              }))}
            />
          </Form.Item>
          <Form.Item
            label="Attribute"
            className="w-full"
            name="attribute"
            labelCol={{ flex: '120px' }}
          >
            <Select
              defaultValue={0}
              options={attribute?.map((item: any) => ({
                value: item.value,
                label: `${item.name} - ${item.displayName}`,
              }))}
            />
          </Form.Item>
        </div>
        <Form.Item label="Remark" name="remark">
          <Input.TextArea rows={2} />
        </Form.Item>
        <Form.Item className="justify-end flex">
          <Button htmlType="submit" type="primary" loading={mutation.isPending}>
            {t('common.save')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ModalReasonDetail
