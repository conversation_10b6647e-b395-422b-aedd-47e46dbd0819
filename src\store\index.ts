import { configureStore } from '@reduxjs/toolkit'
import GlobalSlice from '../store/GlobalSlice'
import AirportSlice from './AirportSlice'
import ArrivalSlice from './ArrivalSlice'
import AuthSlice from './AuthSlice'
import DashboardSlice from './DashboardSlice'
import DepartureSlice from './DepartureSlice'
import FleetSlice from './FleetSlice'
import FlightSlice from './FlightSlice'
import GeneralSlice from './GeneralSlice'
import GroupTaskPTSSlice from './GroupTaskPTSSlice'
import JobSlice from './JobSlice'
import ModalArrivalFlightSlice from './ModalArrivalFlightSlice'
import ModalDepartureFlightSlice from './ModalDepartureFlightSlice'
import OrganizationUnitSlice from './OrganizationUnitSlice'
import PermissionSlice from './PermissionSlice'
import PTSSlice from './PTSSlice'
import ReportSlice from './ReportSlice'
import RoleSlice from './RoleSlice'
import SeasonalSlice from './SeasonalSlice'
import UserSlice from './UserSlice'

export const store = configureStore({
  reducer: {
    global: GlobalSlice,
    auth: AuthSlice,
    permission: PermissionSlice,
    flight: FlightSlice,
    job: JobSlice,
    pts: PTSSlice,
    departure: DepartureSlice,
    role: RoleSlice,
    user: UserSlice,
    organizationUnit: OrganizationUnitSlice,
    airport: AirportSlice,
    groupTaskPTS: GroupTaskPTSSlice,
    fleet: FleetSlice,
    dashboard: DashboardSlice,
    report: ReportSlice,
    modalDepartureFlight: ModalDepartureFlightSlice,
    modalArrivalFlight: ModalArrivalFlightSlice,
    general: GeneralSlice,
    arrival: ArrivalSlice,
    seasonal: SeasonalSlice,
  },
  devTools: import.meta.env.MODE !== 'production',
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
export type AppStore = typeof store
