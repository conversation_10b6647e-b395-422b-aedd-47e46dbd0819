/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import ModalPassenger from '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalPassenger/ModalPassenger'
import { getPaxInfo } from '@/src/service/pax-info'
import {
  openModalPassenger,
  setTypePax,
} from '@/src/store/ModalArrivalFlightSlice'
import { EyeOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { Col, Divider, Form, Input, Row } from 'antd'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

const mapPaxInfoToForm = (paxInfo: any) => ({
  preFlight: paxInfo.paxPreFlight,
  postFlight: paxInfo.paxPostFlight,
  remark: paxInfo.remark,
})

const mapFormToPaxInfo = (values: any) => ({
  paxPreFlight: values.preFlight,
  paxPostFlight: values.postFlight,
  remark: values.remark,
})

const TabModalPassenger = () => {
  const [form] = Form.useForm()
  const { selectedFlightId } = useAppSelector(state => state.arrival)

  const { t } = useTranslation()

  const { visibleModalArrivalFlightDetail } = useAppSelector(
    state => state.modalArrivalFlight
  )

  const { data: paxInfo } = useQuery({
    queryKey: ['pax-info-detail', selectedFlightId],
    queryFn: () => getPaxInfo(selectedFlightId as string),
    enabled: !!selectedFlightId && visibleModalArrivalFlightDetail,
  })

  const dispatch = useAppDispatch()

  useEffect(() => {
    if (paxInfo) {
      form.setFieldsValue(mapPaxInfoToForm(paxInfo))
    } else {
      form.resetFields()
    }
  }, [paxInfo, selectedFlightId])

  const onSubmit = async () => {
    await form.validateFields()
    const values = form.getFieldsValue()
    const payload = mapFormToPaxInfo(values)
    console.log('Submit payload:', payload)
    // mutation.mutate(payload)  // gọi API update nếu cần
  }

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      <div className="text-secondary flex justify-end">
        {t('common.noData')}
      </div>
      <ModalPassenger />
      <Form form={form} layout="vertical" onFinish={onSubmit}>
        <Row gutter={16}>
          <Col span={24}>
            <span className="text-base font-bold">Booking</span>
          </Col>
          <Col span={24}>
            <Divider className="bg-primary" />
          </Col>

          <Col span={8}>
            <Form.Item label="CAPC" name={['preFlight', 'capc']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="BKED" name={['preFlight', 'bked']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Infant" name={['preFlight', 'infant']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('Infant'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="VIP" name={['preFlight', 'vip']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('VIP'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="CIP" name={['preFlight', 'cip']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('CIP'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          {/* <Col span={8}>
            <Form.Item label="Passenger ID" name={['preFlight', 'passengerId']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('PassengerID'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col> */}

          <Col span={8}>
            <Form.Item label="PR" name={['preFlight', 'pr']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('PR'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Outbound" name={['preFlight', 'outbound']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('Outbound'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="FFP" name={['preFlight', 'ffp']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('FFP'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="Sp. Service"
              name={['preFlight', 'specialService']}
            >
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('SpecialService'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Special Meals"
              name={['preFlight', 'specialMeals']}
            >
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="MM" name={['preFlight', 'mm']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('MM'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Child" name={['preFlight', 'child']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('Child'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <span className="text-base font-bold">Actual</span>
          </Col>
          <Col span={24}>
            <Divider className="bg-primary" />
          </Col>

          <Col span={8}>
            <Form.Item label="CAPC" name={['postFlight', 'capc']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="OBRD" name={['postFlight', 'obrd']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Infant" name={['postFlight', 'infant']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('Infant'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="VIP" name={['postFlight', 'vip']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('VIP'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="CIP" name={['postFlight', 'cip']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('CIP'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="FFP" name={['postFlight', 'ffp']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('FFP'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="PR" name={['postFlight', 'pr']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('PR'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Outbound" name={['postFlight', 'outbound']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('Outbound'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          {/* <Col span={8}>
            <Form.Item
              label="Passenger ID"
              name={['postFlight', 'passengerId']}
            >
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('PassengerID'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col> */}

          <Col span={8}>
            <Form.Item
              label="Sp. Service"
              name={['postFlight', 'specialService']}
            >
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('SpecialService'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Special Meals"
              name={['postFlight', 'specialMeals']}
            >
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="MM" name={['postFlight', 'mm']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('MM'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Child" name={['postFlight', 'child']}>
              <Input
                readOnly
                suffix={
                  <EyeOutlined
                    onClick={() => {
                      dispatch(openModalPassenger())
                      dispatch(setTypePax('Child'))
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  )
}

export default TabModalPassenger
