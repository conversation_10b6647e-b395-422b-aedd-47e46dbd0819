import { EditFilled } from '@ant-design/icons'
import { Button, Table } from 'antd'
import styles from './index.module.scss'

const TabGD = () => {
  const columns = [
    { title: 'Order', width: '10%' },
    { title: 'Func.', width: '10%' },
    { title: 'Name', width: '20%' },
    { title: 'Information', width: '60%' },
  ]

  return (
    <div className="w-full py-3 bg-[#F5F9FA]">
      <div className="flex w-full justify-between mb-3">
        <div className="text-lg font-bold">GD</div>
        <Button
          className="!hidden"
          icon={<EditFilled />}
          type="primary"
          onClick={() => {}}
        >
          Chỉnh sửa
        </Button>
      </div>
      <Table bordered columns={columns} className={`${styles.whiteHeader}`} />
    </div>
  )
}

export default TabGD
