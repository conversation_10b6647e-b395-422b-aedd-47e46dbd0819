/* eslint-disable @typescript-eslint/no-explicit-any */
import { configLine } from '@/src/config/configChart'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getDashboardOrganizationUnitLineChart } from '@/src/service/dashboard'
import { setLimit } from '@/src/store/DashboardSlice'
import { Line } from '@ant-design/charts'
import { DownloadOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { Button, Card, Empty, Select } from 'antd'
import QueryString from 'qs'
import { useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import { useResizeObserver } from 'usehooks-ts'

const LineChartDashboard = () => {
  const { t } = useTranslation()

  const ref = useRef<HTMLDivElement>(null)

  const { params, limit } = useAppSelector(state => state.dashboard)

  const dispatch = useAppDispatch()

  const navigate = useNavigate()

  const { data: lineChartData, isLoading } = useQuery({
    queryKey: ['get-dashboard-organization-unit-line-chart', params, limit],
    queryFn: () =>
      getDashboardOrganizationUnitLineChart({
        ...params,
        limit,
      }),
  })

  const { width = 0 } = useResizeObserver({
    ref,
    box: 'border-box',
  })

  const config = configLine({
    data: lineChartData,
    width,
  })

  const totalValue = lineChartData
    ?.flatMap((item: any) => item.datas)
    .reduce((sum: any, d: any) => sum + d.value, 0)

  return (
    <Card className="flex flex-col" loading={isLoading}>
      <div className="flex justify-between items-center pb-4">
        <div className="flex gap-x-6 items-center">
          <span className="text-lg font-bold">
            {t('dashboard.listUnitLate')}
          </span>
          <Select
            defaultValue={limit}
            options={[
              {
                value: 3,
                label: 'Top 3',
              },
              {
                value: 5,
                label: 'Top 5',
              },
              {
                value: 0,
                label: t('common.all'),
              },
            ]}
            onChange={val => {
              dispatch(setLimit(val))
            }}
            className="w-24"
          />
        </div>
        {totalValue > 0 && (
          <div className="flex gap-x-2 items-center">
            <Button icon={<DownloadOutlined />} className="!hidden">
              {t('report.download')}
            </Button>
            <Button
              type="primary"
              className=""
              onClick={() => {
                navigate(
                  `/report/department-late?${QueryString.stringify({
                    ...params,
                    type: 1,
                  })}`
                )
              }}
            >
              {t('dashboard.view_detail')}
            </Button>
          </div>
        )}
      </div>
      {totalValue > 0 ? (
        <div ref={ref} className="flex flex-row gap-x-1">
          <Line {...config} className="custom-grid-chart" />
        </div>
      ) : (
        <Empty description="No data" />
      )}
    </Card>
  )
}

export default LineChartDashboard
