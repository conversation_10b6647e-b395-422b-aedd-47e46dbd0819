import { createSlice } from '@reduxjs/toolkit'

interface GroupTaskPTSState {
  visibleModal: boolean
  selectedId: string | null
  params: {
    SkipCount: number
    MaxResultCount: number
  }
}

const initialState: GroupTaskPTSState = {
  visibleModal: false,
  selectedId: null,
  params: {
    SkipCount: 0,
    MaxResultCount: 20,
  },
}

const groupTaskPTSSlice = createSlice({
  name: 'groupTaskPTS',
  initialState,
  reducers: {
    openGroupTaskPTSModal(state) {
      state.visibleModal = true
    },
    closeGroupTaskPTSModal(state) {
      state.visibleModal = false
    },
    setSelectedId(state, action) {
      state.selectedId = action.payload
    },
    setParams(state, action) {
      state.params = { ...state.params, ...action.payload }
    },
  },
})

export const {
  openGroupTaskPTSModal,
  closeGroupTaskPTSModal,
  setSelectedId,
  setParams,
} = groupTaskPTSSlice.actions
export default groupTaskPTSSlice.reducer
