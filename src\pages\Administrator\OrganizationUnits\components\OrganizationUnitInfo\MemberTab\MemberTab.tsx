/* eslint-disable react-hooks/exhaustive-deps */
import ShowTotal from '@/src/components/Showtotal'
import {
  LAO_DEPARTMENT_ASSIGN_USER,
  LAO_DEPARTMENT_REMOVE_USER,
} from '@/src/constants/permission'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import usePermission from '@/src/hooks/usePermission'
import {
  getUsersInDepartment,
  removeUserFromDepartment,
} from '@/src/service/organization_unit'
import { getRoleAll } from '@/src/service/role'
import {
  resetState,
  setCurrentMemberRole,
  setParams,
  setSelectedMember,
  setVisibleOrganizationUnit,
  toggleChangeRoleMemberModal,
  toggleOrganizationUnitMemberModal,
} from '@/src/store/OrganizationUnitSlice'
import { PlusOutlined } from '@ant-design/icons'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Button, Card, message, Popconfirm, Space, Table } from 'antd'
import type { TableProps } from 'antd/lib'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import AddMemberModal from './AddMemberModal'
import ChangeRoleModal from './ChangeRoleModal'
import styles from './index.module.scss'

interface Member {
  userId: string
  key: string
  userName: string
  creationTime: string
  roleId: string
}

const MemberTab = () => {
  const { t } = useTranslation()
  const { visibleSelectedId, params } = useAppSelector(
    state => state.organizationUnit
  )
  const [detetingItem, setDeletingItem] = useState<string>()
  const dispatch = useAppDispatch()
  const queryClient = useQueryClient()
  const { hasPermission } = usePermission()
  const { data: role } = useQuery({
    queryKey: ['roles'],
    queryFn: () => getRoleAll(),
  })

  const { data: users = [], isFetching: isLoading } = useQuery({
    queryKey: ['userDept', visibleSelectedId],
    queryFn: () => getUsersInDepartment(visibleSelectedId!),
    enabled: !!visibleSelectedId,
  })

  const { mutate: removeUser, isPending: isRemoving } = useMutation({
    mutationFn: (userId: string) => (
      setDeletingItem(userId),
      removeUserFromDepartment(visibleSelectedId!, userId)
    ),
    onSuccess: () => {
      message.success(t('organization.member.deleteSuccess'))
      queryClient.invalidateQueries({
        queryKey: ['userDept'],
      })
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const memberColumns: TableProps<Member>['columns'] = [
    {
      title: t('organization.member.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('organization.member.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('organization.member.role'),
      dataIndex: 'roleId',
      key: 'roleId',
      render: (value: string) => getRoleNameById(value),
      hidden: true,
    },
    {
      title: t('common.action'),
      key: 'action',
      align: 'center',
      width: 100,
      hidden: !hasPermission(LAO_DEPARTMENT_REMOVE_USER),
      render: (_, record) => (
        <Space>
          <Button
            type="default"
            className="!hidden"
            onClick={() => {
              dispatch(setCurrentMemberRole(record.roleId))
              dispatch(setSelectedMember(record.userId))
              dispatch(toggleChangeRoleMemberModal())
            }}
          >
            {t('organization.member.roleEdit')}
          </Button>
          <Popconfirm
            title={t('organization.member.delete')}
            onConfirm={() => {
              removeUser(record.userId)
            }}
          >
            <Button
              loading={isRemoving && record.userId == detetingItem}
              type="primary"
            >
              {t('common.delete')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const getRoleNameById = (id: string) => {
    return role?.items.find(
      (item: { id: string; name: string }) => item.id == id
    )?.name
  }

  useEffect(() => {
    dispatch(setVisibleOrganizationUnit(null))
  }, [])

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <Card
      title={t('organization.info')}
      styles={{
        header: {
          borderBottom: 'none',
        },
        body: {
          paddingTop: 0,
        },
      }}
      extra={
        hasPermission(LAO_DEPARTMENT_ASSIGN_USER) && (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              dispatch(toggleOrganizationUnitMemberModal())
            }}
            className={`${visibleSelectedId ? '' : '!hidden'}`}
          >
            {t('organization.member.add')}
          </Button>
        )
      }
    >
      <Table
        rowKey={'id'}
        columns={memberColumns}
        dataSource={users}
        loading={isLoading}
        pagination={{
          pageSizeOptions: [10, 20, 50, 100, 1000],
          total: users.length,
          current: params?.skipCount / params?.maxResultCount + 1,
          pageSize: params?.maxResultCount || 20,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                ...params,
                skipCount: (page - 1) * pageSize,
                maxResultCount: pageSize,
              })
            )
          },
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
          showSizeChanger: true,
        }}
        size="small"
        className={`${styles.whiteHeader}`}
        bordered
      />
      <AddMemberModal users={users} />
      <ChangeRoleModal />
    </Card>
  )
}

export default MemberTab
