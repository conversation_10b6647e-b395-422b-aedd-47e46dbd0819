/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/flights`

const getReasonList = async (flightId: string) => {
  const api = `${prefix}/${flightId}/reasons`
  const response = await axios.get(api)
  return response.data
}

const createReason = async (flightId: string, body: any) => {
  const api = `${prefix}/${flightId}/reasons`
  const response = await axios.post(api, body)
  return response.data
}

const updateReason = async (flightId: string, body: any) => {
  const api = `${prefix}/${flightId}/reasons`
  const response = await axios.put(api, body)
  return response.data
}

const getReasonDetail = async (flightId: string, id: string) => {
  const api = `${prefix}/${flightId}/reasons/${id}`
  const response = await axios.get(api)
  return response.data
}

export { createReason, getReasonDetail, getReasonList, updateReason }
