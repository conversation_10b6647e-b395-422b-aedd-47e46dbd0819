/* eslint-disable @typescript-eslint/no-explicit-any */

import { ACCESS_TOKEN, AIRPORT_GLOBAL, REFRESH_TOKEN } from '@/src/constants'
import { createSlice } from '@reduxjs/toolkit'
import cookie from 'cookiejs'

interface AuthState {
  user: {
    permission: string[]
  } | null
  isLoading: boolean
  error: any
  account: {
    email: string
    pass: string
  }
}

const initialState: AuthState = {
  user: null,
  isLoading: false,
  error: null,
  account: {
    email: '',
    pass: '',
  },
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCurrentUser: (state, action) => {
      state.user = action.payload
    },
    signOut: state => {
      state.user = null
      cookie.remove(ACCESS_TOKEN)
      cookie.remove(REFRESH_TOKEN)
      sessionStorage.removeItem(ACCESS_TOKEN)
      sessionStorage.removeItem(REFRESH_TOKEN)
      localStorage.removeItem(AIRPORT_GLOBAL)
    },
    setAccount: (state, action) => {
      state.account = action.payload
    },
  },
})

export const { signOut, setAccount, setCurrentUser } = authSlice.actions
export default authSlice.reducer
