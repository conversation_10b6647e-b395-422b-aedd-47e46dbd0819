/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/flights`

const getBriefingList = async (flightId: string) => {
  const api = `${prefix}/${flightId}/briefing`
  const response = await axios.get(api)
  return response.data
}

const createBriefing = async (body: any) => {
  const api = `${prefix}/${body.flightId}/briefing`
  const response = await axios.post(api, body)
  return response.data
}

const updateBriefing = async (body: any) => {
  const api = `${prefix}/${body.flightId}/briefing/${body.briefingId}`
  const response = await axios.put(api, body)
  return response.data
}

const getBriefingDetail = async (flightId: string, id: string) => {
  const api = `${prefix}/${flightId}/briefing/${id}`
  const response = await axios.get(api)
  return response.data
}

const createBreifingReply = async (body: any) => {
  const api = `${prefix}/${body.flightId}/briefing/${body.briefingId}/reply`
  const response = await axios.post(api, body)
  return response.data
}

const approveBriefing = async (body: any) => {
  const api = `${prefix}/${body.flightId}/briefing/${body.briefingId}/approve`
  const response = await axios.post(api, body)
  return response.data
}

export {
  createBriefing,
  getBriefingDetail,
  getBriefingList,
  updateBriefing,
  createBreifingReply,
  approveBriefing,
}
