/* eslint-disable @typescript-eslint/no-explicit-any */
import type { LineConfig, PieConfig } from '@ant-design/charts'

const configPie = (pieConfig: Omit<PieConfig, 'angleField'>) => {
  const total = pieConfig?.data.reduce(
    (sum: any, item: any) => sum + item.value,
    0
  )

  const config = {
    data: pieConfig?.data,
    scale: {
      color: {
        type: 'identity',
      },
    },
    angleField: 'value',
    colorField: 'color',
    innerRadius: 0.7,
    label: {
      text: 'value',
      style: {
        fontWeight: 'bold',
      },
    },
    legend: false,
    style: {
      padding: 10,
    },
    tooltip: {
      title: (d: { value: number }) => ({
        value: `<span class="font-bold text-black">${d.value}/${total}</span>`,
      }),
      items: false,
    },
    width: 100,
    ...pieConfig,
  }
  return config
}

const configLine = (lineConfig: LineConfig) => {
  const data = lineConfig?.data?.flatMap((item: any) =>
    item.datas.map((d: any) => ({
      label: item.label,
      value: d.value,
      series: d.series,
    }))
  )

  const config = {
    xField: 'label',
    yField: 'value',
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    legend: {
      color: {
        position: 'right',
        shape: '',
      },
      marker: {
        symbol: 'circle',
        style: {
          r: 4,
        },
      },
    },
    point: {
      size: 10,
      shape: 'circle',
    },
    seriesField: 'series',
    colorField: 'series',
    smooth: true,
    scale: {
      color: {
        range: [
          '#FF4D4F',
          '#1890FF',
          '#FAAD14',
          '#722ED1',
          '#A0D911',
          '#13C2C2',
          '#EB2F96',
          '#2F54EB',
          '#FA541C',
        ],
      },
    },
    autoFit: true,
    ...lineConfig,
    data,
  }
  return config
}

export { configPie, configLine }
