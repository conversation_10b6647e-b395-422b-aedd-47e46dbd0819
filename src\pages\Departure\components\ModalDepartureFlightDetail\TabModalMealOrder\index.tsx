/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getMealOrder, updateMealOrder } from '@/src/service/meal-order'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Divider, Form, Input, message } from 'antd'
import dayjs from 'dayjs'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

const TabModalMealOrder = () => {
  const { selectedFlightId } = useAppSelector(state => state.departure)

  const { visibleModalDepartureFlightDetail } = useAppSelector(
    state => state.modalDepartureFlight
  )

  const { t } = useTranslation()

  const [form] = Form.useForm()

  const { data, refetch } = useQuery({
    queryKey: ['meal-order', selectedFlightId],
    queryFn: () => getMealOrder(selectedFlightId as string),
    enabled: !!selectedFlightId && visibleModalDepartureFlightDetail,
  })

  const mutation = useMutation({
    mutationFn: (values: any) => {
      return updateMealOrder(selectedFlightId as string, values)
    },
    onSuccess() {
      refetch()
      message.success('Meal order updated successfully!')
    },
    onError(error) {
      handleApiError(error)
    },
  })

  const onSubmit = async () => {
    await form.validateFields()
    const values = form.getFieldsValue()

    const payload = {
      id: data?.id,
      remark: values.remark,
    }

    await mutation.mutateAsync(payload)
  }

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        mealJ: data.actMealJ,
        mealW: data.actMealW,
        mealY: data.actMealY,
        mealF: data.actMealF,
        ActCockpitCrew: data.actCoCrewMeal,
        ActCabinCrew: data.actCcCrewMeal,
        specialMeals: data.actSpecialMeals,
        paidMeal: data.paidMeal,
        remark: data.remark,
      })
    } else {
      form.resetFields()
    }
  }, [data, selectedFlightId])

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      <div className="text-secondary flex justify-end">
        {t('common.noData')}
      </div>
      <Form form={form} layout="vertical" onFinish={onSubmit}>
        <div>Actual Meal (J.W.Y.F)</div>
        <div className="flex gap-x-4">
          <div className="flex gap-x-1">
            <Form.Item name="mealJ">
              <Input readOnly />
            </Form.Item>
            <span className="mt-1">J</span>
          </div>
          <div className="flex gap-x-1">
            <Form.Item name="mealW">
              <Input readOnly />
            </Form.Item>
            <span className="mt-1">W</span>
          </div>
          <div className="flex gap-x-1">
            <Form.Item name="mealY">
              <Input readOnly />
            </Form.Item>
            <span className="mt-1">Y</span>
          </div>
          <div className="flex gap-x-1">
            <Form.Item name="mealF">
              <Input readOnly />
            </Form.Item>
            <span className="mt-1">F</span>
          </div>
        </div>
        <div>Actual Crew Meal</div>
        <div className="flex gap-x-4">
          <div className="flex gap-x-1">
            <Form.Item name="ActCockpitCrew">
              <Input readOnly />
            </Form.Item>
            <span className="mt-1">Cockpit Crew</span>
          </div>
          <div className="flex gap-x-1">
            <Form.Item name="ActCabinCrew">
              <Input readOnly />
            </Form.Item>
            <span className="mt-1">Cabin Crew</span>
          </div>
        </div>
        <Form.Item name="specialMeals" label="Act special meals">
          <Input.TextArea rows={3} readOnly />
        </Form.Item>
        <Form.Item name="paidMeal" label="Paid meal">
          <Input.TextArea rows={3} readOnly />
        </Form.Item>
        <div className="flex justify-between mb-1">
          <div>Remark</div>
          <div className="text-slate-400 italic">
            Last updated by {data?.lastModifierName} at&nbsp;
            {dayjs(data?.lastModificationTime).format('HH:mm DD/MM/YYYY')}
          </div>
        </div>
        <Form.Item name="remark" label="">
          <Input.TextArea rows={3} />
        </Form.Item>
        <Divider className="bg-black" />
        <div className="flex gap-x-2">
          <Form.Item className="flex justify-end">
            <Button type="primary" htmlType="submit">
              Save
            </Button>
          </Form.Item>
        </div>
      </Form>
    </div>
  )
}

export default TabModalMealOrder
