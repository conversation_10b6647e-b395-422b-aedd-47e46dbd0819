import i18next from 'i18next'
import { initReactI18next } from 'react-i18next'
import translationVI from '@/src/locales/vi.json'
import translationEN from '@/src/locales/en.json'

const resources = {
  vi: { translation: translationVI },
  en: { translation: translationEN },
}

i18next.use(initReactI18next).init({
  // lng: localStorage.getItem('app_locale') || 'en',
  lng: 'en',
  debug: import.meta.env.VITE_DEBUG === 'true',
  resources,
})

export default i18next
