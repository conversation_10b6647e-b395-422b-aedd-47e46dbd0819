/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Result } from 'antd'
import React from 'react'
import { useLocation } from 'react-router'

interface ErrorBoundaryState {
  hasError: boolean
}

class ErrorBoundary extends React.Component<any, ErrorBoundaryState> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }
  static getDerivedStateFromError() {
    return { hasError: true }
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.log(error)
    console.log(errorInfo)
  }

  componentDidUpdate(prevProps: any) {
    if (this.props.routeKey !== prevProps.routeKey) {
      this.setState({ hasError: false })
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <Result
          status="500"
          title="500"
          subTitle="Sorry, something went wrong."
          extra={
            <Button
              type="primary"
              onClick={() => {
                window.location.href = '/'
              }}
            >
              Back to Home
            </Button>
          }
        />
      )
    }
    return this.props.children
  }
}

const ErrorBoundaryWrapper = ({ children }: { children: React.ReactNode }) => {
  const location = useLocation()

  return <ErrorBoundary routeKey={location.pathname}>{children}</ErrorBoundary>
}

export default ErrorBoundaryWrapper
