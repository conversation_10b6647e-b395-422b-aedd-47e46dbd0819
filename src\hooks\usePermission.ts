import { useSelector } from 'react-redux'
import type { RootState } from '@/src/store'

const usePermission = () => {
  const { user } = useSelector((state: RootState) => state.auth)

  /**
   * Kiểm tra user có quyền cụ thể không
   */
  // const hasPermission = (permissionName: string): boolean => {
  //   if (!user || !user.permission) {
  //     return false
  //   }

  //   return user.permission.includes(permissionName)
  // }

  /**
   * Kiểm tra user có ít nhất một quyền trong danh sách không
   */
  // const hasAnyPermission = (permissionNames: string[]): boolean => {
  //   if (!user || !user.permission) {
  //     return false
  //   }

  //   return permissionNames.some(permission =>
  //     user.permission.includes(permission)
  //   )
  // }

  const hasPermission = (permission: string | string[]): boolean => {
    if (!user || !user.permission) return false

    if (typeof permission === 'string') {
      return user.permission.includes(permission)
    }

    return permission.some(p => user.permission.includes(p))
  }

  /**
   * Kiểm tra user có tất cả quyền trong danh sách không
   */
  const hasAllPermissions = (permissionNames: string[]): boolean => {
    if (!user || !user.permission) {
      return false
    }

    return permissionNames.every(permission =>
      user.permission.includes(permission)
    )
  }

  return {
    hasPermission,
    hasAllPermissions,
    userPermissions: user?.permission || [],
  }
}

export default usePermission
