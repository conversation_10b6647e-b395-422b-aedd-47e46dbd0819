/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

type JobState = {
  visibleCreateJobModal: boolean
  selectedJobId: string | null
  params: {
    SkipCount: number
    MaxResultCount: number
    KeyWord?: string
    Status?: any
  }
  visibleDeleteModal: boolean
  visibleFilterModal: boolean
  KeyWord?: string
}

const initialState: JobState = {
  visibleCreateJobModal: false,
  selectedJobId: null,
  params: {
    SkipCount: 0,
    MaxResultCount: 20,
  },
  visibleDeleteModal: false,
  visibleFilterModal: false,
}

const jobSlice = createSlice({
  name: 'job',
  initialState,
  reducers: {
    openCreateJobModal(state) {
      state.visibleCreateJobModal = true
    },
    closeCreateJobModal(state) {
      state.visibleCreateJobModal = false
    },
    selectJob(state, action: PayloadAction<string>) {
      state.selectedJobId = action.payload
    },

    openDeleteModal(state) {
      state.visibleDeleteModal = true
    },
    closeDeleteModal(state) {
      state.visibleDeleteModal = false
    },

    openFilterModal(state) {
      state.visibleFilterModal = true
    },
    closeFilterModal(state) {
      state.visibleFilterModal = false
    },
    setParams(state, action: PayloadAction<any>) {
      state.params = { ...state.params, ...action.payload }
    },
    setSearchDebounce(state, action: PayloadAction<string>) {
      state.KeyWord = action.payload
    },
    resetState() {
      return initialState
    },
  },
})

export const {
  openCreateJobModal,
  closeCreateJobModal,
  selectJob,
  setParams,
  openDeleteModal,
  closeDeleteModal,
  openFilterModal,
  closeFilterModal,
  setSearchDebounce,
  resetState,
} = jobSlice.actions
export default jobSlice.reducer
