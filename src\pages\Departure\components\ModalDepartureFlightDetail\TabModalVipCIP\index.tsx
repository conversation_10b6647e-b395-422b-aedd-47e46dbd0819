/* eslint-disable @typescript-eslint/no-explicit-any */
import { Table, type TableProps } from 'antd'
import styles from './index.module.scss'

const TabModalVipCIP = () => {
  const columns: TableProps<any>['columns'] = [
    {
      title: 'Full Name',
      width: '30%',
      dataIndex: 'fullName',
    },
    { title: 'Booking Class.', width: '30%', dataIndex: 'bookingClass' },
    { title: 'Note', width: '40%', dataIndex: 'note' },
  ]

  return (
    <Table
      size="small"
      bordered
      columns={columns}
      className={`${styles.whiteHeader}`}
      dataSource={[]}
    />
  )
}

export default TabModalVipCIP
