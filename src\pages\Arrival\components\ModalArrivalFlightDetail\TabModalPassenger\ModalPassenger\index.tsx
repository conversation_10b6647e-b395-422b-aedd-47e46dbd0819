/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getPassenger } from '@/src/service/pax-info'
import { useQuery } from '@tanstack/react-query'
import { Modal, Table } from 'antd'
import type { TableProps } from 'antd/lib'
import styles from './index.module.scss'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import {
  closeModalPassenger,
  setParamsPassenger,
} from '@/src/store/ModalArrivalFlightSlice'
import moment from 'moment'
import ShowTotal from '@/src/components/Showtotal'

const ModalPassenger = () => {
  const { selectedFlightId } = useAppSelector(state => state.arrival)

  const { paramsPassenger, visibleModalPassenger, typePax } = useAppSelector(
    state => state.modalArrivalFlight
  )

  const dispatch = useAppDispatch()

  const { data } = useQuery({
    queryKey: ['passenger', selectedFlightId, paramsPassenger],
    queryFn: () =>
      getPassenger(selectedFlightId as string, {
        ...paramsPassenger,
        typePax,
      }),
    enabled: !!visibleModalPassenger,
  })

  const columns: TableProps<any>['columns'] = [
    {
      title: 'Order',
      render: (_, __, index) => paramsPassenger.skipCount + index + 1,
      align: 'center',
    },
    { title: 'Surname', dataIndex: 'surname' },
    { title: 'Given name', dataIndex: 'givenName' },
    {
      title: 'Gender',
      dataIndex: 'gender',
      render: (gender: any) => <>{gender === 0 ? 'Male' : 'Female'}</>,
    },
    { title: 'Passport', dataIndex: 'passport', align: 'center' },
    {
      title: 'Nationality',
      dataIndex: 'nationality',
      align: 'center',
    },
    {
      title: 'DOB',
      dataIndex: 'dob',
      align: 'center',
      render: (dob: any) => <>{moment(dob).format('DD/MM/YYYY')}</>,
    },
  ]

  return (
    <Modal
      open={visibleModalPassenger}
      title={`${typePax} Passenger`}
      width={1200}
      footer={null}
      onCancel={() => {
        dispatch(closeModalPassenger())
      }}
    >
      <Table
        columns={columns}
        dataSource={data?.items || []}
        size="small"
        bordered
        className={styles.whiteHeader}
        rowKey={record => record.id}
        pagination={{
          total: data?.totalCount,
          current:
            paramsPassenger.skipCount / paramsPassenger.maxResultCount + 1,
          pageSize: paramsPassenger.maxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParamsPassenger({
                ...paramsPassenger,
                skipCount: (page - 1) * pageSize,
                maxResultCount: pageSize,
              })
            )
          },
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
    </Modal>
  )
}

export default ModalPassenger
