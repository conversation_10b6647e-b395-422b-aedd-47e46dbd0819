/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from 'react'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getPaxInfo, updatePaxInfo } from '@/src/service/pax-info'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Col, Divider, Form, Input, Row } from 'antd'
import { useTranslation } from 'react-i18next'
import { handleApiError } from '@/src/helper/handleApiError'

const TabModalPassenger = () => {
  const [form] = Form.useForm()
  const { t } = useTranslation()
  const { selectedFlightId } = useAppSelector(state => state.departure)

  const { visibleModalDepartureFlightDetail } = useAppSelector(
    state => state.modalDepartureFlight
  )

  const { data: paxInfo, refetch } = useQuery({
    queryKey: ['pax-info-detail-departure', selectedFlightId],
    queryFn: () => getPaxInfo(selectedFlightId as string),
    enabled: !!visibleModalDepartureFlightDetail,
  })

  const mutation = useMutation({
    mutationFn: (values: any) => {
      return updatePaxInfo(selectedFlightId as string, values)
    },
    onSuccess() {
      refetch()
    },
    onError(error) {
      handleApiError(error)
    },
  })

  const onSubmit = async () => {
    await form.validateFields()
    const values = form.getFieldsValue()

    const payload = {
      paxPreFlight: values.preFlight,
      paxPostFlight: values.postFlight,
      id: paxInfo?.id,
      remark: values.newRemark || values.remark,
    }

    mutation.mutate(payload)
    form.setFieldValue('newRemark', '')
  }

  useEffect(() => {
    if (paxInfo) {
      form.setFieldsValue({
        preFlight: paxInfo.paxPreFlight,
        postFlight: paxInfo.paxPostFlight,
        remark: paxInfo.remark,
      })
    } else {
      form.resetFields()
    }
  }, [paxInfo, selectedFlightId])

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      <div className="text-secondary flex justify-end">
        {t('common.noData')}
      </div>
      <Form form={form} layout="vertical" onFinish={onSubmit}>
        <Row gutter={16}>
          <Col span={24}>
            <span className="text-base font-bold">Pre Flight</span>
          </Col>
          <Col span={24}>
            <Divider className="bg-primary" />
          </Col>

          <Col span={8}>
            <Form.Item label="CAPC" name={['preFlight', 'capc']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="BKED" name={['preFlight', 'bked']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Infant" name={['preFlight', 'infant']}>
              <Input readOnly />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="VIP" name={['preFlight', 'vip']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="CIP" name={['preFlight', 'cip']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="FFP" name={['preFlight', 'ffp']}>
              <Input readOnly />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="PR" name={['preFlight', 'pr']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Outbound" name={['preFlight', 'outbound']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Inbound" name={['preFlight', 'inbound']}>
              <Input readOnly />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="Sp. Service"
              name={['preFlight', 'specialService']}
            >
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Child" name={['preFlight', 'child']}>
              <Input readOnly />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={24}>
            <Form.Item
              label="Special meals"
              name={['preFlight', 'specialMeals']}
            >
              <Input.TextArea rows={2} readOnly />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <span className="text-base font-bold">Post Flight</span>
          </Col>
          <Col span={24}>
            <Divider className="bg-primary" />
          </Col>

          <Col span={8}>
            <Form.Item label="CAPC" name={['postFlight', 'capc']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="OBRD" name={['postFlight', 'obrd']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="CK_INF" name={['postFlight', 'cK_INF']}>
              <Input readOnly />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="CK_TRN" name={['postFlight', 'cK_TRN']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="VOLDN" name={['postFlight', 'voldn']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="MISCT" name={['postFlight', 'misct']}>
              <Input readOnly />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="GOSHO" name={['postFlight', 'gosho']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="IVLUP" name={['postFlight', 'ivlup']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="IVLDN" name={['postFlight', 'ivldn']}>
              <Input readOnly />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="NOSHO" name={['postFlight', 'nosho']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="NOREC" name={['postFlight', 'norec']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="VOLUP" name={['postFlight', 'volup']}>
              <Input readOnly />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="TSFRD" name={['postFlight', 'tsfrd']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="WEBCKIN" name={['postFlight', 'webckin']}>
              <Input readOnly />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="OFFLOAD" name={['postFlight', 'offload']}>
              <Input readOnly />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="Remark" name="remark">
              <Input.TextArea rows={2} readOnly />
            </Form.Item>
          </Col>

          <Divider />
          <Col span={24}>
            <div className="w-full flex gap-x-2 items-center">
              <Form.Item
                name="newRemark"
                className="w-full"
                rules={[{ required: true, message: 'Remark is required' }]}
              >
                <Input.TextArea rows={2} />
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit">
                  {t('common.save')}
                </Button>
              </Form.Item>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  )
}

export default TabModalPassenger
