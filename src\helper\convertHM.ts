const toHourMinute = (time?: string): string => {
  if (!time) return '--:--'

  let days = 0
  let hms = time

  // Trường hợp có ng<PERSON>y: "1.06:10:00"
  if (time.includes('.')) {
    const [d, rest] = time.split('.')
    days = Number(d) || 0
    hms = rest ?? ''
  }

  const parts = hms.split(':')
  if (parts.length < 2) return '--:--'

  const [h, m] = parts.map(Number)
  const totalHours = (h ?? 0) + days * 24

  return `${String(totalHours).padStart(2, '0')}:${String(m ?? 0).padStart(2, '0')}`
}

export default toHourMinute
