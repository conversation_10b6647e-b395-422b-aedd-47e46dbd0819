/* eslint-disable @typescript-eslint/no-explicit-any */
export interface IPTSMaster {
  id: string
  code: string
  name: string
  fleetId: string
  fleetCode: string
  airportId: string
  airportName: string
  network: string
  groundTime: number
  fromDate: string
  toDate: string
  isDeleted: boolean
  deleterId: any
  deletionTime: any
  lastModificationTime: any
  lastModifierId: any
  creationTime: string
  creatorId: string
  bhFrom: any
  bhTo: any
  actualStart: any
  estimateStart: any
}

export interface IPtsDetailDto {
  ptsTaskId: string
  completeTime: number
  organizationId: string
  groupPtsTaskId: string
  groupPtsTaskName?: string
  status: boolean
  executeOUIds: string[]
}

export interface IPTSFlightResponse {
  ptsTasks: IPTSMaster[]
  ptsCode: string
  assignmentTime: string
  ptsId: string
}
