/* eslint-disable @typescript-eslint/no-explicit-any */
export interface IFlight {
  dataSource: string
  legNo: number
  acRegistration: string
  acLogicalNo: number
  fnCarrier: string
  fnNumber: string
  fnSufix: string
  seatsF: number
  seatsC: number
  seatsY: number
  cap: number
  flightMin: number
  flightHrs: number
  dayOfOrigin: string
  day: number
  acOwner: string
  acSubType: string
  acVersion: string
  depApSched: string
  arrApSched: string
  depApActual: string
  arrApActual: string
  depSchedDt: string
  arrSchedDt: string
  depDt: string
  arrDt: string
  legState: string
  legType: string
  fltType: string
  network: string
  fleetType: string
  ptsId: string
  lastModificationTime: string
  lastModifierId: string
  creationTime: string
  creatorId: any
  id: string
}
