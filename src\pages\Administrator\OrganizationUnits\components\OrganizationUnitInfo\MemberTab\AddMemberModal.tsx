/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import ShowTotal from '@/src/components/Showtotal'
import { TIME_DEBOUNCE } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import { assignUserToDepartment } from '@/src/service/organization_unit'
import { getRoleAll } from '@/src/service/role'
import { getUser } from '@/src/service/user'
import {
  resetParamsAndSearchText,
  resetState,
  setParamMember,
  setParams,
  setSearchText,
  setSelectRowKeys,
  toggleOrganizationUnitMemberModal,
} from '@/src/store/OrganizationUnitSlice'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { message, Modal, Select, Table } from 'antd'
import Search from 'antd/es/input/Search'
import type { TableProps } from 'antd/lib'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'

interface AddMemberModalProps {
  users: any[]
}
interface Member {
  id: string
  name: string
  email: string
}

const AddMemberModal = ({ users }: AddMemberModalProps) => {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const dispatch = useAppDispatch()

  const {
    organizationUnitMembers,
    visibleSelectedId,
    searchText,
    selectRowKeys,
    paramsMember,
  } = useAppSelector(state => state.organizationUnit)

  const searchTextDebounce = useDebounce(searchText, TIME_DEBOUNCE)

  const queryUsers = useQuery({
    queryKey: ['users', paramsMember],
    queryFn: () => getUser(paramsMember),
    enabled: organizationUnitMembers.visibleAddModal,
  })

  const { data: role, isFetching: isRoleFetching } = useQuery({
    queryKey: ['roles'],
    queryFn: () => getRoleAll(),
  })

  const { mutate: assignUserMutation, isPending: isPending } = useMutation({
    mutationFn: ({ departmentId, body }: { departmentId: string; body: any }) =>
      assignUserToDepartment(departmentId, body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['userDept'],
      })
      message.success(t('organization.member.createSuccess'))
      toggleModal()
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const handleOk = () => {
    if (!visibleSelectedId) return
    assignUserMutation({
      departmentId: String(visibleSelectedId),
      body: selectRowKeys.map((item: any) => ({
        userId: item,
        roleId: '00000000-0000-0000-0000-000000000000',
      })),
    })
  }

  const rowSelection: TableProps<any>['rowSelection'] = {
    selectedRowKeys: selectRowKeys,
    onSelect: (record, selected) => {
      const key = record.id
      const newKeys = selected
        ? [...selectRowKeys, key]
        : selectRowKeys.filter(k => k !== key)

      dispatch(setSelectRowKeys(newKeys))
    },
    onSelectAll: (selected, _selectedRows, changeRows) => {
      const changeKeys = changeRows.map(row => row.id)
      const newKeys = selected
        ? Array.from(new Set([...selectRowKeys, ...changeKeys]))
        : selectRowKeys.filter(k => !changeKeys.includes(k))

      dispatch(setSelectRowKeys(newKeys))
    },
    getCheckboxProps(record) {
      return {
        disabled: users.some(member => member.userId === record.id),
      }
    },
  }

  const toggleModal = () => {
    dispatch(setParams({}))
    dispatch(setSelectRowKeys([]))
    dispatch(resetParamsAndSearchText())
    dispatch(toggleOrganizationUnitMemberModal())
  }

  const memberColumns: TableProps<Member>['columns'] = [
    {
      title: t('organization.member.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('organization.member.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('organization.member.role'),
      dataIndex: 'role',
      key: 'role',
      hidden: true,
      align: 'center',
      render: (_, record) => (
        <Select
          disabled
          defaultValue={
            users.find(user => user.userId === record.id)?.roleId ||
            role?.[0]?.id
          }
          style={{ width: 150 }}
          loading={isRoleFetching}
          onSelect={value => {
            const updatedRowKeys = selectRowKeys.map((item: any) =>
              item.key === record.id ? { ...item, id: value } : item
            )
            dispatch(setSelectRowKeys(updatedRowKeys))
          }}
        >
          {role &&
            role.items.map((role: { id: string; name: string }) => (
              <Select.Option key={role.id} value={role.id}>
                {role.name}
              </Select.Option>
            ))}
        </Select>
      ),
    },
  ]

  useEffect(() => {
    dispatch(
      setParamMember({
        ...paramsMember,
        maxResultCount: 10,
        skipCount: 0,
        filter: searchTextDebounce,
      })
    )
  }, [searchTextDebounce])

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <Modal
      open={
        organizationUnitMembers.visibleAddModal && visibleSelectedId != null
      }
      title={t('organization.member.add')}
      okText={t('common.save')}
      cancelText={t('common.cancel')}
      onCancel={toggleModal}
      onOk={handleOk}
      confirmLoading={isPending}
      destroyOnHidden={true}
      width={1000}
    >
      <Search
        size="large"
        style={{ paddingBottom: 10 }}
        value={searchText}
        onChange={e => {
          dispatch(setSearchText(e.target.value))
        }}
      />
      <Table
        rowSelection={rowSelection}
        size="small"
        bordered
        rowKey={'id'}
        columns={memberColumns}
        className={`${styles.whiteHeader}`}
        dataSource={queryUsers.data?.items || []}
        loading={queryUsers.isFetching}
        footer={() => (
          <div className="flex justify-end items-center gap-x-4">
            <div className="text-sm text-gray-600 text-right">
              {t('common.selected')}: {selectRowKeys.length}
            </div>
          </div>
        )}
        pagination={{
          total: queryUsers.data?.totalCount || 0,
          pageSize: paramsMember?.maxResultCount || 20,
          onChange: (page, pageSize) => {
            dispatch(
              setParamMember({
                ...paramsMember,
                skipCount: (page - 1) * pageSize,
                maxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
    </Modal>
  )
}

export default AddMemberModal
