/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  CloseOutlined,
  MenuOutlined,
  MergeCellsOutlined,
} from '@ant-design/icons'
import { Button, Dropdown, Form, Switch } from 'antd'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

interface Props {
  columns: any[]
  onChangeColumn: (values: any) => void
  onOk: () => void
  onCancel: () => void
  open: boolean
}

const DropdownChangeColumn = (props: Props) => {
  const { columns, onChangeColumn, onOk, onCancel, open } = props

  const { t } = useTranslation()

  const [form] = Form.useForm()

  const onSubmitViewColumn = () => {
    const values = form.getFieldsValue()
    const newColumns = Object.keys(values).filter(key => values[key])
    onChangeColumn(newColumns)
  }

  useEffect(() => {}, [columns])

  return (
    <Dropdown
      popupRender={() => (
        <div className="bg-white rounded-sm p-4 shadow-bottom w-max">
          <div className="w-full justify-between flex flex-row items-center">
            <div className="text-base font-bold">{t('common.viewColumn')}</div>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => {
                onCancel()
              }}
            />
          </div>
          <Form
            className="grid grid-cols-4 overflow-auto mt-4 gap-4 max-h-[calc(50vh-200px)]"
            form={form}
          >
            {columns.map((item: any) => (
              <div
                key={item.key}
                className="flex items-center justify-between bg-[#F0F1F3] px-4 py-0 rounded gap-x-2"
              >
                <div className="flex items-center gap-x-2 text-base text-black py-1">
                  <MenuOutlined /> {item.title}
                </div>
                <Form.Item
                  key={item.key}
                  name={item.key}
                  valuePropName="checked"
                  noStyle
                  initialValue={!item.hidden}
                >
                  <Switch />
                </Form.Item>
              </div>
            ))}
          </Form>
          <div className="w-full flex justify-end mt-4 gap-x-4">
            <Button
              type="text"
              onClick={() => {
                onCancel()
              }}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="primary"
              onClick={() => {
                onSubmitViewColumn()
                onCancel()
              }}
            >
              {t('common.apply')}
            </Button>
          </div>
        </div>
      )}
      open={open}
      placement="bottomRight"
    >
      <Button
        icon={<MergeCellsOutlined />}
        onClick={() => {
          if (open) {
            onCancel()
          } else {
            onOk()
          }
        }}
      />
    </Dropdown>
  )
}

export default DropdownChangeColumn
