import { Button, Form, Modal, Switch } from 'antd'
import { view_column } from './viewColumn'
import { MenuOutlined } from '@ant-design/icons'
import { closeModalViewColumn } from '@/src/store/PermissionSlice'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'

interface Props {
  open: boolean
}

const ModalViewColumn = (props: Props) => {
  const { open } = props

  const [form] = Form.useForm()

  const dispatch = useAppDispatch()

  const onSubmit = async () => {
    // const values = form.getFieldsValue()
    // console.log('🚀 ~ onSubmit ~ values:', values)
  }

  return (
    <Modal
      open={open}
      width={'50%'}
      title="View Column"
      closable={false}
      className="min-w-3xl"
      footer={
        <div className="flex justify-end w-full gap-x-4 ">
          <Button
            onClick={() => {
              dispatch(closeModalViewColumn())
            }}
            type="text"
            className="!text-sx !font-bold"
          >
            Hủy
          </Button>
          <Button
            type="primary"
            onClick={onSubmit}
            className="!text-sx !font-bold !px-8"
          >
            Áp dụng
          </Button>
        </div>
      }
    >
      <Form
        className="grid grid-cols-3 overflow-auto h-[380px] !mt-4 gap-4"
        form={form}
      >
        {view_column.map(item => (
          <div
            key={item.key}
            className="flex items-center justify-between bg-[#F0F1F3] px-4 py-2 rounded"
          >
            <div className="flex items-center gap-x-2 text-base text-black">
              <MenuOutlined /> {item.label}
            </div>
            <Form.Item
              key={item.key}
              name={item.key}
              valuePropName="checked"
              noStyle
            >
              <Switch />
            </Form.Item>
          </div>
        ))}
      </Form>
    </Modal>
  )
}

export default ModalViewColumn
