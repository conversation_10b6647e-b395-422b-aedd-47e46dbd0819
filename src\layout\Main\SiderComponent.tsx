import { icon_logo, logo } from '@/src/assets/image'
import { colors } from '@/src/constants/colors'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { toggleSider } from '@/src/store/GlobalSlice'
import { LeftOutlined, RightOutlined } from '@ant-design/icons'
import { ConfigProvider, Layout, Menu } from 'antd'
import { useLocation, useNavigate } from 'react-router'
import styles from './index.module.scss'
import { useRoleMap } from './roleMap'

const { Sider } = Layout

const splitPathToSegments = (path: string) => {
  if (path === '/') return ['/']

  const parts = path.split('/').filter(Boolean)
  return parts.map(part => '/' + part)
}

const SiderComponent = () => {
  const dispatch = useAppDispatch()
  const { collapse } = useAppSelector(state => state.global)
  const navigate = useNavigate()
  const location = useLocation()
  const ROLE_MAP = useRoleMap()

  return (
    <ConfigProvider
      theme={{
        components: {
          Layout: {
            siderBg: colors.primary,
          },
        },
      }}
    >
      <Sider
        collapsed={collapse}
        className="relative h-screen rounded-r-3xl shadow-bottom flex flex-col justify-between"
        width={320}
      >
        <div
          className="rounded-full !absolute top-16 z-10 -right-4 bg-white px-2 py-1 shadow-bottom cursor-pointer"
          onClick={() => dispatch(toggleSider())}
        >
          {collapse ? <RightOutlined /> : <LeftOutlined />}
        </div>
        <div className="flex flex-col items-center p-8 w-full">
          <img
            src={collapse ? icon_logo : logo}
            alt="logo"
            className={`${collapse && 'scale-200'}`}
          />
        </div>
        <ConfigProvider
          theme={{
            components: {
              Menu: {
                itemBg: colors.primary,
                itemActiveBg: colors.primary,
                itemSelectedBg: colors.primary,
                itemSelectedColor: colors.secondary,
                itemBorderRadius: 0,
                itemColor: colors.white,
                itemDisabledColor: colors.white,
                subMenuItemSelectedColor: colors.white,
                itemMarginBlock: 0,
                itemHoverColor: colors.secondary,
                popupBg: colors.primary,
              },
            },
          }}
        >
          <Menu
            items={ROLE_MAP}
            className={`${styles.menu_item_selected} overflow-y-auto h-[calc(100vh-120px)] scrollbar-hidden`}
            mode="inline"
            onSelect={val => navigate(val.keyPath.reverse().join(''))}
            defaultOpenKeys={[
              '/category',
              '/flight-schedule',
              '/administrator',
              '/report',
            ]}
            selectedKeys={splitPathToSegments(location.pathname)}
          />
        </ConfigProvider>
      </Sider>
    </ConfigProvider>
  )
}

export default SiderComponent
