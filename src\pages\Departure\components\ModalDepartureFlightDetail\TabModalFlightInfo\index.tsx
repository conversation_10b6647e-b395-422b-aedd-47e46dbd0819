/* eslint-disable react-hooks/exhaustive-deps */
import { TIME_ONLY } from '@/src/constants'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getFlightDetail } from '@/src/service/flight'
import { useQuery } from '@tanstack/react-query'
import { Col, Form, Input, Row, Spin } from 'antd'
import moment from 'moment'
import { useEffect } from 'react'

const SpanLine = () => (
  <span
    style={{
      display: 'inline-block',
      lineHeight: '32px',
      textAlign: 'center',
      marginTop: 30,
    }}
  >
    -
  </span>
)

const TabModalFlightInfo = () => {
  const [form] = Form.useForm()

  const { selectedFlightId } = useAppSelector(state => state.departure)

  const { visibleModalDepartureFlightDetail } = useAppSelector(
    state => state.modalDepartureFlight
  )

  const { data: detailData, isLoading } = useQuery({
    queryKey: ['flight-detail', selectedFlightId],
    queryFn: () => getFlightDetail(selectedFlightId as string),
    enabled: !!selectedFlightId && visibleModalDepartureFlightDetail,
  })

  useEffect(() => {
    if (detailData) {
      form.setFieldsValue({
        ...detailData,
        flightNo: detailData?.fnCarrier + detailData?.fnNumber,
        routing: detailData?.depApSched + ' - ' + detailData?.arrApSched,
        std: moment(detailData?.depSchedDt).format(TIME_ONLY),
        sta: moment(detailData?.arrSchedDt).format(TIME_ONLY),
        etd: moment(detailData?.depDt).format(TIME_ONLY),
        eta: moment(detailData?.arrDt).format(TIME_ONLY),
        atd:
          detailData.legState === 'ARR'
            ? moment(detailData?.depDt).format(TIME_ONLY)
            : null,
        ata:
          detailData.legState === 'ARR'
            ? moment(detailData?.arrDt).format(TIME_ONLY)
            : null,
        nature: detailData?.fltType,
        configuration: `${detailData?.seatsC + 'J' + detailData?.seatsW + 'W' + detailData?.seatsY + 'Y'}`,
        fht: detailData?.fht ? moment(detailData?.fht).format(TIME_ONLY) : null,
      })
    }
  }, [selectedFlightId, detailData])

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      <Spin spinning={isLoading}>
        <Form form={form} layout="vertical">
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item label="Aircraft" name="acSubType">
                <Input readOnly />
              </Form.Item>
              <Form.Item label="Flight No" name="flightNo">
                <Input readOnly />
              </Form.Item>
              <Form.Item label="Routing" name="routing">
                <Input readOnly />
              </Form.Item>
              <Form.Item label="Configuration" name="configuration">
                <Input readOnly />
              </Form.Item>

              <Row gutter={12} className="flex justify-between">
                <Col span={11}>
                  <Form.Item label="STD" name="std">
                    <Input readOnly />
                  </Form.Item>
                </Col>
                <SpanLine />
                <Col span={11}>
                  <Form.Item label="STA" name="sta">
                    <Input readOnly />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={12} className="flex justify-between">
                <Col span={11}>
                  <Form.Item label="ETD" name="etd">
                    <Input readOnly />
                  </Form.Item>
                </Col>
                <SpanLine />
                <Col span={11}>
                  <Form.Item label="ETA" name="eta">
                    <Input readOnly />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={12} className="flex justify-between">
                <Col span={11}>
                  <Form.Item label="ATD" name="atd">
                    <Input readOnly />
                  </Form.Item>
                </Col>
                <SpanLine />
                <Col span={11}>
                  <Form.Item label="ATA" name="ata">
                    <Input readOnly />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={11}>
                  <Form.Item label="Flight duty" name="flightDuty">
                    <Input readOnly />
                  </Form.Item>
                </Col>
              </Row>
            </Col>

            <Col span={12}>
              <Form.Item label="Register No" name="acRegistration">
                <Input readOnly />
              </Form.Item>
              <Form.Item label="Code share" name="codeShare">
                <Input readOnly />
              </Form.Item>
              <Row gutter={12}>
                <Col span={12}>
                  <Form.Item label="Status" name="legState">
                    <Input readOnly />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Nature" name="nature">
                    <Input readOnly />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={12}>
                <Col span={24}>
                  <Form.Item label="Boarding Time" name="brd">
                    <Input readOnly />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={24}>
                  <Form.Item label="FHT" name="fht">
                    <Input readOnly />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label="GT reduce" name="gtReduce">
                <Input readOnly />
              </Form.Item>
              <Form.Item label="Gate duty" name="gateDuty">
                <Input readOnly />
              </Form.Item>
            </Col>
          </Row>

          <Col span={24}>
            <Form.Item label="Remark" name="remark">
              <Input.TextArea rows={2} readOnly />
            </Form.Item>
          </Col>
        </Form>
      </Spin>
    </div>
  )
}

export default TabModalFlightInfo
