/* eslint-disable react-hooks/exhaustive-deps */
import { HomeOutlined } from '@ant-design/icons'
import { Breadcrumb as AntdBreadcrumb } from 'antd'
import { Link, useLocation } from 'react-router'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

const isUUID = (str: string) => {
  const uuidPattern =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  return uuidPattern.test(str)
}

const Breadcrumb = () => {
  const location = useLocation()
  const { t } = useTranslation()
  const pathSnippets = location.pathname.split('/').filter(Boolean)

  // Loại bỏ segment cuối cùng nếu là ID (số hoặc UUID)
  const lastSegment = pathSnippets[pathSnippets.length - 1] || ''
  const isLastDynamicId = /^\d+$/.test(lastSegment) || isUUID(lastSegment)
  const displaySnippets = isLastDynamicId
    ? pathSnippets.slice(0, -1)
    : pathSnippets

  const breadcrumbNameMap: Record<string, string> = {
    category: t('nav.category'),
    pts: t('nav.categoryPTS'),
    edit: t('nav.edit'),
    create: t('nav.create'),
    job: t('nav.categoryJobs'),
    airport: t('nav.airport'),
    'group-pts-task': t('nav.groupPtsTask'),
    'employee-management': t('nav.employeeManagement'),
    administrator: t('nav.administrator'),
    user: t('nav.userManagement'),
    role: t('nav.roleManagement'),
    'aircraft-tech-info': t('nav.aircraftTechInfo'),
    'flight-schedule': t('nav.flightSchedule'),
    departure: t('nav.departure'),
    arrival: t('nav.arrival'),
    general: t('nav.general'),
    'organization-unit': t('nav.OrganizationUnits'),
    copy: t('nav.copy'),
    fleet: t('nav.fleet'),
    report: t('nav.report'),
    'flight-late': t('nav.flightLate'),
    'group-task-late': t('nav.groupTaskLate'),
    'department-late': t('nav.departmentLate'),
    'report-on-time-gt': t('nav.flightOnTimeGT'),
    'report-late-gt': t('nav.flightLateGT'),
    'report-pts-task-late': t('nav.reportPTSTaskLate'),
    'report-no-record-pts': t('nav.reportNoRecordPTS'),
    seasonal: 'Seasonal schedule',
  }

  const nonClickableSegments = [
    'category',
    'flight-schedule',
    'edit',
    'create',
    'administrator',
    'report',
  ]

  const breadcrumbItems = useMemo(() => {
    return displaySnippets.map((segment, index) => {
      // Tạo URL không bao gồm ID
      const url = `/${displaySnippets.slice(0, index + 1).join('/')}`
      const name = breadcrumbNameMap[segment] || segment

      const isNonClickable = nonClickableSegments.includes(segment)

      return {
        title: isNonClickable ? (
          <span className="text-gray-500">{name}</span>
        ) : (
          <Link className="!flex gap-2 items-center" to={url}>
            {name}
          </Link>
        ),
        key: url,
      }
    })
  }, [displaySnippets])

  return (
    <AntdBreadcrumb
      className={`!mb-4 ${location.pathname === '/' ? 'hidden' : ''}`}
      items={[
        {
          title: (
            <Link className="!flex gap-2 items-center" to="/">
              <HomeOutlined />
              <span>{t('nav.home')}</span>
            </Link>
          ),
          key: '/',
        },
        ...breadcrumbItems,
      ]}
    />
  )
}

export default Breadcrumb
