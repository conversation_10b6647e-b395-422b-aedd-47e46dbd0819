import { colors } from '@/src/constants/colors'
import { EditFilled } from '@ant-design/icons'
import { Button, Divider, Space } from 'antd'

const TabBriefing = () => {
  return (
    <div className="w-full py-3 bg-[#F5F9FA]">
      <div className="flex w-full justify-between mb-3">
        <div className="text-lg font-bold">Briefing</div>
        <Button
          className="!hidden"
          icon={<EditFilled />}
          type="primary"
          onClick={() => {}}
        >
          Chỉnh sửa
        </Button>
      </div>
      <div className="bg-[#FFFFFF] p-4 flex flex-col my-3">
        <div className="flex w-full justify-between">
          <div className="flex flex-col">
            <div className="text-lg font-semibold">VN36 07APR 06:20</div>
            <div className="text-xs text-[#9C9B9B] font-normal">
              Messaged by&nbsp;
              <span className="text-primary font-bold">thanhphamhuy</span> at
              16:24 Thu, 06/04/2023
            </div>
          </div>
          <Space className="flex">
            <Button type="primary">Đóng</Button>
            <Button type="primary">Chỉnh sửa</Button>
            <Button type="primary">Phản hồi</Button>
            <Button type="primary">In</Button>
          </Space>
        </div>
        <Divider style={{ borderColor: colors.primary, borderWidth: 1 }} />
        <div className="text-sm text-black font-normal">
          <div>VN1351 - VN657/18MAY: take care short conx.</div>
          <div>VN1351 SC dẫn đến pax short OB (1h30’).</div>
          <div>
            CXR đã check in pax + bag tới SIN, gửi 1pc hành lý Hầm 5, đồng thời
            nhờ PUR hỗ trợ cho pax xuống cùng xe J.
          </div>
          <div>Nhờ a/c hỗ trợ cho pax kịp nối chuyến nhé.</div>
          <div>Tks & Brgds,</div>
        </div>
        <div
          className="!text-primary font-bold cursor-pointer"
          onClick={() => {}}
        >
          More
        </div>
      </div>
    </div>
  )
}

export default TabBriefing
