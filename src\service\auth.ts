/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'
import type { ILoginType } from '@/src/schema/ILoginType'

const prefix = `/api/account`

const login = async (body: ILoginType) => {
  const api = `${prefix}/login`

  const response = await axios.post(api, body)
  return response.data
}

const logout = async () => {
  const api = `${prefix}/logout`

  const response = await axios.get(api)
  return response.data
}

const connectTokenLdap = async (body: any) => {
  const api = `/connect/token`

  const data = {
    grant_type: 'ldap',
    client_id:
      (window as any).RUNTIME_CONFIG?.VITE_CLIENT_ID ||
      import.meta.env.VITE_CLIENT_ID,
    scope:
      (window as any).RUNTIME_CONFIG?.VITE_SCOPE || import.meta.env.VITE_SCOPE,
    username: body.userNameOrEmailAddress,
    password: body.password,
  }

  const response = await axios.post(api, data, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })

  return response.data
}

const connectToken = async (body: any) => {
  const api = `/connect/token`

  const data = {
    grant_type: 'password',
    client_id:
      (window as any).RUNTIME_CONFIG?.VITE_CLIENT_ID ||
      import.meta.env.VITE_CLIENT_ID,
    scope:
      (window as any).RUNTIME_CONFIG?.VITE_SCOPE || import.meta.env.VITE_SCOPE,
    username: body.userNameOrEmailAddress,
    password: body.password,
  }

  const response = await axios.post(api, data, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })

  return response.data
}

const getMyProfile = async () => {
  const response = await axios.get(`${prefix}/my-profile`)
  return response.data
}

export { connectToken, getMyProfile, login, logout, connectTokenLdap }
