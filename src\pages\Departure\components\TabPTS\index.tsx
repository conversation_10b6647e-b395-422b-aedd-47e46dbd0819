/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  DISPLAY_DATE,
  ISO_DATETIME,
  ISO_DATETIME_NOSECOND,
  TIME_ONLY,
} from '@/src/constants'
import {
  LAO_FLIGHTS_CHANGE_PTS,
  LAO_FLIGHTS_LOG_PTS_TASK_TIME,
  LAO_FLIGHTS_RESET_PTS,
} from '@/src/constants/permission'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import usePermission from '@/src/hooks/usePermission'
import ModalGantt from '@/src/pages/Departure/components/TabPTS/ModalGantt'
import ModalLateGroupTask from '@/src/pages/Departure/components/TabPTS/ModalLateGroupTask'
import type { IPTSFlightResponse, IPTSMaster } from '@/src/schema/IPTSMaster'
import {
  getFlight,
  getPTSFlight,
  recordPTSFlight,
  resetPTSFlight,
  updatePTSMasterFlight,
} from '@/src/service/flight'
import {
  cannotReset,
  canReset,
  closeUpdatePTSModal,
  openGantt,
  openModalReasonLate,
  openModalSuitablePTS,
  openUpdatePTSModal,
  setPtsUpdateId,
  setSelectedPTSMasterId,
  setSort,
} from '@/src/store/DepartureSlice'
import { CaretDownOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  Dropdown,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Space,
  Table,
  TimePicker,
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import moment from 'moment'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import styles from './index.module.scss'
import ModalSuitablePts from './ModalSuitablePts'

const TabPTS = () => {
  const { t } = useTranslation()

  const {
    selectedFlightId,
    selectedPTSMasterId,
    visibleUpdatePTSModal,
    ptsUpdateId,
    visibleModalSuitablePTS,
    reset,
    sort,
    params,
  } = useAppSelector(state => state.departure)

  const { visibleModalDepartureFlightDetail } = useAppSelector(
    state => state.modalDepartureFlight
  )

  const navigate = useNavigate()

  const { locale } = useAppSelector(state => state.global)

  const dispatch = useAppDispatch()
  const { hasPermission } = usePermission()
  const [form] = Form.useForm()

  const {
    data: PTSFlightData,
    refetch,
    isLoading: isLoadingPTSFlight,
    error,
  } = useQuery<IPTSFlightResponse>({
    queryKey: ['flight-pts-detail', selectedFlightId, locale.locale],
    queryFn: async () => {
      return selectedFlightId && getPTSFlight(selectedFlightId, locale.locale)
    },
    enabled:
      (!!selectedFlightId && visibleModalDepartureFlightDetail) ||
      (!!visibleModalSuitablePTS && visibleModalDepartureFlightDetail),
  })

  const query = useQuery({
    queryKey: ['departure', params],
    queryFn: () => getFlight(params),
    enabled: false,
  })

  const mutationUpdatePTS = useMutation({
    mutationFn: async ({
      flightId,
      ptsId,
      body,
    }: {
      flightId: string
      ptsId: string
      body: any
    }) => {
      return updatePTSMasterFlight(flightId, ptsId, body)
    },
    onSuccess: () => {
      message.success(t('pts.updateSuccess'))
      refetch()
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const mutation = useMutation({
    mutationFn: ({
      flightId,
      ptsId,
      body,
    }: {
      flightId: string
      ptsId: string
      body: any
    }) => recordPTSFlight(flightId, ptsId, body),
    onSuccess: () => {
      message.success(t('pts.recordSuccess'))
      refetch()
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const mutationReset = useMutation({
    mutationFn: async () => {
      return selectedFlightId && resetPTSFlight(selectedFlightId)
    },
    onSuccess: () => {
      message.success(t('pts.resetSuccess'))
      refetch()
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const onUpdatePTS = () => {
    const values = form.getFieldsValue()
    if (!values.newTime) {
      message.error(t('pts.newTimeMessage'))
      return
    }
    const data = {
      flightId: selectedFlightId as string,
      ptsId: PTSFlightData?.ptsId as string,
      body: [
        {
          remark: values.note,
          actualStart: dayjs(values.newTime, TIME_ONLY).format(ISO_DATETIME),
          ptsDetailId: ptsUpdateId,
        },
      ],
    }

    mutationUpdatePTS.mutate(data)
    dispatch(closeUpdatePTSModal())
    form.resetFields()
    dispatch(setPtsUpdateId(null))
  }

  const columns: ColumnsType<IPTSMaster> = [
    {
      key: 'standardTime',
      title: t('pts.standardTime'),
      align: 'center',
      width: '150px',
      dataIndex: 'estimateStart',
      render: (time: string) => (
        <>
          <div>{time ? `${moment(time).format(TIME_ONLY)}` : ''}</div>
          <div>{time ? `${moment(time).format(DISPLAY_DATE)}` : ''}</div>
        </>
      ),
    },
    {
      key: 'actualTime',
      title: <div>{t('pts.actualTime')}</div>,
      align: 'center',
      width: '150px',
      dataIndex: 'actualStart',
      render: (time: string) => (
        <>
          <div>{time ? `${moment(time).format(TIME_ONLY)}` : ''}</div>
          <div>{time ? `${moment(time).format(DISPLAY_DATE)}` : ''}</div>
        </>
      ),
    },
    {
      key: 'description',
      title: <div className="text-center">{t('pts.description')}</div>,
      width: '200px',
      dataIndex: 'ptsDescription',
    },
    {
      key: 'jobGroup',
      title: <div className="text-center">{t('pts.jobGroup')}</div>,
      width: '200px',
      dataIndex: 'groupPtsTaskName',
    },
    {
      key: 'departmentName',
      dataIndex: 'departmentName',
      title: (
        <div className="text-center">{t('pts.managementResponsibility')}</div>
      ),
      width: '200px',
    },
    {
      key: 'note',
      title: <div className="text-center">{t('pts.note')}</div>,
      width: '300px',
      dataIndex: 'remark',
    },
    {
      key: 'status',
      title: t('pts.status'),
      width: '100px',
      fixed: 'right',
      align: 'center',
      hidden: !hasPermission(LAO_FLIGHTS_LOG_PTS_TASK_TIME),
      render: (record: any) => (
        <Flex className="justify-center w-full flex">
          <Button
            type="primary"
            className={`${record.actualStart ? '!hidden' : ''}`}
            loading={mutation.isPending}
            onClick={async () => {
              const data = {
                remark: record.remark ?? '',
                actualStart: moment().format(ISO_DATETIME),
                ptsDetailId: record.ptsDetailId,
                taskName: record.ptsDescription,
              }
              mutation.mutate({
                flightId: selectedFlightId as string,
                ptsId: PTSFlightData?.ptsId as string,
                body: [data],
              })
            }}
          >
            {t('pts.record')}
          </Button>
          <Button
            className={`${record.actualStart ? '' : '!hidden'}`}
            type="primary"
            loading={mutation.isPending}
            onClick={() => {
              dispatch(openUpdatePTSModal())
              dispatch(setPtsUpdateId(record.ptsDetailId))
              form.setFieldsValue({
                actualTime: moment(record.actualStart).format(TIME_ONLY),
                note: record.remark ?? '',
              })
            }}
          >
            {t('pts.edit')}
          </Button>
        </Flex>
      ),
    },
  ]

  const status = [
    {
      qty: 1,
      color: 'bg-late',
      text: t('pts.categoryLate'),
      key: 'late',
    },
  ]

  useEffect(() => {
    if (error) {
      handleApiError(error)
    }
  }, [error])

  useEffect(() => {
    if ((PTSFlightData?.ptsTasks || []).some(item => item.actualStart)) {
      dispatch(canReset())
    } else {
      dispatch(cannotReset())
    }
    if (PTSFlightData?.ptsId) {
      dispatch(setSelectedPTSMasterId(PTSFlightData?.ptsId))
    }
  }, [PTSFlightData])

  useEffect(() => {
    if (!selectedPTSMasterId) query.refetch()
  }, [visibleModalSuitablePTS])

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      <div className="w-full py-3 gap-y-4 flex flex-col">
        <div className="flex w-full justify-between items-center">
          <div className="flex flex-row text-sm gap-x-4">
            <div>
              {PTSFlightData?.ptsCode && (
                <div>
                  <span className="text-primary font-bold">
                    {t('departure.ptsCode')}
                  </span>
                  :&nbsp;
                  {PTSFlightData?.ptsCode}
                </div>
              )}
            </div>
            <div>
              {PTSFlightData?.assignmentTime && (
                <div>
                  <span className="text-primary font-bold">
                    {t('departure.assignPTSTime')}
                  </span>
                  :&nbsp;
                  {moment(PTSFlightData?.assignmentTime).format(
                    ISO_DATETIME_NOSECOND
                  )}
                </div>
              )}
            </div>
          </div>
          <Space className={`${!PTSFlightData ? '!hidden' : ''}`}>
            <Button
              color="danger"
              variant="outlined"
              onClick={() => {
                dispatch(openModalReasonLate())
              }}
            >
              {t('departure.reasonLate')}
            </Button>
            <Button
              type="primary"
              onClick={() => {
                dispatch(openGantt())
              }}
            >
              {t('pts.ganttChart')}
            </Button>
            {!reset
              ? hasPermission(LAO_FLIGHTS_CHANGE_PTS) && (
                  <Button
                    type="primary"
                    onClick={() => {
                      dispatch(openModalSuitablePTS())
                    }}
                  >
                    {t('pts.suitablePTSList')}
                  </Button>
                )
              : hasPermission(LAO_FLIGHTS_RESET_PTS) && (
                  <Button
                    type="primary"
                    onClick={() => {
                      Modal.confirm({
                        width: 600,
                        title: t('pts.resetPTS'),
                        content: t('pts.resetPTSMessage'),
                        okText: t('common.apply'),
                        cancelText: t('common.cancel'),
                        okButtonProps: {
                          style: { backgroundColor: '#006885', color: '#fff' },
                        },
                        onOk: () => {
                          mutationReset.mutate()
                        },
                      })
                    }}
                  >
                    {t('pts.resetPTS')}
                  </Button>
                )}
          </Space>
          <Space className={`${PTSFlightData ? '!hidden' : ''}`}>
            <Button
              type="primary"
              onClick={() => {
                navigate('/category/pts/create')
              }}
            >
              {t('pts.ptsCreate')}
            </Button>
          </Space>
        </div>
        <Table
          loading={
            isLoadingPTSFlight ||
            mutationUpdatePTS.isPending ||
            mutation.isPending
          }
          rowKey="ptsDetailId"
          bordered
          title={() => (
            <div className="flex flex-row justify-between">
              <div className="flex flex-row gap-x-3">
                {status.map((item: any) => (
                  <div
                    className="flex gap-x-1 text-xs font-normal text-black items-center text-nowrap"
                    key={item.key}
                  >
                    <div
                      className={`w-5 h-5 ${item.color} rounded-full flex items-center justify-center text-[8px] text-white font-medium`}
                    >
                      {item.qty}
                    </div>
                    {item.text}
                  </div>
                ))}
              </div>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'asc',
                      label: t('ptsDetail.sortAscending'),
                      onClick: () => {
                        dispatch(setSort('asc'))
                      },
                    },
                    {
                      key: 'desc',
                      label: t('ptsDetail.sortDescending'),
                      onClick: () => {
                        dispatch(setSort('desc'))
                      },
                    },
                  ],
                  selectedKeys: [sort],
                }}
              >
                <Space className="text-primary !text-sm font-semibold">
                  {t('common.sort')}
                  <CaretDownOutlined />
                </Space>
              </Dropdown>
            </div>
          )}
          columns={columns}
          className={`${styles.whiteHeader}`}
          size="small"
          scroll={{ x: 'max-content' }}
          rowHoverable={false}
          rowClassName={(record: any) => {
            const { actualStart, estimateStart } = record
            const isLate = moment(actualStart).isAfter(moment(estimateStart))
            return isLate ? '!bg-late' : ''
          }}
          dataSource={
            sort === 'asc'
              ? PTSFlightData?.ptsTasks?.sort((a: any, b: any) => {
                  return moment(a.estimateStart).diff(moment(b.estimateStart))
                })
              : PTSFlightData?.ptsTasks?.sort((a: any, b: any) => {
                  return moment(b.estimateStart).diff(moment(a.estimateStart))
                })
          }
          pagination={false}
          locale={{
            emptyText: (
              <div className="flex flex-col items-center justify-center w-full gap-y-1 py-4">
                <div className="text-base font-semibold text-black">
                  {error && (error as any).response?.data?.error?.message}
                </div>
              </div>
            ),
          }}
        />
        <Modal
          open={visibleUpdatePTSModal}
          okButtonProps={{ style: { display: 'none' } }}
          cancelButtonProps={{ style: { display: 'none' } }}
          onCancel={() => {
            dispatch(closeUpdatePTSModal())
            form.resetFields()
            dispatch(setPtsUpdateId(null))
          }}
        >
          <Form
            form={form}
            className="!mt-6 flex flex-col"
            labelCol={{ flex: '160px' }}
            labelAlign="left"
          >
            <Form.Item label={t('pts.oldActualTime')} name="actualTime">
              <Input className="!w-1/3" variant="borderless" />
            </Form.Item>
            <Form.Item label={t('pts.newActualTime')} name="newTime">
              <TimePicker format={TIME_ONLY} />
            </Form.Item>
            <div>{t('pts.note')}:</div>
            <Form.Item name="note">
              <Input.TextArea rows={3} />
            </Form.Item>
            <Button type="primary" onClick={onUpdatePTS}>
              {t('common.save')}
            </Button>
          </Form>
        </Modal>
        <ModalSuitablePts />
        <ModalGantt />
        <ModalLateGroupTask />
      </div>
    </div>
  )
}

export default TabPTS
