/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/file-managements`

const uploadFile = async (body: any) => {
  const api = `${prefix}`
  const response = await axios.post(api, body, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  console.log('----------')

  return response.data
}

const downloadFile = async (body: any) => {
  const api = `${prefix}/download`
  const response = await axios.post(api, body, {
    responseType: 'blob',
  })
  console.log('----------')
  return response.data
}

export { uploadFile, downloadFile }
