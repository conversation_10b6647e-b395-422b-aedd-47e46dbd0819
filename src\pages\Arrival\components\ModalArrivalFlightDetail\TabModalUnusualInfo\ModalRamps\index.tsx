/* eslint-disable @typescript-eslint/no-explicit-any */
import { Input, Form, Select, Card, Modal, Button } from 'antd'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  closeModalRamps,
  setSelectedUnusualInfoId,
} from '@/src/store/ModalArrivalFlightSlice'
import { PROCESSING_BY_OPTIONS, UNUSUAL_INFO_RAMPS } from '@/src/constants'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  createUnusualInfo,
  getUnusualInfoDetail,
  getUnusualInfoList,
  updateUnusualInfo,
} from '@/src/service/unusualInfo'
import { useEffect } from 'react'
import { handleApiError } from '@/src/helper/handleApiError'
import { getUnusualGroupCodes } from '@/src/service/unusualGroupCodes'
import { getServiceDept } from '@/src/service/serviceDept'
import { getAttribute } from '@/src/service/attribute'
import { getUnusualCodes } from '@/src/service/unusualCodes'

const ModalRamps = () => {
  const [form] = Form.useForm()
  const { visibleModalRamps, selectedUnusualInfoId, selectFlightModalId } =
    useAppSelector(state => state.modalArrivalFlight)

  const { data: unusualInfoDetail, isFetching } = useQuery({
    queryKey: [
      'unusual-info-detail',
      selectFlightModalId,
      selectedUnusualInfoId,
    ],
    queryFn: () =>
      getUnusualInfoDetail(
        selectFlightModalId as string,
        selectedUnusualInfoId as string
      ),
    enabled:
      !!selectedUnusualInfoId && !!selectFlightModalId && !!visibleModalRamps,
  })

  const { data: unusualGroupCodes } = useQuery({
    queryKey: ['unusual-group-codes'],
    queryFn: () => getUnusualGroupCodes(),
  })

  const { data: unusualCodes } = useQuery({
    queryKey: ['unusual-codes'],
    queryFn: () => getUnusualCodes(),
  })

  const { data: serviceDept } = useQuery({
    queryKey: ['service-dept'],
    queryFn: () => getServiceDept(),
  })

  const { data: attribute } = useQuery({
    queryKey: ['attribute'],
    queryFn: () => getAttribute(),
  })

  const mutation = useMutation({
    mutationFn: (values: any) => {
      return selectedUnusualInfoId && selectFlightModalId
        ? updateUnusualInfo(
            selectFlightModalId as string,
            selectedUnusualInfoId as string,
            values
          )
        : createUnusualInfo(selectFlightModalId as string, values)
    },
    onSuccess() {
      refetch()
      dispatch(setSelectedUnusualInfoId(null))
      dispatch(closeModalRamps())
      form.resetFields()
    },
    onError(error) {
      handleApiError(error)
    },
  })

  const { refetch } = useQuery({
    queryKey: ['unusual-info-list', selectFlightModalId],
    queryFn: () => getUnusualInfoList(selectFlightModalId as string),
    enabled: !!selectFlightModalId,
  })

  const dispatch = useAppDispatch()

  const onSubmit = async (values: any) => {
    await form.validateFields()
    await mutation.mutateAsync({ ...values, type: UNUSUAL_INFO_RAMPS })
  }

  useEffect(() => {
    if (unusualInfoDetail && visibleModalRamps) {
      form.setFieldsValue(unusualInfoDetail)
    }
  }, [unusualInfoDetail, visibleModalRamps])

  return (
    <Modal
      width="50%"
      title={
        unusualInfoDetail
          ? 'Update Unusual information'
          : 'Create Unusual information'
      }
      open={visibleModalRamps}
      onCancel={() => {
        dispatch(closeModalRamps())
        dispatch(setSelectedUnusualInfoId(null))
        form.resetFields()
      }}
      footer={null}
    >
      <Card size="small" variant="borderless" loading={isFetching}>
        <Form
          form={form}
          labelAlign="left"
          labelCol={{ flex: '120px' }}
          onFinish={onSubmit}
          initialValues={{ attribute: '0' }}
        >
          <Form.Item
            label="Code Group"
            name="codeGroup"
            rules={[{ required: true }]}
          >
            <Select
              options={unusualGroupCodes?.map((code: any) => ({
                label: `${code.name} - ${code.displayName}`,
                value: String(code.value),
              }))}
            />
          </Form.Item>
          <Form.Item label="Code" name="code" rules={[{ required: true }]}>
            <Select
              options={unusualCodes?.map((code: any) => ({
                label: `${code.name} - ${code.displayName}`,
                value: String(code.value),
              }))}
            />
          </Form.Item>
          <div className="flex w-full gap-x-4">
            <Form.Item
              label="Service Dept"
              className="w-1/2"
              name="serviceDept"
              rules={[{ required: true }]}
            >
              <Select
                options={serviceDept?.map((dept: any) => ({
                  label: `${dept.name} - ${dept.displayName}`,
                  value: String(dept.value),
                }))}
              />
            </Form.Item>
            <Form.Item
              label="Attribute"
              className="w-1/2"
              name="attribute"
              rules={[{ required: true }]}
            >
              <Select
                options={attribute?.map((attr: any) => ({
                  label: `${attr.name} - ${attr.displayName}`,
                  value: String(attr.value),
                }))}
              />
            </Form.Item>
          </div>
          <Form.Item label="Event" name="event">
            <Input.TextArea rows={1} />
          </Form.Item>
          <Form.Item label="Reason" name="reason">
            <Input.TextArea rows={1} />
          </Form.Item>
          <Form.Item label="Solution" name="solution">
            <Input.TextArea rows={1} />
          </Form.Item>
          <Form.Item label="Suggestion" name="suggestion">
            <Input.TextArea rows={1} />
          </Form.Item>
          <Form.Item
            label="Processing By"
            name="processingBy"
            rules={[{ required: true }]}
          >
            <Select options={PROCESSING_BY_OPTIONS} />
          </Form.Item>
          <Form.Item className="flex justify-end">
            <Button
              htmlType="submit"
              type="primary"
              loading={mutation.isPending}
            >
              Save
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </Modal>
  )
}

export default ModalRamps
