import { EditFilled } from '@ant-design/icons'
import { Button, Table } from 'antd'
import styles from './index.module.scss'

const TabReason = () => {
  const columns = [
    { title: 'Reason marked by DHKT', width: '20%' },
    { title: 'Service dept', width: '20%' },
    { title: 'Attribute', width: '20%' },
    { title: 'Delay time', width: '20%' },
    { title: 'Updated by', width: '20%' },
  ]

  const columns2 = [
    { title: 'Reason marked by DHKT', width: '33%' },
    { title: 'Delay time', width: '33%' },
    { title: 'Updated by', width: '33%' },
  ]

  return (
    <div className="w-full py-3 bg-[#F5F9FA] gap-y-4 flex flex-col">
      <div className="flex w-full justify-between">
        <div className="text-lg font-bold">Pre-Flight</div>
        <Button
          className="!hidden"
          icon={<EditFilled />}
          type="primary"
          onClick={() => {}}
        >
          Chỉnh sửa
        </Button>
      </div>
      <Table bordered columns={columns} className={`${styles.whiteHeader}`} />
      <div className="flex w-full justify-end">
        <Button className="w-max !text-primary" type="text">
          Add / Edit
        </Button>
      </div>
      <Table bordered columns={columns2} className={`${styles.whiteHeader}`} />
    </div>
  )
}

export default TabReason
