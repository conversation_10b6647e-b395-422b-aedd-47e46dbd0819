/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Flex, message, Modal, Table, Typography } from 'antd'
import type { TableProps } from 'antd/lib'
import styles from './index.module.scss'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import {
  openModalFlight,
  openModalGroundService,
  openModalRamps,
  setSelectedUnusualInfoId,
} from '@/src/store/ModalArrivalFlightSlice'
import React, { Suspense } from 'react'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  deleteUnusualInfo,
  getUnusualInfoList,
} from '@/src/service/unusualInfo'
import { UNUSUAL_INFO_GROUND_SERVICE } from '@/src/constants'
import { UNUSUAL_INFO_FLIGHT } from '@/src/constants'
import { UNUSUAL_INFO_RAMPS } from '@/src/constants'
import { DeleteFilled, EditFilled } from '@ant-design/icons'
import FileSaver from 'file-saver'
import { getUnusualGroupCodes } from '@/src/service/unusualGroupCodes'
import { getAttribute } from '@/src/service/attribute'
import { downloadFile } from '@/src/service/upload'
import { handleApiError } from '@/src/helper/handleApiError'
import { getUnusualCodes } from '@/src/service/unusualCodes'

// Lazy load modal components
const ModalGroundService = React.lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalUnusualInfo/ModalGroundService'
    )
)
const ModalFlight = React.lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalUnusualInfo/ModalFlight'
    )
)
const ModalRamps = React.lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalUnusualInfo/ModalRamps'
    )
)

const TabModalUnusualInfo = () => {
  const dispatch = useAppDispatch()
  const {
    selectFlightModalId,
    selectedUnusualInfoId,
    visibleModalArrivalFlightDetail,
  } = useAppSelector(state => state.modalArrivalFlight)
  const {
    data: unusualInfoList,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ['unusual-info-list', selectFlightModalId],
    queryFn: () => getUnusualInfoList(selectFlightModalId as string),
    enabled: !!selectFlightModalId && visibleModalArrivalFlightDetail,
  })

  const unusualInfoListGroundService = unusualInfoList?.filter(
    (item: any) => item.type === UNUSUAL_INFO_GROUND_SERVICE
  )
  const unusualInfoListFlight = unusualInfoList?.filter(
    (item: any) => item.type === UNUSUAL_INFO_FLIGHT
  )
  const unusualInfoListRamps = unusualInfoList?.filter(
    (item: any) => item.type === UNUSUAL_INFO_RAMPS
  )

  const { data: unusualCodes } = useQuery({
    queryKey: ['unusual-codes'],
    queryFn: () => getUnusualCodes(),
  })

  const { data: attributes } = useQuery({
    queryKey: ['attributes'],
    queryFn: () => getAttribute(),
  })

  const { data: unusualGroupCodes } = useQuery({
    queryKey: ['unusual-group-codes'],
    queryFn: () => getUnusualGroupCodes(),
  })

  const mutationGetFile = useMutation({
    mutationFn: async (values: any) => {
      return downloadFile(values)
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationDelete = useMutation({
    mutationFn: () =>
      deleteUnusualInfo(
        selectFlightModalId as string,
        selectedUnusualInfoId as string
      ),
    onSuccess() {
      refetch()
      dispatch(setSelectedUnusualInfoId(null))
      message.success('Unusual information deleted successfully!')
    },
    onError(error) {
      handleApiError(error)
    },
  })

  const columnsGroundService: TableProps<any>['columns'] = [
    {
      title: 'Code',
      width: '10%',
      render: (record: any) =>
        record.code
          ? `${unusualCodes?.find((code: any) => code.value == record.code)?.displayName ?? ''}`
          : '',
    },
    {
      title: 'Content',
      width: '50%',
      render: (record: any) => (
        <div>
          {record?.description && (
            <div>
              <div>Description:</div>
              <div className="pl-4">
                <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                  {record.description}
                </Typography.Paragraph>
              </div>
            </div>
          )}

          {record?.solution && (
            <div>
              <div>Solution:</div>
              <div className="pl-4">
                <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                  {record.solution}
                </Typography.Paragraph>
              </div>
            </div>
          )}

          {record?.result && (
            <div>
              <div>Result:</div>
              <div className="pl-4">
                <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                  {record.result}
                </Typography.Paragraph>
              </div>
            </div>
          )}

          {record?.remark && (
            <div>
              <div>Remark:</div>
              <div className="pl-4">
                <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                  {record.remark}
                </Typography.Paragraph>
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'File',
      width: '15%',
      render: (record: any) => (
        <a
          onClick={async () => {
            const file = await mutationGetFile.mutateAsync({
              filePath: record.filePath,
              fileName: record.fileName,
            })
            return FileSaver.saveAs(file, record.fileName)
          }}
        >
          {record.fileName}
        </a>
      ),
    },
    { title: 'Created by', width: '15%', dataIndex: 'creatorName' },
    {
      title: 'Action',
      width: '10%',
      render: (record: any) => (
        <Flex justify="center" gap={4}>
          <Button
            icon={<EditFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedUnusualInfoId(record.id))
              dispatch(openModalGroundService())
            }}
          />
          <Button
            icon={<DeleteFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedUnusualInfoId(record.id))
              Modal.confirm({
                title: 'Delete Reason?',
                content: 'Do you want to remove this reason?',
                okText: 'Yes',
                cancelText: 'No',
                closable: false,
                onOk: () => mutationDelete.mutate(),
              })
            }}
          />
        </Flex>
      ),
    },
  ]

  const columnsFlight: TableProps<any>['columns'] = [
    {
      title: 'Code',
      width: '10%',
      render: (record: any) =>
        record.code
          ? `${unusualCodes?.find((code: any) => code.value == record.code)?.displayName ?? ''}`
          : '',
    },
    {
      title: 'Content',
      width: '50%',
      render: (record: any) => {
        return (
          <div>
            {record?.event && (
              <div>
                <div>Event:</div>
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.event}
                  </Typography.Paragraph>
                </div>
              </div>
            )}

            {record?.reason && (
              <div>
                <div>Reason:</div>
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.reason}
                  </Typography.Paragraph>
                </div>
              </div>
            )}

            {record?.solution && (
              <div>
                <div>Solution:</div>
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.solution}
                  </Typography.Paragraph>
                </div>
              </div>
            )}

            {record?.suggestion && (
              <div>
                <div>Suggestion:</div>
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.suggestion}
                  </Typography.Paragraph>
                </div>
              </div>
            )}
          </div>
        )
      },
    },
    {
      title: 'Attribute',
      width: '15%',
      render: (record: any) =>
        record.attribute
          ? `${attributes?.find((code: any) => code.value == record.attribute)?.displayName ?? ''}`
          : '',
    },
    { title: 'Created by', width: '15%', dataIndex: 'creatorName' },
    {
      title: 'Action',
      width: '10%',
      render: (record: any) => (
        <Flex justify="center" gap={4}>
          <Button
            icon={<EditFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedUnusualInfoId(record.id))
              dispatch(openModalFlight())
            }}
          />
          <Button
            icon={<DeleteFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedUnusualInfoId(record.id))
              Modal.confirm({
                title: 'Delete Reason?',
                content: 'Do you want to remove this reason?',
                okText: 'Yes',
                cancelText: 'No',
                closable: false,
                onOk: () => mutationDelete.mutate(),
              })
            }}
          />
        </Flex>
      ),
    },
  ]

  const columnsRamps: TableProps<any>['columns'] = [
    {
      title: 'Group',
      width: '10%',
      render: (record: any) =>
        record.codeGroup
          ? `${unusualGroupCodes?.find((code: any) => code.value == record.codeGroup)?.displayName ?? ''}`
          : '',
    },
    {
      title: 'Code',
      width: '10%',
      render: (record: any) =>
        record.code
          ? `${unusualCodes?.find((code: any) => code.value == record.code)?.displayName ?? ''}`
          : '',
    },
    {
      title: 'Content',
      width: '40%',
      render: (record: any) => {
        return (
          <div>
            {record?.event && (
              <div>
                <div>Event:</div>
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.event}
                  </Typography.Paragraph>
                </div>
              </div>
            )}

            {record?.reason && (
              <div>
                <div>Reason:</div>
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.reason}
                  </Typography.Paragraph>
                </div>
              </div>
            )}

            {record?.solution && (
              <div>
                <div>Solution:</div>
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.solution}
                  </Typography.Paragraph>
                </div>
              </div>
            )}

            {record?.suggestion && (
              <div>
                <div>Suggestion:</div>
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.suggestion}
                  </Typography.Paragraph>
                </div>
              </div>
            )}
          </div>
        )
      },
    },
    {
      title: 'Attribute',
      width: '15%',
      render: (record: any) =>
        record.attribute
          ? `${attributes?.find((code: any) => code.value == record.attribute)?.displayName ?? ''}`
          : '',
    },
    { title: 'Created by', width: '15%', dataIndex: 'creatorName' },
    {
      title: 'Action',
      width: '10%',
      align: 'center',
      render: (record: any) => (
        <Flex justify="center" gap={4}>
          <Button
            icon={<EditFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedUnusualInfoId(record.id))
              dispatch(openModalRamps())
            }}
          />
          <Button
            icon={<DeleteFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedUnusualInfoId(record.id))
              Modal.confirm({
                title: 'Delete Reason?',
                content: 'Do you want to remove this reason?',
                okText: 'Yes',
                cancelText: 'No',
                closable: false,
                onOk: () => mutationDelete.mutate(),
              })
            }}
          />
        </Flex>
      ),
    },
  ]

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      <div className="flex flex-col gap-y-4">
        <Table
          title={() => (
            <div className="font-bold text-lg">
              Unusual information for Ground Service
            </div>
          )}
          bordered
          size="small"
          columns={columnsGroundService}
          className={`${styles.whiteHeader}`}
          dataSource={unusualInfoListGroundService || []}
          pagination={false}
          rowKey={record => record.id}
          loading={isFetching}
        />
        <Flex justify="flex-end" className="items-center flex !py-1 ">
          <Button
            type="primary"
            onClick={() => {
              dispatch(openModalGroundService())
            }}
          >
            Create Unusual information
          </Button>
        </Flex>
        <Suspense>
          <ModalGroundService />
        </Suspense>

        <Table
          title={() => (
            <div className="font-bold text-lg">
              Unusual information for Flight
            </div>
          )}
          bordered
          size="small"
          columns={columnsFlight}
          className={`${styles.whiteHeader}`}
          dataSource={unusualInfoListFlight || []}
          pagination={false}
          rowKey={record => record.id}
          loading={isFetching}
        />
        <Flex justify="flex-end" className="items-center flex !py-1 ">
          <Button
            type="primary"
            onClick={() => {
              dispatch(openModalFlight())
            }}
          >
            Create Unusual information
          </Button>
        </Flex>
        <Suspense>
          <ModalFlight />
        </Suspense>

        <Table
          title={() => (
            <div className="font-bold text-lg">
              Unusual information for Ramps
            </div>
          )}
          bordered
          size="small"
          columns={columnsRamps}
          className={`${styles.whiteHeader}`}
          dataSource={unusualInfoListRamps || []}
          pagination={false}
          loading={isFetching}
        />
        <Flex justify="flex-end" className="items-center flex !py-1 ">
          <Button
            type="primary"
            onClick={() => {
              dispatch(openModalRamps())
            }}
          >
            Create Unusual information
          </Button>
        </Flex>
        <Suspense>
          <ModalRamps />
        </Suspense>
      </div>
    </div>
  )
}
export default TabModalUnusualInfo
