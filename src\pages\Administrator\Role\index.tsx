/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import ModalRolePermission from '@/src/pages/Administrator/Role/ModalRolePermission'
import type { IRole } from '@/src/schema/IRole'
import { createRole, getRole, removeRole, updateRole } from '@/src/service/role'
import {
  closeRoleModal,
  openRoleModal,
  openRolePermissionModal,
  resetState,
  setParams,
  setSelectedRoleId,
  setSelectedRoleName,
} from '@/src/store/RoleSlice'
import { PlusOutlined, SettingFilled } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  Dropdown,
  Form,
  Input,
  message,
  Modal,
  Space,
  Table,
  Tag,
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import { handleApiError } from '@/src/helper/handleApiError'
import { useEffect } from 'react'
import ShowTotal from '@/src/components/Showtotal'
import usePermission from '@/src/hooks/usePermission'
import {
  ABP_IDENTITY_ROLES_CREATE,
  ABP_IDENTITY_ROLES_DELETE,
  ABP_IDENTITY_ROLES_MANAGE_PERMISSIONS,
  ABP_IDENTITY_ROLES_UPDATE,
} from '@/src/constants/permission'

const RolePage = () => {
  const [form] = Form.useForm()
  const { hasPermission } = usePermission()

  const { t } = useTranslation()

  const { visibleRoleModal, params, selectedRoleId } = useAppSelector(
    state => state.role
  )

  const dispatch = useAppDispatch()

  const {
    data: roleData,
    isLoading: isRoleLoading,
    refetch: getRoleList,
  } = useQuery({
    queryKey: ['role-list', params],
    queryFn: () => getRole(params),
  })

  const mutation = useMutation({
    mutationFn: async (values: IRole) => {
      return selectedRoleId ? updateRole(values) : createRole(values)
    },
    onSuccess: () => {
      getRoleList()
      form.resetFields()
      dispatch(closeRoleModal())
      dispatch(setSelectedRoleId(''))
      message.success(
        selectedRoleId
          ? t('role.updateRoleSuccess')
          : t('role.createRoleSuccess')
      )
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const mutationDelete = useMutation({
    mutationFn: async (id: string) => removeRole(id),
    onSuccess: () => {
      getRoleList()
      message.success(t('role.deleteRoleSuccess'))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const columns: ColumnsType<IRole> = [
    {
      title: t('role.action'),
      width: 100,
      align: 'center',
      hidden: !hasPermission([
        ABP_IDENTITY_ROLES_UPDATE,
        ABP_IDENTITY_ROLES_MANAGE_PERMISSIONS,
        ABP_IDENTITY_ROLES_DELETE,
      ]),
      render: (record: IRole) => {
        const menuItems = [
          {
            key: 'edit',
            label: t('common.edit'),
            onClick: () => {
              form.setFieldsValue(record)
              dispatch(setSelectedRoleId(record.id))
              dispatch(openRoleModal())
            },
            className: !hasPermission(ABP_IDENTITY_ROLES_UPDATE)
              ? '!hidden'
              : '',
          },
          {
            key: 'permissions',
            label: t('role.permissions'),
            onClick: () => {
              dispatch(setSelectedRoleId(record.id))
              dispatch(setSelectedRoleName(record.name))
              dispatch(openRolePermissionModal())
            },
            className: !hasPermission(ABP_IDENTITY_ROLES_MANAGE_PERMISSIONS)
              ? '!hidden'
              : '',
          },
          {
            key: 'delete',
            label: t('common.delete'),
            className: !hasPermission(ABP_IDENTITY_ROLES_DELETE)
              ? '!hidden'
              : '',
            onClick: () => {
              Modal.confirm({
                title: t('common.confirmDeleteTitle'),
                content: t('common.confirmDeleteMessage'),
                okText: t('common.yes'),
                cancelText: t('common.no'),
                onOk: () => mutationDelete.mutate(record.id),
              })
            },
          },
        ]
        return (
          <Space>
            <Dropdown menu={{ items: menuItems }}>
              <Button icon={<SettingFilled />} type="primary">
                {t('role.action')}
              </Button>
            </Dropdown>
          </Space>
        )
      },
    },
    {
      key: 'name',
      title: t('role.roleName'),
      dataIndex: '',
      // width: '40%',
      render: (record: IRole) => (
        <Space>
          {record.name}
          {record.isPublic && (
            <Tag className="!text-white !bg-primary !rounded-full !hidden">
              Public
            </Tag>
          )}
        </Space>
      ),
    },
    // {
    //   key: 'userCount',
    //   title: t('role.userCount'),
    //   dataIndex: 'userCount',
    //   width: '10%',
    //   align: 'center',
    // },
  ]

  const onSubmit = async () => {
    const values = await form.validateFields()
    const data = {
      ...values,
      id: selectedRoleId ?? '',
    }
    mutation.mutate(data)
  }

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <div className="flex flex-col gap-4">
      <div className="w-full flex justify-between">
        <div className="text-lg font-bold text-black">{t('role.roleList')}</div>
        {hasPermission(ABP_IDENTITY_ROLES_CREATE) && (
          <Button
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => dispatch(openRoleModal())}
          >
            {t('role.add')}
          </Button>
        )}
      </div>

      <Table
        dataSource={roleData ? roleData.items : []}
        columns={columns}
        bordered
        size="small"
        loading={isRoleLoading}
        className={`${styles.whiteHeader}`}
        // title={() => (
        //   <div className="flex justify-end items-center">
        //     <Button
        //       type="primary"
        //       icon={<PlusOutlined />}
        //       onClick={() => {
        //         dispatch(openRoleModal())
        //       }}
        //     >
        //       {t('role.add')}
        //     </Button>
        //   </div>
        // )}
        pagination={{
          pageSizeOptions: [10, 50, 100, 500, 1000],
          total: isRoleLoading ? 0 : roleData?.totalCount,
          current: params.SkipCount / params.MaxResultCount + 1,
          pageSize: params.MaxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                SkipCount: (page - 1) * pageSize,
                MaxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        rowKey={record => record.id}
      />
      <Modal
        open={visibleRoleModal}
        title={selectedRoleId ? t('role.updateRole') : t('role.newRole')}
        onOk={onSubmit}
        onCancel={() => {
          dispatch(closeRoleModal())
          dispatch(setSelectedRoleId(''))
          form.resetFields()
        }}
        closable={false}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label={t('role.roleName')}
            name="name"
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          {/* <Form.Item name="isDefault" valuePropName="checked" label={null}>
            <Checkbox>{t('role.isDefault')}</Checkbox>
          </Form.Item>
          <Form.Item name="isPublic" valuePropName="checked" label={null}>
            <Checkbox>{t('role.isPublic')}</Checkbox>
          </Form.Item> */}
        </Form>
      </Modal>
      <ModalRolePermission />
    </div>
  )
}

export default RolePage
