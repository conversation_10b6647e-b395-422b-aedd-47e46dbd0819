/* eslint-disable @typescript-eslint/no-explicit-any */
import { AIRPORT_GLOBAL } from '@/src/constants'
import { createSlice } from '@reduxjs/toolkit'
import localeEn from 'antd/locale/en_US'
import localeVi from 'antd/locale/vi_VN'

const locale = {
  vi: localeVi,
  en: localeEn,
}

interface GlobalState {
  collapse: boolean
  locale: (typeof locale)[keyof typeof locale]
  profile: any
  visibleModalSelectAirport: boolean
  airportGlobal: any
  settings: any
}

// Lấy locale từ localStorage hoặc mặc định là 'vi'
const getStoredLocale = (): keyof typeof locale => {
  try {
    const stored = localStorage.getItem('app_locale')
    return stored === 'en' || stored === 'vi' ? stored : 'en'
  } catch {
    return 'en'
  }
}

const getStoredAirport = () => {
  try {
    const stored = localStorage.getItem(AIRPORT_GLOBAL)

    return stored ?? null
  } catch {
    return null
  }
}

const initialState: GlobalState = {
  collapse: false,
  profile: null,
  locale: locale[getStoredLocale()],
  visibleModalSelectAirport: getStoredAirport() ? false : true,
  airportGlobal: getStoredAirport() || '',
  settings: null,
}

const globalSlice = createSlice({
  name: 'global',
  initialState,
  reducers: {
    toggleSider: state => {
      state.collapse = !state.collapse
    },
    setLocale: (state, action: { payload: keyof typeof locale }) => {
      state.locale = locale[action.payload]
      localStorage.setItem('app_locale', action.payload)
    },
    setProfile: (state, action) => {
      state.profile = action.payload
    },
    openModalSelectAirport: state => {
      state.visibleModalSelectAirport = true
    },
    closeModalSelectAirport: state => {
      state.visibleModalSelectAirport = false
    },
    setAirportGlobal: (state, action) => {
      state.airportGlobal = action.payload
      localStorage.setItem(AIRPORT_GLOBAL, action.payload)
    },
    setSettings: (state, action) => {
      state.settings = action.payload
    },
  },
})

export const {
  toggleSider,
  setLocale,
  setProfile,
  openModalSelectAirport,
  closeModalSelectAirport,
  setAirportGlobal,
  setSettings,
} = globalSlice.actions
export default globalSlice.reducer
