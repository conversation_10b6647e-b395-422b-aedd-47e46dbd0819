import { PlusOutlined } from '@ant-design/icons'
import { Button, Flex, Table } from 'antd'
import type { TableProps } from 'antd/lib'
import AddRoleModal from './AddRoleModal'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { toggleOrganizationUnitRoleModal } from '@/src/store/OrganizationUnitSlice'
import styles from '../index.module.scss'

interface Role {
  key: string
  roleName: string
  creationTime: string
}

const roleColumns: TableProps<Role>['columns'] = [
  {
    title: 'Tên vai trò',
    dataIndex: 'roleName',
    key: 'roleName',
  },
  {
    title: 'Thời điểm thêm',
    dataIndex: 'creationTime',
    key: 'creationTime',
  },
  {
    title: 'Hành động',
    key: 'action',
    align: 'center',
    render: () => <Button type="primary">Xoá</Button>,
  },
]

const roleData = [
  {
    key: '1',
    roleName: 'Quản trị viên',
    creationTime: '2024-06-01 09:00',
  },
  {
    key: '2',
    roleName: 'Kế toán',
    creationTime: '2024-06-02 10:30',
  },
  {
    key: '3',
    roleName: 'Nhân viên bán hàng',
    creationTime: '2024-06-03 14:15',
  },
  {
    key: '4',
    roleName: 'Quản lý kho',
    creationTime: '2024-06-04 16:45',
  },
  {
    key: '5',
    roleName: 'Khách hàng',
    creationTime: '2024-06-05 08:20',
  },
]

const RoleTab = () => {
  const dispatch = useAppDispatch()

  return (
    <>
      <Table
        title={() => (
          <Flex align="end" vertical>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                dispatch(toggleOrganizationUnitRoleModal())
              }}
            >
              Thêm vai trò
            </Button>
          </Flex>
        )}
        columns={roleColumns}
        dataSource={roleData}
        size="small"
        bordered
        className={`${styles.whiteHeader}`}
      />
      <AddRoleModal />
    </>
  )
}

export default RoleTab
