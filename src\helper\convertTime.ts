export const convertTime = (time?: string) => {
  if (!time || !time.includes('.')) return time

  const [dayPart, timePart] = time.split('.')
  if (!dayPart || !timePart || !timePart.includes(':')) return '00:00:00'

  const [hourStr, minutesStr, secondStr] = timePart.split(':')
  if (!hourStr || !minutesStr || !secondStr) return '00:00:00'

  const hh = Number(dayPart) * 24 + Number(hourStr)

  return `${hh}:${minutesStr}:${secondStr}`
}
