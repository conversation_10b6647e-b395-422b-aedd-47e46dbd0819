/* eslint-disable @typescript-eslint/no-explicit-any */
import { Table } from 'antd'
import type { TableProps } from 'antd/lib'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'

const TabModalPassengerIncident = () => {
  const { t } = useTranslation()

  const columns: TableProps<any>['columns'] = [
    {
      key: 'index',
      title: t('table.order'),
      width: '10%',
    },
    {
      key: 'name',
      title: t('table.name'),
      width: '20%',
      dataIndex: 'name',
    },
    {
      key: 'description',
      title: t('table.description'),
      width: '60%',
      dataIndex: 'description',
    },
    {
      key: 'cost',
      dataIndex: 'cost',
      title: t('table.cost'),
      width: '10%',
    },
  ]

  return (
    <div>
      <Table
        size="small"
        rowKey={record => record.id}
        bordered
        columns={columns}
        className={`${styles.whiteHeader}`}
        dataSource={[
          {
            key: 1,
            name: 'Name 1',
            description: 'Description 1',
            cost: '1',
          },
        ]}
      />
    </div>
  )
}

export default TabModalPassengerIncident
