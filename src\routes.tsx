/* eslint-disable react-refresh/only-export-components */
import {
  ABP_IDENTITY_ROLES,
  ABP_IDENTITY_USERS,
  LAO_AIRPORTS,
  LAO_DEPARTMENT,
  LAO_FLEETS,
  LAO_FLIGHTS,
  LAO_GROUP_PTS_TASKS,
  LAO_PTS_MASTERS,
  LAO_PTS_MASTERS_CREATE,
  LAO_PTS_MASTERS_EDIT,
  LAO_PTS_TASKS,
} from '@/src/constants/permission'
import RolePage from '@/src/pages/Administrator/Role'
import UserPage from '@/src/pages/Administrator/User'
import AirportPage from '@/src/pages/Airport'
import DeparturePage from '@/src/pages/Departure'
import FleetPage from '@/src/pages/Fleet'
import GroupTaskPTS from '@/src/pages/GroupTaskPTS'
import JobPage from '@/src/pages/Job'
import LoginLdapPage from '@/src/pages/LoginLdap'
import type { RouteObject } from 'react-router'
import { useRoutes } from 'react-router'
import ProtectedRoute from './components/ProtectedRoute'
import AuthLayout from './layout/Auth'
import MainLayout from './layout/Main'
import OrganizationUnitsPage from './pages/Administrator/OrganizationUnits'
import DashboardPage from './pages/Dashboard'
import ForbiddenPage from './pages/Forbidden'
import LoginPage from './pages/Login'
import NotFound from './pages/NotFound'
import PTSPage from './pages/PTS'
import PTSCopyPage from './pages/PTSCopy'
import PTSCreatePage from './pages/PTSCreate'
import PTSEditPage from './pages/PTSEdit'
import ReportFlightPage from '@/src/pages/ReportFlight'
import ReportDepartmentLatePage from '@/src/pages/ReportDepartmentLate'
import ReportOnTimeGTPage from '@/src/pages/ReportOnTimeGT'
import ReportLateGTPage from '@/src/pages/ReportLateGT'
import ReportPTSTaskLatePage from '@/src/pages/ReportPTSTaskLate'
import ReportNoRecordPTSPage from '@/src/pages/ReportNoRecordPTS'
import ReportPTSGroupTaskLatePage from '@/src/pages/ReportPTSGroupTaskLate'
import ArrivalPage from '@/src/pages/Arrival'
import GeneralPage from '@/src/pages/General'
import SeasonalPage from '@/src/pages/Seasonal'

const routes: RouteObject[] = [
  {
    element: <AuthLayout />,
    errorElement: <NotFound />,
    children: [
      {
        path: '/login',
        element: <LoginPage />,
        errorElement: <NotFound />,
      },
      {
        index: true,
        path: '/login-ldap',
        element: <LoginLdapPage />,
        errorElement: <NotFound />,
      },
    ],
  },
  {
    element: <MainLayout />,
    errorElement: <NotFound />,
    children: [
      {
        index: true,
        path: '/',
        element: <DashboardPage />,
        errorElement: <NotFound />,
      },
      {
        path: '/flight-schedule',
        children: [
          {
            path: 'departure',
            element: (
              <ProtectedRoute requiredPermission={LAO_FLIGHTS}>
                <DeparturePage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
          {
            path: 'arrival',
            element: (
              <ProtectedRoute requiredPermission={LAO_FLIGHTS}>
                <ArrivalPage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
          {
            path: 'general',
            element: (
              <ProtectedRoute requiredPermission={LAO_FLIGHTS}>
                <GeneralPage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
          {
            path: 'seasonal',
            element: <SeasonalPage />,
            errorElement: <NotFound />,
          },
        ],
      },
      {
        path: '/category',
        children: [
          {
            path: 'pts',
            children: [
              {
                index: true,
                element: (
                  <ProtectedRoute requiredPermission={LAO_PTS_MASTERS}>
                    <PTSPage />
                  </ProtectedRoute>
                ),
                errorElement: <NotFound />,
              },
              {
                path: 'edit/:id',
                element: (
                  <ProtectedRoute requiredPermission={LAO_PTS_MASTERS_EDIT}>
                    <PTSEditPage />
                  </ProtectedRoute>
                ),
                errorElement: <NotFound />,
              },
              {
                path: 'copy/:id',
                element: (
                  <ProtectedRoute requiredPermission={LAO_PTS_MASTERS_CREATE}>
                    <PTSCopyPage />
                  </ProtectedRoute>
                ),
                errorElement: <NotFound />,
              },
              {
                path: 'create',
                element: (
                  <ProtectedRoute requiredPermission={LAO_PTS_MASTERS_CREATE}>
                    <PTSCreatePage />
                  </ProtectedRoute>
                ),
                errorElement: <NotFound />,
              },
            ],
          },
          {
            path: 'job',
            element: (
              <ProtectedRoute requiredPermission={LAO_PTS_TASKS}>
                <JobPage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
          {
            path: 'airport',
            element: (
              <ProtectedRoute requiredPermission={LAO_AIRPORTS}>
                <AirportPage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
          {
            path: 'group-pts-task',
            element: (
              <ProtectedRoute requiredPermission={LAO_GROUP_PTS_TASKS}>
                <GroupTaskPTS />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
          {
            path: 'fleet',
            element: (
              <ProtectedRoute requiredPermission={LAO_FLEETS}>
                <FleetPage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
        ],
      },
      {
        path: '/report',
        children: [
          {
            path: 'flight-late',
            element: <ReportFlightPage />,
            errorElement: <NotFound />,
          },
          {
            path: 'department-late',
            element: <ReportDepartmentLatePage />,
            errorElement: <NotFound />,
          },
          {
            path: 'group-task-late',
            element: <ReportPTSGroupTaskLatePage />,
            errorElement: <NotFound />,
          },
          {
            path: 'report-on-time-gt',
            element: <ReportOnTimeGTPage />,
            errorElement: <NotFound />,
          },
          {
            path: 'report-late-gt',
            element: <ReportLateGTPage />,
            errorElement: <NotFound />,
          },
          {
            path: 'report-pts-task-late',
            element: <ReportPTSTaskLatePage />,
            errorElement: <NotFound />,
          },
          {
            path: 'report-no-record-pts',
            element: <ReportNoRecordPTSPage />,
            errorElement: <NotFound />,
          },
        ],
      },
      {
        path: '/administrator',
        children: [
          {
            path: 'user',
            element: (
              <ProtectedRoute requiredPermission={ABP_IDENTITY_USERS}>
                <UserPage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
          {
            path: 'role',
            element: (
              <ProtectedRoute requiredPermission={ABP_IDENTITY_ROLES}>
                <RolePage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
          {
            path: 'organization-unit',
            element: (
              <ProtectedRoute requiredPermission={LAO_DEPARTMENT}>
                <OrganizationUnitsPage />
              </ProtectedRoute>
            ),
            errorElement: <NotFound />,
          },
        ],
      },
      {
        path: '/403',
        element: <ForbiddenPage />,
      },
      {
        path: '*',
        element: <NotFound />,
      },
    ],
  },
  {
    path: '*',
    element: <NotFound />,
  },
]

export function AppRoutes() {
  const element = useRoutes(routes)
  return element
}

export default routes
