/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { selectFilterOption } from '@/src/helper/selectFilterOption'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getAirportByUser } from '@/src/service/airport'
import {
  closeModalSelectAirport,
  openModalSelectAirport,
  setAirportGlobal,
} from '@/src/store/GlobalSlice'
import { useQuery } from '@tanstack/react-query'
import { Button, Form, Modal, Select } from 'antd'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

const ModalSelectAirport = () => {
  const [form] = Form.useForm()
  const dispatch = useAppDispatch()
  const { visibleModalSelectAirport, airportGlobal } = useAppSelector(
    state => state.global
  )

  const { t } = useTranslation()

  const { data } = useQuery({
    queryKey: ['airport-list-by-user'],
    queryFn: () => getAirportByUser(),
  })

  const onSubmit = async () => {
    const values = await form.validateFields()
    dispatch(setAirportGlobal(values.airport))
    dispatch(closeModalSelectAirport())
  }

  useEffect(() => {
    if (!airportGlobal) {
      dispatch(openModalSelectAirport())
    }
  }, [airportGlobal])

  return (
    <Modal
      open={visibleModalSelectAirport}
      okButtonProps={{ style: { display: 'none' } }}
      cancelButtonProps={{ style: { display: 'none' } }}
      centered
      title={t('common.selectAirport')}
      width={400}
      onCancel={() => dispatch(closeModalSelectAirport())}
    >
      <Form form={form}>
        <Form.Item
          name="airport"
          rules={[{ required: true, message: t('common.selectAirport') }]}
        >
          <Select
            placeholder={t('common.selectAirport')}
            options={
              data?.map((item: any) => ({
                value: item.iataCode,
                label: item.iataCode,
              })) || []
            }
            showSearch
            filterOption={selectFilterOption}
          />
        </Form.Item>
      </Form>
      <Button type="primary" className="w-full" onClick={onSubmit}>
        {t('common.confirm')}
      </Button>
    </Modal>
  )
}

export default ModalSelectAirport
