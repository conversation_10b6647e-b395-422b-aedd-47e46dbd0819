/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/flights`

const getDepoInad = async (flightId: string, params?: any) => {
  const api = `${prefix}/${flightId}/depoinad`
  const response = await axios.get(api, { params })
  return response.data
}

const createDepoInad = async (body: any) => {
  const api = `${prefix}/${body.flightId}/depoinad`
  const response = await axios.post(api, body)
  return response.data
}

const updateDepoInad = async (body: any) => {
  const api = `${prefix}/${body.flightId}/depoinad/${body.id}`
  const response = await axios.put(api, body)
  return response.data
}

const removeDepoInad = async (id: string, flightId: string) => {
  const api = `${prefix}/${flightId}/depoinad/${id}`
  const response = await axios.delete(api)
  return response.data
}

const getDepoInadDetail = async (flightId: string, depoInadId: string) => {
  const api = `${prefix}/${flightId}/depoinad/${depoInadId}`
  const response = await axios.get(api)
  return response.data
}

const getDepoInadGroup = async () => {
  const api = `/api/v1/depo-inad-groups`
  const response = await axios.get(api)
  return response.data
}

export {
  getDepoInad,
  createDepoInad,
  updateDepoInad,
  removeDepoInad,
  getDepoInadDetail,
  getDepoInadGroup,
}
