/* eslint-disable react-hooks/exhaustive-deps */
import { ACCESS_TOKEN, REFRESH_TOKEN, TIME_EXPIRED } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import type { ILoginType } from '@/src/schema/ILoginType'
import { connectToken } from '@/src/service/auth'
import { setAccount } from '@/src/store/AuthSlice'
import { useMutation } from '@tanstack/react-query'
import {
  Button,
  Card,
  Checkbox,
  Form,
  Input,
  message,
  type FormProps,
} from 'antd'
import cookie from 'cookiejs'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

const LoginPage = () => {
  const navigate = useNavigate()

  const [form] = Form.useForm()

  const { t } = useTranslation()

  const dispatch = useAppDispatch()

  const { account } = useAppSelector(state => state.auth)

  const mutation = useMutation({
    mutationFn: (values: ILoginType) => connectToken(values),
    onSuccess: data => {
      const { access_token, refresh_token } = data

      if (!access_token || !refresh_token) {
        message.error(t('login.loginFailed'))
        return
      }

      const rememberMe = form.getFieldValue('rememberMe')
      handleTokenStorage(access_token, refresh_token, rememberMe)
      dispatch(setAccount({ email: '', pass: '' }))
      navigate('/')
    },
    onError: handleApiError,
  })

  const handleTokenStorage = (
    accessToken: string,
    refreshToken: string,
    rememberMe: boolean
  ) => {
    cookie.set(ACCESS_TOKEN, accessToken, { expires: TIME_EXPIRED })
    cookie.set(REFRESH_TOKEN, refreshToken, { expires: TIME_EXPIRED })
    localStorage.setItem('rememberMe', JSON.stringify(rememberMe))
  }

  const onFinish: FormProps<ILoginType>['onFinish'] = values => {
    mutation.mutate(values)
  }

  useEffect(() => {
    if (account) form.setFieldsValue(account)
  }, [])

  return (
    <Card className="w-3/5 mx-auto shadow-bottom min-w-[460px]">
      <div className="text-primary font-bold text-4xl text-center w-full pb-10">
        {t('login.login')}
      </div>
      <Form
        onFinish={onFinish}
        layout="vertical"
        form={form}
        className="text-primary"
        initialValues={{ rememberMe: true }}
      >
        <Form.Item
          label={t('login.username')}
          name="userNameOrEmailAddress"
          rules={[{ required: true, message: t('login.emailPlaceholder') }]}
        >
          <Input placeholder={t('login.emailPlaceholder')} />
        </Form.Item>
        <Form.Item
          label={t('login.password')}
          name="password"
          rules={[
            { required: true, message: t('login.passwordPlaceholder') },
            {
              min: 6,
              message: t('login.passwordRequired'),
            },
          ]}
        >
          <Input.Password placeholder={t('login.passwordPlaceholder')} />
        </Form.Item>
        <Form.Item
          name="rememberMe"
          valuePropName="checked"
          className="items-center flex gap-x-1"
        >
          <Checkbox className="">{t('login.rememberMe')}</Checkbox>
        </Form.Item>
        <Form.Item label={null} className="w-full">
          <Button
            type="primary"
            htmlType="submit"
            className="w-full uppercase !bg-primary"
            loading={mutation.isPending}
          >
            {t('login.login')}
          </Button>
        </Form.Item>
        <div className="flex items-center justify-center w-full mx-auto">
          OR
        </div>
        <Button
          className="w-full mt-4"
          onClick={() => {
            const values = form.getFieldsValue()
            dispatch(setAccount(values))
            navigate('/login-ldap')
          }}
        >
          {t('login.loginLdap')}
        </Button>
      </Form>
    </Card>
  )
}

export default LoginPage
