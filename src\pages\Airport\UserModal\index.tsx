/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { TIME_DEBOUNCE } from '@/src/constants'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import { addUserToAirport, getUserOnAirport } from '@/src/service/airport'
import { getUser } from '@/src/service/user'
import {
  closeUserModal,
  setSelectRowKeys,
  setShouldRefetchUserAirport,
} from '@/src/store/AirportSlice'
import {
  resetState,
  setKeyWordDebounce,
  setParams,
} from '@/src/store/UserSlice'
import { SearchOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Input, message, Modal, Table } from 'antd'
import type { TableProps } from 'antd/lib'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import { handleApiError } from '@/src/helper/handleApiError'
import ShowTotal from '@/src/components/Showtotal'

const UserModal = () => {
  const { t } = useTranslation()

  const dispatch = useAppDispatch()

  const { visibleUserModal, selectedAirportId, selectRowKeys } = useAppSelector(
    state => state.airport
  )

  const { params, KeyWordDebounce } = useAppSelector(state => state.user)

  const KeyWordSearchDebounce = useDebounce(KeyWordDebounce, TIME_DEBOUNCE)

  const { data: userAirportData } = useQuery({
    queryKey: ['airport-user-list', [selectedAirportId]],
    queryFn: () =>
      selectedAirportId &&
      getUserOnAirport(selectedAirportId, {
        MaxResultCount: 1000,
      }),
    enabled: !!selectedAirportId && visibleUserModal,
  })

  const mutation = useMutation({
    mutationFn: async (values: any) => {
      return selectedAirportId && addUserToAirport(selectedAirportId, values)
    },
    onSuccess: () => {
      dispatch(closeUserModal())
      message.success(t('airport.add_employee_success'))
      dispatch(setShouldRefetchUserAirport(true))
      dispatch(setSelectRowKeys([]))
      dispatch(setKeyWordDebounce(''))
      dispatch(resetState())
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const onSubmit = async () => {
    const selectedIds = selectRowKeys as string[]
    mutation.mutate(selectedIds)
  }

  const { data, isLoading } = useQuery({
    queryKey: ['user-airport-list', params],
    queryFn: () => getUser(params),
  })

  const columns: TableProps<any>['columns'] = [
    {
      title: t('airport.employee'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('airport.email'),
      dataIndex: 'email',
      key: 'email',
    },
  ]

  const rowSelection: TableProps<any>['rowSelection'] = {
    selectedRowKeys: selectRowKeys,
    onSelect: (record, selected) => {
      const key = record.id
      const newKeys = selected
        ? [...selectRowKeys, key]
        : selectRowKeys.filter(k => k !== key)

      dispatch(setSelectRowKeys(newKeys))
    },
    onSelectAll: (selected, _selectedRows, changeRows) => {
      const changeKeys = changeRows.map(row => row.id)
      const newKeys = selected
        ? Array.from(new Set([...selectRowKeys, ...changeKeys]))
        : selectRowKeys.filter(k => !changeKeys.includes(k))

      dispatch(setSelectRowKeys(newKeys))
    },
    getCheckboxProps(record) {
      return {
        disabled: userAirportData?.items.some(
          (user: any) => user.id === record.id
        ),
      }
    },
  }

  useEffect(() => {
    dispatch(
      setParams({ ...params, Filter: KeyWordSearchDebounce, SkipCount: 0 })
    )
  }, [KeyWordSearchDebounce])

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <Modal
      width={1000}
      open={visibleUserModal}
      title={t('airport.employee_list')}
      onOk={onSubmit}
      onCancel={() => {
        dispatch(closeUserModal())
        dispatch(setSelectRowKeys([]))
        dispatch(setKeyWordDebounce(''))
        dispatch(resetState())
      }}
      okText={t('common.confirm')}
      cancelText={t('common.cancel')}
    >
      <Table
        rowSelection={rowSelection}
        size="small"
        rowKey={'id'}
        title={() => (
          <div className="flex justify-end items-center gap-x-4">
            <Input
              value={KeyWordDebounce}
              className="!w-44"
              prefix={<SearchOutlined />}
              placeholder={t('airport.search_by_employee_name')}
              onChange={e => {
                dispatch(setKeyWordDebounce(e.target.value))
              }}
            />
          </div>
        )}
        footer={() => (
          <div className="flex justify-end items-center gap-x-4">
            <div className="text-sm text-gray-600 text-right">
              {t('common.selected')}: {selectRowKeys.length}
            </div>
          </div>
        )}
        columns={columns}
        bordered
        className={`${styles.whiteHeader}`}
        dataSource={data?.items || []}
        loading={isLoading}
        pagination={{
          total: isLoading ? 0 : data?.totalCount,
          current: params.SkipCount / params.MaxResultCount + 1,
          pageSize: params.MaxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                SkipCount: (page - 1) * pageSize,
                MaxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
    </Modal>
  )
}

export default UserModal
