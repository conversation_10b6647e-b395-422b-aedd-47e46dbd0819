/* eslint-disable @typescript-eslint/no-explicit-any */
import { normalizeText } from '@/src/helper/normalizeText'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { exportExcelReportOrganizationUnitLateDetail } from '@/src/service/report'
import { closeModalDepartment } from '@/src/store/ReportSlice'
import { DownloadOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { Button, Modal, Table } from 'antd'
import type { TableProps } from 'antd/lib'
import FileSaver from 'file-saver'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import { convertTime } from '@/src/helper/convertTime'

const ModalDepartment = () => {
  const { t } = useTranslation()

  const dispatch = useAppDispatch()

  const { visibleModalDepartment, selectedUnit, paramDepartmentDetail } =
    useAppSelector(state => state.report)

  const [params, setParams] = useState({
    // ...paramDepartmentDetail,
    skipCount: 0,
  })

  const { refetch: exportExcelReport, isLoading: isLoadingExport } = useQuery({
    queryKey: ['report-unit-late-detail', params],
    queryFn: () =>
      exportExcelReportOrganizationUnitLateDetail(
        selectedUnit?.organizationId,
        selectedUnit?.groupPtsTaskId,
        {
          ...params,
          skipCount: 0,
        }
      ),
    enabled: false,
  })

  const handleExport = async () => {
    const { data } = await exportExcelReport()
    const fileName = `${t('common.reportName')}_${normalizeText(t('report.reportDepartmentLate'))}.csv`
    return FileSaver.saveAs(data, fileName)
  }

  const columns: TableProps<any>['columns'] = [
    {
      title: t('table.order'),
      width: '10%',
      dataIndex: 'key',
      align: 'center',
      render: (_value, _record, index) => index + 1,
    },
    {
      title: t('table.flightNo'),
      width: '10%',
      dataIndex: '',
      align: 'center',
      render: (_value, record) => record.fnCarrier + record.fnNumber,
    },
    {
      title: t('table.totalTimeDelay'),
      width: '20%',
      dataIndex: 'lateDuration',
      align: 'center',
      render: time => <>{convertTime(time)}</>,
    },
  ]

  useEffect(() => {
    setParams({
      ...paramDepartmentDetail,
      skipCount: 0,
    })
  }, [paramDepartmentDetail])

  return (
    <Modal
      open={visibleModalDepartment && selectedUnit !== null}
      width={1000}
      closable={false}
      onCancel={() => {
        dispatch(closeModalDepartment())
        setParams({
          ...paramDepartmentDetail,
          skipCount: 0,
        })
      }}
      cancelText={t('common.close')}
      okButtonProps={{ style: { display: 'none' } }}
    >
      <div className="flex w-full justify-between items-center mb-4 text-base">
        <div className="flex">
          <div>
            {t('table.department')}:&nbsp;
            <span>{selectedUnit?.organizationName}</span>
          </div>
          &nbsp; - &nbsp;
          <div>
            {t('table.ptsGroupTask')}:&nbsp;
            <span>{selectedUnit?.groupPtsTaskName}</span>
          </div>
        </div>
        <Button
          loading={isLoadingExport}
          onClick={handleExport}
          icon={<DownloadOutlined />}
          disabled={isLoadingExport}
        >
          {t('report.download')}
        </Button>
      </div>
      <Table
        bordered
        size="small"
        rowKey={record => record.id}
        className={`${styles.whiteHeader}`}
        dataSource={selectedUnit?.details || []}
        columns={columns}
        pagination={{
          pageSize: 10,
          current: params.skipCount / 10 + 1,
          onChange: (page, pageSize) => {
            setParams({
              ...params,
              skipCount: (page - 1) * pageSize,
            })
          },
        }}
        //pagination={{
        //   pageSize: paramReportDepartment.maxResultCount,
        //   current:
        //     paramReportDepartment.skipCount /
        //       paramReportDepartment.maxResultCount +
        //     1,
        //   total: 10,
        //   showSizeChanger: true,
        //   onChange(page, pageSize) {
        //     dispatch(
        //       setParamsModalDepartment({
        //         ...paramReportDepartment,
        //         skipCount: (page - 1) * pageSize,
        //         maxResultCount: pageSize,
        //       })
        //     )
        //   },
        //   showTotal: (total, range) => (
        //     <ShowTotal total={total} range={range} />
        //   ),
        // }}
      />
    </Modal>
  )
}

export default ModalDepartment
