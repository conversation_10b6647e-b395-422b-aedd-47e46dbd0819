/* eslint-disable @typescript-eslint/no-explicit-any */
export const selectFilterOption = (input: string, option: any) => {
  const label =
    typeof option?.label === 'string'
      ? option.label.toLowerCase()
      : String(option?.label).toLowerCase()
  return label.includes(input.toLowerCase())
}

export const selectFilterValueOption = (input: string, option: any) => {
  const label =
    typeof option?.value === 'string'
      ? option.value.toLowerCase()
      : String(option?.value).toLowerCase()
  return label.includes(input.toLowerCase())
}
