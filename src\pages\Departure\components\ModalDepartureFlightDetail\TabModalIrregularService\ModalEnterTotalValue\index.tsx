/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { colors } from '@/src/constants/colors'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  getIrregularServiceDetail,
  postIrregularService,
} from '@/src/service/irregular-service'
import { closeModalEnterTotalValue } from '@/src/store/ModalDepartureFlightSlice'
import { MinusSquareOutlined, PlusSquareOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  // Checkbox,
  Collapse,
  Form,
  Input,
  InputNumber,
  Modal,
  type CollapseProps,
} from 'antd'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export const Restaurant = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item
        label="Breakfast"
        name={['restaurant', 'breakfastPaxs']}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
      >
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>
      <Form.Item
        label="Σ="
        name={['restaurant', 'breakfastAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>
      <Form.Item
        label="Lunch/Dinner"
        name={['restaurant', 'lunchDinnerPaxs']}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
      >
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>
      <Form.Item
        label="Σ="
        name={['restaurant', 'lunchDinnerAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>
      <Form.Item
        label="Beverage"
        name={['restaurant', 'beveragePaxs']}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
      >
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>
      <Form.Item
        label="Σ="
        name={['restaurant', 'beverageAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>
      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['restaurant', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const CabinCrew = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item
        name={['cabinCrew', 'mealAndBeverage']}
        wrapperCol={{ span: 18 }}
      >
        <Input addonAfter="(meal & beverage)" readOnly={readOnly} />
      </Form.Item>
      <div />

      <Form.Item name={['cabinCrew', 'serials']}>
        <Input addonAfter="(serials)" readOnly={readOnly} />
      </Form.Item>
      <div />

      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['cabinCrew', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const Taxi = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item name={['taxi', 'paxs']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['taxi', 'paxsAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item name={['taxi', 'vouchers']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(vouchers)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['taxi', 'voucherAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['taxi', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const Hotel = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item name={['hotel', 'paxs']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['hotel', 'paxsAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item name={['hotel', 'vouchers']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(vouchers)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['hotel', 'voucherAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['hotel', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const Offload = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item name={['offload', 'paxsVnd']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['offload', 'amountVnd']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item name={['offload', 'paxsUsd']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['offload', 'amountUsd']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          addonAfter="(usd)"
          min={0}
          readOnly={readOnly}
          formatter={value => {
            if (!value) return ''
            return new Intl.NumberFormat('de-DE').format(Number(value))
          }}
          parser={(value: any) => {
            if (!value) return ''
            return value.replace(/\./g, '').replace(',', '.')
          }}
        />
      </Form.Item>

      <Form.Item name={['offload', 'paxsMco']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['offload', 'amountMco']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber addonAfter="(mco/e-voucher)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item name={['offload', 'serials']} wrapperCol={{ span: 24 }}>
        <Input addonAfter="(serials)" readOnly={readOnly} />
      </Form.Item>

      <div />

      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['offload', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const IFE = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item name={['ife', 'paxsVnd']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['ife', 'amountVnd']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item name={['ife', 'paxsUsd']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['ife', 'amountUsd']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          addonAfter="(usd)"
          min={0}
          readOnly={readOnly}
          formatter={value => {
            if (!value) return ''
            return new Intl.NumberFormat('de-DE').format(Number(value))
          }}
          parser={(value: any) => {
            if (!value) return ''
            return value.replace(/\./g, '').replace(',', '.')
          }}
        />
      </Form.Item>

      <Form.Item name={['ife', 'paxsMco']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['ife', 'amountMco']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber addonAfter="(mco/e-voucher)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item name={['ife', 'serials']} wrapperCol={{ span: 24 }}>
        <Input addonAfter="(serials)" readOnly={readOnly} />
      </Form.Item>

      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['ife', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const FIM = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item name={['fim', 'paxsJ']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="J (paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item name={['fim', 'paxsY']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="Y (paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item name={['fim', 'serials']} wrapperCol={{ span: 24 }}>
        <Input addonAfter="(serials)" readOnly={readOnly} />
      </Form.Item>
      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['fim', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const NewTickets = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item name={['newTicket', 'paxsVnd']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['newTicket', 'amountVnd']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item name={['newTicket', 'paxsUsd']} wrapperCol={{ span: 12 }}>
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['newTicket', 'amountUsd']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(usd)"
          min={0}
          formatter={value => {
            if (!value) return ''
            return new Intl.NumberFormat('de-DE').format(Number(value))
          }}
          parser={(value: any) => {
            if (!value) return ''
            return value.replace(/\./g, '').replace(',', '.')
          }}
        />
      </Form.Item>

      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['newTicket', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const CDM = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item
        name={['cdmCompensation', 'totalPaxs']}
        wrapperCol={{ span: 12 }}
      >
        <InputNumber addonAfter="total paxs" min={0} readOnly={readOnly} />
      </Form.Item>

      <div />

      <Form.Item
        name={['cdmCompensation', 'paxsVnd']}
        wrapperCol={{ span: 12 }}
      >
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['cdmCompensation', 'amountVnd']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item
        name={['cdmCompensation', 'paxsUsd']}
        wrapperCol={{ span: 12 }}
      >
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['cdmCompensation', 'amountUsd']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(usd)"
          formatter={value => {
            if (!value) return ''
            return new Intl.NumberFormat('de-DE').format(Number(value))
          }}
          parser={(value: any) => {
            if (!value) return ''
            return value.replace(/\./g, '').replace(',', '.')
          }}
        />
      </Form.Item>

      <Form.Item
        name={['cdmCompensation', 'paxsNcf']}
        wrapperCol={{ span: 12 }}
      >
        <InputNumber addonAfter="(paxs)" min={0} readOnly={readOnly} />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['cdmCompensation', 'amountNcf']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(ncf)"
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <div />

      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['cdmCompensation', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const Other = ({ readOnly = false }: { readOnly?: boolean }) => {
  return (
    <div className="grid grid-cols-2 gap-x-20 pl-10">
      <Form.Item name={['other', 'groundTransportationAssistance']}>
        <InputNumber
          addonAfter="Ground Transportation Assistance"
          min={0}
          readOnly={readOnly}
        />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['other', 'groundTransportationAssistanceAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item name={['other', 'flightScheduleChangeCompensation']}>
        <InputNumber
          addonAfter="Flight Schedule Change Compensation"
          min={0}
          readOnly={readOnly}
        />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['other', 'flightScheduleChangeCompensationAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item name={['other', 'goodwillCompensation']}>
        <InputNumber
          addonAfter="Goodwill Compensation"
          min={0}
          readOnly={readOnly}
        />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['other', 'goodwillCompensationAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      <Form.Item name={['other', 'otherCompensationExpenses']}>
        <InputNumber
          addonAfter="Other Compensation Expenses"
          min={0}
          readOnly={readOnly}
        />
      </Form.Item>

      <Form.Item
        label="Σ="
        name={['other', 'otherCompensationExpensesAmount']}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 16 }}
      >
        <InputNumber
          readOnly={readOnly}
          addonAfter="(vnd)"
          min={0}
          formatter={value =>
            new Intl.NumberFormat('vi-VN').format(Number(value))
          }
          parser={value =>
            value?.replace(/\s?₫|\./g, '').replace(/,/g, '') as any
          }
        />
      </Form.Item>

      {/* <Form.Item
          className="col-start-2 place-self-end"
          name={['other', 'export']}
        >
          <Checkbox>Export</Checkbox>
        </Form.Item> */}
    </div>
  )
}

export const ModalEnterTotalValue = () => {
  const [activeKey, setActiveKey] = useState<string[]>(['restaurant'])

  const { visibleModalEnterTotalValue, selectFlightModalId } = useAppSelector(
    state => state.modalDepartureFlight
  )
  const dispatch = useAppDispatch()
  const { t } = useTranslation()
  const [form] = Form.useForm()

  const { data, refetch } = useQuery({
    queryKey: ['irregular-service', selectFlightModalId],
    queryFn: () => getIrregularServiceDetail(selectFlightModalId as string),
    enabled: !!selectFlightModalId,
  })

  const mutate = useMutation({
    mutationFn: (values: any) => {
      return postIrregularService(selectFlightModalId as string, {
        restaurant: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.restaurant,
          ...values.restaurant,
        },
        cabinCrew: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.cabinCrew,
          ...values.cabinCrew,
        },
        taxi: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.taxi,
          ...values.taxi,
        },
        hotel: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.hotel,
          ...values.hotel,
        },
        offload: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.offload,
          ...values.offload,
        },
        ife: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.ife,
          ...values.ife,
        },
        fim: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.fim,
          ...values.fim,
        },
        newTicket: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.newTicket,
          ...values.newTicket,
        },
        cdmCompensation: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.cdmCompensation,
          ...values.cdmCompensation,
        },
        other: {
          id: '00000000-0000-0000-0000-000000000000',
          ...data.other,
          ...values.other,
        },
      })
    },
    onSuccess() {
      dispatch(closeModalEnterTotalValue())
      form.resetFields()
    },
    onError(error) {
      handleApiError(error)
    },
  })

  const onSubmit = async (values: any) => {
    await form.validateFields()
    await mutate.mutateAsync(values)
    setActiveKey([])
    refetch()
  }

  const items: CollapseProps['items'] = [
    {
      key: 'restaurant',
      label: 'Restaurant',
      children: <Restaurant />,
    },
    {
      key: 'cabin_crew',
      label: 'Cabin crew',
      children: <CabinCrew />,
    },
    {
      key: 'taxi',
      label: 'Taxi',
      children: <Taxi />,
    },
    {
      key: 'hotel',
      label: 'Hotel',
      children: <Hotel />,
    },
    {
      key: 'offload',
      label: 'Offload',
      children: <Offload />,
    },
    {
      key: 'ife',
      label: 'IFE',
      children: <IFE />,
    },
    {
      key: 'fim',
      label: 'FIM',
      children: <FIM />,
    },
    {
      key: 'new_tickets',
      label: 'New tickets',
      children: <NewTickets />,
    },
    {
      key: 'cdm',
      label: 'C.D.M compensation (Bồi thường ứng trước không hoàn lại)',
      children: <CDM />,
    },
    {
      key: 'other',
      label: 'Other',
      children: <Other />,
    },
  ]

  useEffect(() => {
    if (visibleModalEnterTotalValue && data) {
      form.setFieldsValue(data)
    }
  }, [data, visibleModalEnterTotalValue])

  return (
    <Modal
      width={1000}
      title={data ? 'Update irregular service' : 'Create irregular service'}
      open={visibleModalEnterTotalValue}
      footer={null}
      onCancel={() => {
        dispatch(closeModalEnterTotalValue())
        setActiveKey([])
        form.resetFields()
      }}
    >
      <Form
        form={form}
        className="gap-y-4 flex flex-col"
        onFinish={onSubmit}
        colon={false}
        labelAlign="left"
      >
        <Collapse
          items={items}
          collapsible="icon"
          expandIconPosition="end"
          activeKey={activeKey}
          onChange={keys => setActiveKey(keys as string[])}
          expandIcon={({ isActive }) => (
            <span className="ml-auto">
              {isActive ? (
                <MinusSquareOutlined
                  style={{ fontSize: 16, color: colors.primary }}
                />
              ) : (
                <PlusSquareOutlined
                  style={{ fontSize: 16, color: colors.primary }}
                />
              )}
            </span>
          )}
        />
        <div className="flex justify-end">
          <div className="flex gap-x-2">
            {/* <Button type="primary">{t('common.export')}</Button> */}
            <Form.Item>
              <Button htmlType="submit" type="primary">
                {t('common.save')}
              </Button>
            </Form.Item>
          </div>
        </div>
      </Form>
    </Modal>
  )
}
