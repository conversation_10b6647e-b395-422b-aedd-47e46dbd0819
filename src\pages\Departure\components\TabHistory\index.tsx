import { EditFilled } from '@ant-design/icons'
import { Button, Table } from 'antd'
import styles from './index.module.scss'

const TabHistory = () => {
  const columns = [
    { title: 'Date', width: '10%' },
    { title: 'Update by', width: '10%' },
    { title: 'Action', width: '5%' },
    { title: 'Content', width: '30%' },
    { title: 'Updated reason', width: '30%' },
    { title: '#', width: '30%' },
  ]

  return (
    <div className="w-full py-3 bg-[#F5F9FA] gap-y-4 flex flex-col">
      <div className="flex w-full justify-between">
        <div className="text-lg font-bold"> </div>
        <Button
          className="!hidden"
          icon={<EditFilled />}
          type="primary"
          onClick={() => {}}
        >
          Chỉnh sửa
        </Button>
      </div>
      <Table bordered columns={columns} className={`${styles.whiteHeader}`} />
    </div>
  )
}

export default TabHistory
