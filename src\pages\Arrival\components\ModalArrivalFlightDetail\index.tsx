import { colors } from '@/src/constants/colors'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  closeModalArrivalFlightDetail,
  setTabName,
} from '@/src/store/ModalArrivalFlightSlice'
import { ConfigProvider, Form, Modal, Tabs } from 'antd'
import { lazy, Suspense } from 'react'
import styles from './index.module.scss'
const TabModalBriefing = lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalBriefing'
    )
)
const TabModalDEPOINAD = lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalDEPOINAD'
    )
)
const TabModalFlightInfo = lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalFlightInfo'
    )
)
const TabModalHistory = lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalHistory'
    )
)
const TabModalReasons = lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalReasons'
    )
)
const TabModalUnusualInfo = lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalUnusualInfo'
    )
)

const TabModalPassenger = lazy(
  () =>
    import(
      '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalPassenger'
    )
)
const TabModalGD = lazy(
  () =>
    import('@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalGD')
)

const ModalArrivalFlightDetail = () => {
  const [form] = Form.useForm()

  const dispatch = useAppDispatch()

  const { tabName, visibleModalArrivalFlightDetail } = useAppSelector(
    state => state.modalArrivalFlight
  )

  const items = [
    {
      key: 'flight_info',
      label: 'Flight Info',
      children: (
        <Suspense>
          <TabModalFlightInfo />
        </Suspense>
      ),
    },
    // {
    //   key: 'ac_tech_info',
    //   label: 'AC Tech Info',
    //   children: <TabModalACTechInfo />,
    // },
    {
      key: 'pax-info',
      label: 'Pax Info',
      children: (
        <Suspense>
          <TabModalPassenger />
        </Suspense>
      ),
    },
    // {
    //   key: 'meal_order',
    //   label: 'Meal Order',
    //   children: <TabModalMealOrder />,
    // },
    {
      key: 'depo_inad',
      label: 'DEPO INAD',
      children: (
        <Suspense>
          <TabModalDEPOINAD />
        </Suspense>
      ),
    },
    // {
    //   key: 'gd',
    //   label: 'GD',
    //   children: <TabModalGD />,
    // },
    // {
    //   key: 'depo_inad',
    //   label: 'DEPO INAD',
    //   children: (
    //     <Suspense>
    //       <TabModalDEPOINAD />
    //     </Suspense>
    //   ),
    // },
    {
      key: 'gd',
      label: 'GD',
      children: (
        <Suspense>
          <TabModalGD />
        </Suspense>
      ),
    },
    {
      key: 'reasons',
      label: 'Reasons',
      children: (
        <Suspense>
          <TabModalReasons />
        </Suspense>
      ),
    },
    {
      key: 'unusual_info',
      label: 'Unusual Info',
      children: (
        <Suspense>
          <TabModalUnusualInfo />
        </Suspense>
      ),
    },
    {
      key: 'briefing',
      label: 'Briefing',
      children: (
        <Suspense>
          <TabModalBriefing />
        </Suspense>
      ),
    },
    {
      key: 'history',
      label: 'History',
      children: (
        <Suspense>
          <TabModalHistory />
        </Suspense>
      ),
    },
    // {
    //   key: 'ground_service',
    //   label: 'Ground Service',
    //   children: <TabGroundService />,
    // },
    // {
    //   key: 'irregular_service',
    //   label: 'Irregular Service',
    //   children: <TabModalIrregularService />,
    // },

    // {
    //   key: 'unusual_pax',
    //   label: 'Unusual Pax',
    //   children: <TabModalUnusualPax />,
    // },
    // {
    //   key: 'unusual_luggage',
    //   label: 'Unusual Luggage',
    //   children: <TabModalUnusualLuggage />,
    // },
    // {
    //   key: 'sla_evaluation',
    //   label: 'SLA Evaluation',
    //   children: <TabModalSLAEvaluation />,
    // },
  ]

  return (
    <Modal
      open={visibleModalArrivalFlightDetail}
      title={`${tabName}`}
      width={1200}
      centered
      okButtonProps={{ style: { display: 'none' } }}
      cancelButtonProps={{ style: { display: 'none' } }}
      onCancel={() => dispatch(closeModalArrivalFlightDetail())}
      className="h-screen"
    >
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              itemActiveBg: colors.primary,
              itemSelectedBg: colors.primary,
              itemSelectedColor: '#fff',
              itemBorderRadius: 4,
              itemDisabledColor: colors.white,
              subMenuItemSelectedColor: colors.white,
              itemMarginBlock: 8,
            },
          },
        }}
      >
        <Form layout="vertical" form={form}>
          <ConfigProvider>
            <Tabs
              tabPosition="left"
              items={items}
              defaultActiveKey="sub1"
              className={`${styles.custom_tabs}`}
              onTabClick={key => {
                const tab = items.find(item => item.key === key)
                dispatch(setTabName(tab?.label))
              }}
            />
          </ConfigProvider>
        </Form>
      </ConfigProvider>
    </Modal>
  )
}

export default ModalArrivalFlightDetail
