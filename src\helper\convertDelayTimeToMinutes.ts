/* eslint-disable @typescript-eslint/no-explicit-any */
export const convertDelayTimeToMinutes = (
  delayTime: any
): number | undefined => {
  if (!delayTime) return undefined

  if (typeof delayTime === 'string') {
    const [hStr, mStr] = delayTime.split(':')
    const h = Number(hStr)
    const m = Number(mStr)
    if (isNaN(h) || isNaN(m)) return undefined
    return h * 60 + m
  }

  // Assuming it's a moment/dayjs object with hour() and minute() methods
  if (
    typeof delayTime.hour === 'function' &&
    typeof delayTime.minute === 'function'
  ) {
    const h = delayTime.hour()
    const m = delayTime.minute()
    if (typeof h !== 'number' || typeof m !== 'number') return undefined
    return h * 60 + m
  }

  return undefined
}
