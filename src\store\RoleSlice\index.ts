/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

type RoleState = {
  visibleRoleModal: boolean
  selectedRoleId: string | null
  params: {
    SkipCount: number
    MaxResultCount: number
  }
  visibleRolePermissionModal: boolean
  selectedRoleName: string | null
  permissions: Record<string, boolean>
}

const initialState: RoleState = {
  visibleRoleModal: false,
  selectedRoleId: null,
  params: {
    SkipCount: 0,
    MaxResultCount: 20,
  },
  visibleRolePermissionModal: false,
  selectedRoleName: null,
  permissions: {},
}

const getChildrenKeys = (
  permissions: Record<string, boolean>,
  parentKey: string
) => {
  return Object.keys(permissions).filter(key => key.startsWith(parentKey + '.'))
}

const roleSlice = createSlice({
  name: 'role',
  initialState,
  reducers: {
    openRoleModal(state) {
      state.visibleRoleModal = true
    },
    closeRoleModal(state) {
      state.visibleRoleModal = false
    },
    setParams(state, action: PayloadAction<any>) {
      state.params = { ...state.params, ...action.payload }
    },
    setSelectedRoleId(state, action: PayloadAction<string>) {
      state.selectedRoleId = action.payload
    },
    openRolePermissionModal(state) {
      state.visibleRolePermissionModal = true
    },
    closeRolePermissionModal(state) {
      state.visibleRolePermissionModal = false
    },
    setSelectedRoleName(state, action: PayloadAction<string>) {
      state.selectedRoleName = action.payload
    },

    setPermissionsFromApi(
      state,
      action: PayloadAction<{
        permissions: { name: string; isGranted: boolean }[]
      }>
    ) {
      const map: Record<string, boolean> = {}
      action.payload.permissions.forEach(p => {
        map[p.name] = p.isGranted
      })
      state.permissions = map
    },

    setPermission(
      state,
      action: PayloadAction<{ key: string; value: boolean }>
    ) {
      const { key, value } = action.payload

      if (key in state.permissions) {
        state.permissions[key] = value
      }

      const childrenKeys = getChildrenKeys(state.permissions, key)
      childrenKeys.forEach(childKey => {
        state.permissions[childKey] = value
      })
    },

    setGroupPermissions(
      state,
      action: PayloadAction<{ group: string; value: boolean }>
    ) {
      Object.keys(state.permissions).forEach(key => {
        if (key.startsWith(action.payload.group + '.')) {
          state.permissions[key] = action.payload.value
        }
      })
    },

    setAllPermissions(state, action: PayloadAction<boolean>) {
      Object.keys(state.permissions).forEach(key => {
        state.permissions[key] = action.payload
      })
    },

    resetState() {
      return initialState
    },
  },
})

export const {
  openRoleModal,
  closeRoleModal,
  setParams,
  setSelectedRoleId,
  openRolePermissionModal,
  closeRolePermissionModal,
  setSelectedRoleName,
  setPermissionsFromApi,
  setPermission,
  setGroupPermissions,
  setAllPermissions,
  resetState,
} = roleSlice.actions

export default roleSlice.reducer
