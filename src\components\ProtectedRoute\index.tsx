import { ACCESS_TOKEN } from '@/src/constants'
import usePermission from '@/src/hooks/usePermission'
import { Navigate } from 'react-router'
import cookie from 'cookiejs'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string | string[]
}

const ProtectedRoute = ({
  children,
  requiredPermission,
}: ProtectedRouteProps) => {
  const { hasPermission } = usePermission()

  const access_token =
    cookie.get(ACCESS_TOKEN) || sessionStorage.getItem(ACCESS_TOKEN)

  if (!access_token) {
    return <Navigate to="/login-ldap" replace />
  }

  if (!requiredPermission) {
    return <>{children}</>
  }

  const hasAccess = hasPermission(requiredPermission)

  if (!hasAccess) {
    return <Navigate to="/403" replace />
  }

  return <>{children}</>
}

export default ProtectedRoute
