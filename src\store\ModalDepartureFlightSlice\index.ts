import { createSlice } from '@reduxjs/toolkit'

type ModalFlightState = {
  visibleModalDepartureFlightDetail: boolean
  selectedFlightId: string | null
  selectedUnusualInfoId: string | null
  selectedReasonId: string | null
  tabName: string
  visibleModalReasonDetail: boolean
  visibleModalFTH: boolean
  visibleModalGroundServiceReportDetail: boolean
  visibleModalFlightBriefingInformation: boolean
  visibleModalBriefingResponses: boolean
  visibleModalEnterTotalValue: boolean
  selectFlightModalId: string | null
  visibleModalArrivalFlightDetail: boolean
  visibleModalDepoInadDetail: boolean
  visibleModalGroundService: boolean
  visibleModalFlight: boolean
  visibleModalRamps: boolean
  visibleModalUnusualInfoDetail: boolean
  selectedBriefingId: string | null
  paramsHistory: {
    skipCount: number
    maxResultCount: number
  }
  paramsMealOrder: {
    skipCount: number
    maxResultCount: number
  }
  paramsGd: {
    skipCount: number
    maxResultCount: number
  }
}

const initialState: ModalFlightState = {
  visibleModalDepartureFlightDetail: false,
  selectedFlightId: null,
  selectedUnusualInfoId: null,
  selectedReasonId: null,
  tabName: 'Flight Info',
  visibleModalReasonDetail: false,
  visibleModalFTH: false,
  visibleModalGroundServiceReportDetail: false,
  visibleModalFlightBriefingInformation: false,
  visibleModalBriefingResponses: false,
  visibleModalEnterTotalValue: false,
  selectFlightModalId: null,
  visibleModalArrivalFlightDetail: false,
  visibleModalDepoInadDetail: false,
  visibleModalGroundService: false,
  visibleModalFlight: false,
  visibleModalRamps: false,
  visibleModalUnusualInfoDetail: false,
  selectedBriefingId: null,
  paramsHistory: {
    skipCount: 0,
    maxResultCount: 20,
  },
  paramsMealOrder: {
    skipCount: 0,
    maxResultCount: 20,
  },
  paramsGd: {
    skipCount: 0,
    maxResultCount: 20,
  },
}

const modalDepartureFlightSlice = createSlice({
  name: 'modalDepartureFlight',
  initialState,
  reducers: {
    openModalDepartureFlightDetail(state) {
      state.visibleModalDepartureFlightDetail = true
    },
    closeModalFlightDetail(state) {
      state.visibleModalDepartureFlightDetail = false
    },
    setTabName(state, action) {
      state.tabName = action.payload
    },
    openModalReasonDetail(state) {
      state.visibleModalReasonDetail = true
    },
    closeModalReasonDetail(state) {
      state.visibleModalReasonDetail = false
    },
    openModalFTH(state) {
      state.visibleModalFTH = true
    },
    closeModalFTH(state) {
      state.visibleModalFTH = false
    },
    openModalGroundServiceReportDetail: state => {
      state.visibleModalGroundServiceReportDetail = true
    },
    closeModalGroundServiceReportDetail: state => {
      state.visibleModalGroundServiceReportDetail = false
    },
    openModalFlightBriefingInformation: state => {
      state.visibleModalFlightBriefingInformation = true
    },
    closeModalFlightBriefingInformation: state => {
      state.visibleModalFlightBriefingInformation = false
    },
    openModalBriefingResponses: state => {
      state.visibleModalBriefingResponses = true
    },
    closeModalBriefingResponses: state => {
      state.visibleModalBriefingResponses = false
    },
    openModalEnterTotalValue: state => {
      state.visibleModalEnterTotalValue = true
    },
    closeModalEnterTotalValue: state => {
      state.visibleModalEnterTotalValue = false
    },
    setSelectedFlightModalId: (state, action) => {
      state.selectFlightModalId = action.payload
    },
    openModalArrivalFlightDetail: state => {
      state.visibleModalArrivalFlightDetail = true
    },
    closeModalArrivalFlightDetail: state => {
      state.visibleModalArrivalFlightDetail = false
    },
    openModalDepoInadDetail: state => {
      state.visibleModalDepoInadDetail = true
    },
    closeModalDepoInadDetail: state => {
      state.visibleModalDepoInadDetail = false
    },
    openModalGroundService: state => {
      state.visibleModalGroundService = true
    },
    closeModalGroundService: state => {
      state.visibleModalGroundService = false
    },
    openModalFlight: state => {
      state.visibleModalFlight = true
    },
    closeModalFlight: state => {
      state.visibleModalFlight = false
    },
    openModalRamps: state => {
      state.visibleModalRamps = true
    },
    closeModalRamps: state => {
      state.visibleModalRamps = false
    },
    setSelectedUnusualInfoId: (state, action) => {
      state.selectedUnusualInfoId = action.payload
    },
    setSelectedReasonId: (state, action) => {
      state.selectedReasonId = action.payload
    },
    setSelectedBriefingId: (state, action) => {
      state.selectedBriefingId = action.payload
    },
    setParamsHistory: (state, action) => {
      state.paramsHistory = action.payload
    },
    setParamsMealOrder: (state, action) => {
      state.paramsMealOrder = action.payload
    },
    setParamsGd: (state, action) => {
      state.paramsGd = action.payload
    },
  },
})

export const {
  openModalDepartureFlightDetail,
  closeModalFlightDetail,
  setTabName,
  openModalReasonDetail,
  closeModalReasonDetail,
  openModalFTH,
  closeModalFTH,
  openModalGroundServiceReportDetail,
  closeModalGroundServiceReportDetail,
  openModalFlightBriefingInformation,
  closeModalFlightBriefingInformation,
  openModalBriefingResponses,
  closeModalBriefingResponses,
  openModalEnterTotalValue,
  closeModalEnterTotalValue,
  setSelectedFlightModalId,
  openModalArrivalFlightDetail,
  closeModalArrivalFlightDetail,
  openModalDepoInadDetail,
  closeModalDepoInadDetail,
  openModalGroundService,
  closeModalGroundService,
  openModalFlight,
  closeModalFlight,
  openModalRamps,
  closeModalRamps,
  setSelectedUnusualInfoId,
  setSelectedReasonId,
  setSelectedBriefingId,
  setParamsHistory,
  setParamsMealOrder,
  setParamsGd,
} = modalDepartureFlightSlice.actions

export default modalDepartureFlightSlice.reducer
