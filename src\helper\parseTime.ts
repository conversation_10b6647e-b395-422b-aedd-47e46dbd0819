const parseTimeSpan = (timespan?: string): { h: number; m: number } => {
  if (!timespan) return { h: 0, m: 0 }

  let days = 0
  let hms = timespan

  // <PERSON><PERSON>u có dạng "1.06:10:00"
  if (timespan.includes('.')) {
    const [d, rest] = timespan.split('.')
    days = Number(d) || 0
    hms = rest || '00:00:00'
  }

  const parts = hms.split(':')
  if (parts.length < 2) return { h: 0, m: 0 }

  const [h, m] = parts.map(Number)
  const totalHours = (h ?? 0) + days * 24

  return { h: totalHours, m: m ?? 0 }
}

export default parseTimeSpan
