/* eslint-disable @typescript-eslint/no-explicit-any */
import { ISO_DATETIME } from '@/src/constants'
import { createSlice } from '@reduxjs/toolkit'
import dayjs from 'dayjs'

type DashboardState = {
  params: {
    airport?: string
    fromDate?: any
    toDate?: any
  }
  limit: number
}

const initialState: DashboardState = {
  params: {
    airport: '',
    fromDate: dayjs().startOf('day').format(ISO_DATETIME),
    toDate: dayjs().endOf('day').format(ISO_DATETIME),
  },
  limit: 3,
}

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    setParams(state, action) {
      state.params = { ...state.params, ...action.payload }
    },
    resetState() {
      return initialState
    },
    setLimit(state, action) {
      state.limit = action.payload
    },
  },
})

export const { setParams, resetState, setLimit } = dashboardSlice.actions
export default dashboardSlice.reducer
