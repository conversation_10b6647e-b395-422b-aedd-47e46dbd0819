/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { setPage, setPageSize } from '@/src/store/PermissionSlice'
import {
  DeleteFilled,
  EditFilled,
  EyeFilled,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons'
import { Button, Input, Space, Table, type TableColumnsType } from 'antd'
import { useNavigate } from 'react-router'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'

const PermissionPage = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  const dispatch = useAppDispatch()

  const { total, page, pageSize } = useAppSelector(state => state.permission)

  const columns: TableColumnsType<any> = [
    {
      title: t('table.order'),
      key: 'stt',
      width: 50,
      align: 'center',
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: t('permission.permissionGroupName'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 400,
    },
    {
      title: t('table.description'),
      dataIndex: 'description',
      key: 'description',
      align: 'center',
      width: 400,
    },
    {
      title: t('permission.numberOfAccounts'),
      dataIndex: 'number_of_accounts',
      key: 'number_of_accounts',
      width: 150,
      align: 'center',
    },
    {
      title: t('table.action'),
      key: 'action',
      align: 'center',
      width: 300,
      render: () => (
        <Space className="gap-x-4">
          <Button type="text" icon={<EyeFilled />} onClick={() => {}} />
          <Button type="text" icon={<EditFilled />} onClick={() => {}} />
          <Button type="text" icon={<DeleteFilled />} onClick={() => {}} />
        </Space>
      ),
    },
  ]

  return (
    <div className="w-full flex flex-col">
      <div className="flex justify-between w-full items-center">
        <div className="text-lg font-bold text-black">
          {t('permission.permissionList')}
        </div>
        <Button
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => navigate('/permission/detail')}
        >
          {t('common.add')}
        </Button>
      </div>
      <div className="flex justify-between w-full items-center bg-[#F5F9FA] rounded-2xl p-4 mt-8">
        <div className="text-sm font-normal flex text-black flex-row gap-x-6 text-nowrap items-center">
          {t('permission.permissionGroupName')}:&nbsp;
          <Input
            placeholder="Tên nhóm quyền"
            className="!w-[300px] !bg-[#F5F9FA]"
          />
        </div>
        <Button icon={<SearchOutlined />} type="primary" className="uppercase">
          {t('common.search')}
        </Button>
      </div>
      <Table
        bordered
        size="small"
        columns={columns}
        dataSource={[{ name: 'aaaaa' }, { name: 'aaaaa' }]}
        className={`${styles.whiteHeader} mt-8`}
        pagination={{
          total,
          current: page,
          pageSize,
          onChange: (page, pageSize) => {
            dispatch(setPage(page))
            dispatch(setPageSize(pageSize))
          },
        }}
      />
    </div>
  )
}

export default PermissionPage
