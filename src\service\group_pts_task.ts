/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/group-pts-tasks`

const getGroupPTSTask = async (params: any, language?: any) => {
  const api = `${prefix}`
  const response = await axios.get(api, {
    params,
    headers: { 'Accept-Language': language === 'vi' ? 'vi-VN' : language },
  })
  return response.data
}

const createGroupPTSTask = async (body: any) => {
  const api = `${prefix}`

  const response = await axios.post(api, body)
  return response.data
}

const updateGroupPTSTask = async (body: any) => {
  const api = `${prefix}/${body.id}`

  const response = await axios.put(api, body)
  return response.data
}

const removeGroupPTSTask = async (id: string) => {
  const api = `${prefix}/${id}`

  const response = await axios.delete(api)
  return response.data
}

const getGroupPtsTaskId = async (id: string) => {
  const api = `${prefix}/${id}`
  const response = await axios.get(api)
  return response.data
}

export {
  createGroupPTSTask,
  getGroupPTSTask,
  removeGroupPTSTask,
  updateGroupPTSTask,
  getGroupPtsTaskId,
}
