import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { updateUserRoleInDepartment } from '@/src/service/organization_unit'
import { getRoleAll } from '@/src/service/role'
import { toggleChangeRoleMemberModal } from '@/src/store/OrganizationUnitSlice'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Form, Modal, Select } from 'antd'
import { useTranslation } from 'react-i18next'

const ChangeRoleModal = () => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const { organizationUnitMembers, visibleSelectedId } = useAppSelector(
    state => state.organizationUnit
  )
  const dispatch = useAppDispatch()

  const { data: role, isFetching: isRoleFetching } = useQuery({
    queryKey: ['roles'],
    queryFn: () => getRoleAll(),
  })

  const toggleModal = () => {
    dispatch(toggleChangeRoleMemberModal())
  }

  const selectedId = organizationUnitMembers.selectedId
  const currentRoleId = organizationUnitMembers.currentRoleId

  const queryClient = useQueryClient()

  const { mutate: updateRole, isPending: isUpdating } = useMutation({
    mutationFn: ({
      userId,
      departmentId,
      roleId,
    }: {
      userId: string
      departmentId: string
      roleId: string
    }) =>
      updateUserRoleInDepartment(departmentId, userId, {
        userId: userId,
        roleId: roleId,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['userDept'],
      })
      toggleModal()
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      updateRole({
        userId: selectedId!,
        departmentId: visibleSelectedId!,
        roleId: values.roleId,
      })
    } catch (error) {
      handleApiError(error)
    }
  }

  return (
    <Modal
      open={organizationUnitMembers.visibleChangeRoleModal}
      title={t('organization.member.roleEdit')}
      okText={t('common.save')}
      cancelText={t('common.cancel')}
      onCancel={toggleModal}
      onOk={handleOk}
      okButtonProps={{ autoFocus: true, htmlType: 'submit' }}
      destroyOnHidden={true}
      loading={isRoleFetching}
      confirmLoading={isUpdating}
      modalRender={dom => (
        <Form
          layout="vertical"
          form={form}
          name="form_in_modal"
          initialValues={{ modifier: 'public' }}
          clearOnDestroy
        >
          {dom}
        </Form>
      )}
    >
      <Form.Item
        name="roleId"
        label={t('common.edit')}
        rules={[{ required: true }]}
      >
        <Select
          placeholder={t('organization.member.selectRole')}
          allowClear
          style={{ width: '100%' }}
          loading={isRoleFetching}
          defaultValue={
            role?.items?.find(
              (item: { id: string; name: string }) => item.id == currentRoleId
            )?.name
          }
        >
          {role &&
            role.items.map((role: { id: string; name: string }) => (
              <Select.Option key={role.id} value={role.id}>
                {role.name}
              </Select.Option>
            ))}
        </Select>
      </Form.Item>
    </Modal>
  )
}

export default ChangeRoleModal
