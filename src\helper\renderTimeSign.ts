import { TIME_ONLY } from '@/src/constants'
import moment from 'moment'

const renderTimeWithSign = (value: string, std: string) => {
  if (!value) return ''

  const time = moment(value).format(TIME_ONLY)
  const valueDate = moment(value).startOf('day')
  const stdDate = moment(std).startOf('day')

  if (valueDate.isBefore(stdDate)) return `${time} -`
  if (valueDate.isAfter(stdDate)) return `${time} +`
  return time
}

export { renderTimeWithSign }
