/* eslint-disable @typescript-eslint/no-explicit-any */
import { message } from 'antd'

export const handleApiError = (error: any) => {
  if (error?.response?.data?.error?.message) {
    return message.error(
      `[${error.response.data.error.code}] ${error.response.data.error.message}`
    )
  }

  if (error?.response?.data?.errors) {
    const errorData = error.response.data.errors
    const errorMessages: string[] = []

    Object.entries(errorData).forEach(([, value]) => {
      if (Array.isArray(value) && value.length > 0) {
        errorMessages.push(value[0])
      }
    })

    if (errorMessages.length > 0) {
      return message.error(errorMessages.join(', '))
    }
  }

  if (error?.response?.data?.error_description) {
    return message.error(error.response.data.error_description)
  }

  if (error?.response?.data?.message) {
    return message.error(error.response.data.message)
  }

  // return message.error('Lỗi hệ thống')
}
