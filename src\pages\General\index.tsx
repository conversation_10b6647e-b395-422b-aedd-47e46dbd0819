/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import DropdownChangeColumn from '@/src/components/DropdownColumn'
import {
  ISO_DATETIME,
  ISO_DATETIME_HH_MM,
  ISO_DATETIME_NOSECOND,
} from '@/src/constants'
import { disabledDaysDate } from '@/src/helper/disabledDayDate'
import { renderTimeWithSign } from '@/src/helper/renderTimeSign'
import { isValidRangeDate } from '@/src/helper/validDate'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import type { IAirport } from '@/src/schema/IAirport'
import { getAirport } from '@/src/service/airport'
import { getFlightCombined } from '@/src/service/combined'
import {
  closeDropdownViewColumn,
  closeFilterModal,
  openDropdownViewColumn,
  openFilterModal,
  openModalArrivalGeneralFlightInformation,
  openModalDepartureGeneralFlightInformation,
  setCheckListFlight,
  setCombined,
  setParams,
  setSelectedArrivalFlightId,
  setSelectedFlightId,
  setSelectedPTSMasterId,
} from '@/src/store/GeneralSlice'
import { FilterFilled, SearchOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import type { TableColumnsType } from 'antd'
import {
  Button,
  Checkbox,
  DatePicker,
  Flex,
  Form,
  Input,
  Popover,
  Select,
  Table,
} from 'antd'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import QueryString from 'qs'
import { lazy, Suspense, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import styles from './index.module.scss'
import { LAO_ENDHOUR, LAO_STARTHOUR } from '@/src/constants/setting'

const ModalDepartureGeneralFlightInformation = lazy(
  () => import('@/src/pages/General/ModalDepartureGeneralFlightInformation')
)

const ModalArrivalGeneralFlightInformation = lazy(
  () => import('@/src/pages/General/ModalArrivalGeneralFlightInformation')
)

dayjs.extend(utc)
const { RangePicker } = DatePicker

const GeneralPage = () => {
  const { t } = useTranslation()

  const navigate = useNavigate()

  const [form] = Form.useForm()

  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search

  const [keyword, setKeyword] = useState<undefined | string>(undefined)
  const debouncedKeyword = useDebounce(keyword, 500)

  const { airportGlobal, settings } = useAppSelector(state => state.global)

  const [paramsGeneral, setParamsGeneral] = useState<{
    maxResultCount: number
    skipCount: number
    fromDate: string
    toDate: string
    fleetTypes: string[]
    legStates: string[]
    networks: string[]
    depApSched: string
    arrApSched: string
    keyWord: string
    isCombine: boolean
    fromHour: string
    toHour: string
  }>(() => {
    const queryParams = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof queryParams.fromDate === 'string'
        ? dayjs(queryParams.fromDate)
        : null

    const to =
      typeof queryParams.toDate === 'string' ? dayjs(queryParams.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...queryParams,
      skipCount: Number(queryParams.skipCount) || 0,
      maxResultCount: Number(queryParams.maxResultCount) || 1000,
      fromDate:
        valid && from
          ? from?.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to?.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
      fleetTypes: queryParams.fleetTypes || [],
      legStates: queryParams.legStates || [],
      networks: queryParams.networks || [],
      depApSched: airportGlobal,
      arrApSched: queryParams.arrApSched || '',
      isCombine: queryParams.isCombine || true,
    }
    return value
  })

  const {
    selectedFlightId,
    visibleFilterModal,
    visibleDropdownViewColumn,
    checkListFlight,
  } = useAppSelector(state => state.general)

  const query = useQuery({
    queryKey: ['general', paramsGeneral],
    queryFn: () => getFlightCombined(paramsGeneral),
    enabled: !!airportGlobal,
  })

  const { data: airportData } = useQuery({
    queryKey: ['get-airport-list'],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
  })

  // const { data: networkData } = useQuery({
  //   queryKey: ['get-network-list'],
  //   queryFn: () => getNetwork(),
  // })

  // const { data: legStateData } = useQuery({
  //   queryKey: ['get-leg-state-list'],
  //   queryFn: () => getLegState(),
  // })

  // const { data: fleetData } = useQuery({
  //   queryKey: ['get-fleet-list'],
  //   queryFn: () =>
  //     getFleet({
  //       MaxResultCount: 1000,
  //     }),
  // })

  const dispatch = useAppDispatch()

  const columns: TableColumnsType<any> = [
    {
      key: 'index',
      title: t('table.order'),
      align: 'center',
      render: (_, __, index) => paramsGeneral.skipCount + index + 1,
    },
    {
      key: 'nature',
      dataIndex: 'nature',
      title: 'Nature',
      hidden: false,
      align: 'center',
    },
    {
      key: 'aircraft',
      dataIndex: 'aircraft',
      title: 'Aircraft',
      hidden: false,
      align: 'center',
    },
    {
      key: 'registerNo',
      dataIndex: 'registerNo',
      title: 'Register No',
      hidden: false,
      align: 'center',
    },
    {
      key: 'flightNo',
      title: 'Flight No',
      hidden: false,
      dataIndex: 'flightNo',
      align: 'center',
    },
    {
      key: 'routing',
      title: 'Routing',
      hidden: false,
      dataIndex: 'routing',
      align: 'center',
    },
    {
      key: 'sta',
      dataIndex: 'sta',
      title: 'STA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.arrSchedDt)}</>
      ),
    },
    {
      key: 'std',
      dataIndex: 'std',
      title: 'STD',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.arrSchedDt)}</>
      ),
    },
    {
      key: 'eta',
      dataIndex: 'eta',
      title: 'ETA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.arrSchedDt)}</>
      ),
    },
    {
      key: 'etd',
      dataIndex: 'etd',
      title: 'ETD',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.arrSchedDt)}</>
      ),
    },
    {
      key: 'ata',
      dataIndex: 'ata',
      title: 'ATA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.arrSchedDt)}</>
      ),
    },
    {
      key: 'atd',
      dataIndex: 'atd',
      title: 'ATD',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.arrSchedDt)}</>
      ),
    },
    // {
    //   key: 'bay',
    //   dataIndex: 'bay',
    //   title: 'Bay',
    //   hidden: false,
    //   width: 60,
    // },
    {
      key: 'status',
      dataIndex: 'status',
      title: 'Status',
      hidden: false,
      align: 'center',
    },
    // {
    //   key: 'codeShare',
    //   dataIndex: 'codeShare',
    //   title: 'Code share',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'remark',
      dataIndex: 'remark',
      title: 'Remark',
      hidden: false,
      width: 300,
    },
  ]

  const newColumns = columns.map(item => ({
    ...item,
    hidden: !checkListFlight.includes(item.key as string),
  }))

  const now = dayjs()
  const roundedHour = now.startOf('hour')

  const fromDate = settings
    ? roundedHour.add(Number(settings?.[LAO_STARTHOUR]), 'hour')
    : dayjs().startOf('day')
  const toDate = settings
    ? roundedHour.add(Number(settings?.[LAO_ENDHOUR]), 'hour')
    : dayjs().endOf('day')

  const renderTitleTable = () => {
    return (
      <div className="w-full flex justify-end items-center">
        <Button
          type="primary"
          onClick={() => {
            dispatch(openModalArrivalGeneralFlightInformation())
          }}
          className="mt-2 !hidden"
        >
          {t('common.add')}
        </Button>
        <div className="flex flex-row gap-x-2 mt-2 items-center">
          <Checkbox
            defaultChecked={paramsGeneral.isCombine}
            onChange={val => {
              setParamsGeneral({
                ...paramsGeneral,
                skipCount: 0,
                isCombine: val.target.checked,
              })
              dispatch(setCombined(val.target.checked))
            }}
          >
            Combined
          </Checkbox>

          <Button onClick={() => {}} type="primary" className="!hidden">
            {t('common.export')}
          </Button>
          <RangePicker
            allowClear={false}
            showTime
            defaultValue={[fromDate, toDate]}
            format={ISO_DATETIME_NOSECOND}
            className="w-max"
            disabledDate={disabledDaysDate(7)}
            onChange={value => {
              if (value) {
                const newParams = {
                  ...paramsGeneral,
                  skipCount: 0,
                  fromDate: dayjs(value[0]).format(ISO_DATETIME_HH_MM),
                  toDate: dayjs(value[1]).format(ISO_DATETIME_HH_MM),
                }
                return setParamsGeneral(newParams)
              }
            }}
          />
          <Input
            prefix={<SearchOutlined />}
            placeholder={t('departure.searchPlaceholder')}
            className="!w-[200px]"
            defaultValue={searchParams.get('keyWord') || ''}
            onChange={e => {
              setKeyword(e.target.value)
            }}
          />
          <Popover
            open={visibleFilterModal}
            arrow={false}
            placement="bottomRight"
            trigger={['click']}
            content={
              <Form
                className="w-[500px]"
                form={form}
                layout="vertical"
                initialValues={{
                  depApSched: paramsGeneral.depApSched,
                  arrApSched: paramsGeneral.arrApSched,
                  network: paramsGeneral.networks,
                  fleetType: paramsGeneral.fleetTypes,
                  legState: paramsGeneral.legStates,
                }}
              >
                <Flex className="flex flex-row w-full gap-x-6">
                  <Form.Item
                    name="depApSched"
                    label={t('departure.depApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      disabled
                      value={airportGlobal}
                      placeholder={t('common.all')}
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      options={[
                        { value: null, label: t('common.all') },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>

                  <Form.Item
                    name="arrApSched"
                    label={t('departure.arrApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      placeholder={t('common.all')}
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      options={[
                        { value: '', label: t('common.all') },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>

                <div className="flex justify-end gap-x-2">
                  {/* Cancel chỉ đóng modal */}
                  <Button onClick={() => dispatch(closeFilterModal())}>
                    {t('common.cancel')}
                  </Button>

                  {/* Reset */}
                  <Button
                    onClick={() => {
                      const defaultValues = {
                        depApSched: airportGlobal,
                        arrApSched: '',
                        network: [],
                        fleetType: [],
                        legState: [],
                      }

                      form.resetFields()
                      form.setFieldsValue(defaultValues)

                      setParamsGeneral({
                        ...paramsGeneral,
                        depApSched: airportGlobal,
                        arrApSched: '',
                        networks: [],
                        fleetTypes: [],
                        legStates: [],
                        fromDate: dayjs().startOf('day').format(ISO_DATETIME),
                        toDate: dayjs().endOf('day').format(ISO_DATETIME),
                        skipCount: 0,
                      })
                    }}
                  >
                    {t('common.reset')}
                  </Button>

                  {/* Apply */}
                  <Button
                    type="primary"
                    onClick={() => {
                      const newParams = {
                        ...paramsGeneral,
                        depApSched: airportGlobal,
                        arrApSched: form.getFieldValue('arrApSched') || '',
                        skipCount: 0,
                        fromDate: fromDate.format(ISO_DATETIME),
                        toDate: toDate.format(ISO_DATETIME),
                        networks: form.getFieldValue('network') || [],
                        fleetTypes: form.getFieldValue('fleetType') || [],
                        legStates: form.getFieldValue('legState') || [],
                      }

                      setParamsGeneral(newParams)
                      dispatch(closeFilterModal())
                    }}
                  >
                    {t('common.apply')}
                  </Button>
                </div>
              </Form>
            }
          >
            <Button
              icon={<FilterFilled />}
              onClick={() => {
                if (visibleFilterModal) {
                  dispatch(closeFilterModal())
                } else {
                  dispatch(openFilterModal())
                }
              }}
            >
              {t('common.filter')}
            </Button>
          </Popover>
          <DropdownChangeColumn
            columns={columns || []}
            onChangeColumn={val => dispatch(setCheckListFlight(val))}
            onOk={() => dispatch(openDropdownViewColumn())}
            onCancel={() => dispatch(closeDropdownViewColumn())}
            open={visibleDropdownViewColumn}
          />
        </div>
      </div>
    )
  }

  useEffect(() => {
    const data = query.data?.items
    const isSelectedFlightInData = (data: any[], selectedId: string | null) => {
      return data?.some(item => item.id === selectedId)
    }
    if (!isSelectedFlightInData(data, selectedFlightId)) {
      dispatch(setSelectedFlightId(query.data?.items[0]?.id))
      dispatch(setSelectedPTSMasterId(query.data?.items[0]?.ptsId))
    }
  }, [query.data])

  useEffect(() => {
    if (keyword !== undefined) {
      setParamsGeneral({
        ...paramsGeneral,
        skipCount: 0,
        keyWord: debouncedKeyword as string,
      })
    }
  }, [debouncedKeyword])

  useEffect(() => {
    setParamsGeneral({
      ...paramsGeneral,
      depApSched: airportGlobal,
      skipCount: 0,
    })
    dispatch(closeFilterModal())
    form.setFieldsValue({
      ...paramsGeneral,
      depApSched: airportGlobal,
    })
  }, [airportGlobal])

  useEffect(() => {
    dispatch(setCheckListFlight(columns.map(item => item.key as string)))
  }, [])

  useEffect(() => {
    navigate(
      `/flight-schedule/general?${QueryString.stringify(paramsGeneral)}`,
      { replace: true }
    )
    dispatch(setParams(paramsGeneral))
  }, [paramsGeneral])

  // const rowSelection: TableProps<any>['rowSelection'] = {
  //   onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
  //     console.log(
  //       `selectedRowKeys: ${selectedRowKeys}`,
  //       'selectedRows: ',
  //       selectedRows
  //     )
  //   },
  //   getCheckboxProps: (record: any) => ({
  //     disabled: record.name === 'Disabled User',
  //     name: record.name,
  //   }),
  // }

  return (
    <div className="flex flex-col gap-y-4">
      {/* <div className="text-black text-lg font-bold">
        {t('flight.flightDetails')}
      </div>
      <div className="bg-[#F5F9FA] p-4 rounded-sm flex flex-col">
        <Tabs
          defaultActiveKey="1"
          items={tabs.filter(item => !item.hidden)}
          tabPosition="top"
          className={`${styles.tab_active} rounded-sm bg-[#E6F0F3]`}
        />
      </div>
      <div className="text-black text-base font-bold">
        {t('departure.listFlight')}
      </div> */}
      <Table
        // rowSelection={{ type: 'checkbox', ...rowSelection }}
        columns={newColumns}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        title={renderTitleTable}
        scroll={{ x: 'max-content' }}
        dataSource={query.data?.items || []}
        onRow={record => ({
          onClick: () => {
            dispatch(setSelectedFlightId(record.departureId))
            dispatch(setSelectedPTSMasterId(record.ptsId))
            dispatch(setSelectedArrivalFlightId(record.arriveId))
          },
          onDoubleClick: () => {
            dispatch(openModalDepartureGeneralFlightInformation())
          },
        })}
        rowHoverable={false}
        rowClassName={record => {
          return record.departureId === selectedFlightId ? '!bg-[#E6F0F3]' : ''
        }}
        // pagination={{
        //   total: query.data?.totalCount || 0,
        //   current: paramsGeneral.skipCount / paramsGeneral.maxResultCount + 1,
        //   pageSize: paramsGeneral.maxResultCount,
        //   onChange: (page, pageSize) => {
        //     setParamsGeneral({
        //       ...paramsGeneral,
        //       skipCount: (page - 1) * pageSize,
        //       maxResultCount: pageSize,
        //     })
        //   },
        //   showSizeChanger: true,
        //   showTotal: (total, range) => (
        //     <ShowTotal total={total} range={range} />
        //   ),
        // }}
        pagination={false}
        rowKey={record => record.id}
        loading={query.isLoading}
        footer={() => {
          return (
            <div className="justify-end flex">
              {t('common.total')}:&nbsp;
              {query.data?.totalCount}&nbsp;
              {t('common.record')}
            </div>
          )
        }}
      />

      <Suspense>
        <ModalArrivalGeneralFlightInformation />
        <ModalDepartureGeneralFlightInformation />
      </Suspense>
    </div>
  )
}

export default GeneralPage
