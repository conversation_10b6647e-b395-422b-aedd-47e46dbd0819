/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1`

const getSeasonalFlight = async (params?: any) => {
  const api = `${prefix}/season-flight-schedules`
  const response = await axios.get(api, {
    params,
  })
  return response.data
}

const getSeasonalFlightDetail = async (id: string) => {
  const api = `${prefix}/season-flight-schedules/${id}`
  const response = await axios.get(api)
  return response.data
}

export { getSeasonalFlight, getSeasonalFlightDetail }
