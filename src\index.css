@import 'tailwindcss';
body {
  font-family: Nunito Sans;
}

.scrollbar::-webkit-scrollbar {
  width: 8px;
}

.scrollbar::-webkit-scrollbar-thumb {
  background: #006885;
  border-radius: 10px;
}

.scrollbar-hidden {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

@keyframes blink {
  0%,
  100% {
    background-color: transparent;
  }
  50% {
    background-color: #df9f20;
  }
}

.blink-cell {
  animation: blink 1.5s infinite;
}

@theme {
  --color-primary: #006885;
  --color-label: #525050;
  --color-link: #327dff;
  --color-negative: #e93c3c;
  --color-secondary: #df9f20;
  --color-positive: #2ac769;
  --color-black: rgba(7, 5, 4, 1);
  --color-white: rgba(255, 255, 255, 1);
  --color-late: #e3a937;
  --color-cancel: #9c9b9b;
  --color-inconstant: #ff8000;
  --color-tech_erorr: #327dff;
  --color-vip_cip: #af4bff;
  --animate-wiggle: wiggle 1s ease-in-out;
}

@keyframes wiggle {
  0%,
  100% {
    transform: rotate(-30deg);
  }
  50% {
    transform: rotate(30deg);
  }
}

:root {
  --shadow-bottom: 0px 4px 12px rgba(0, 0, 0, 0.2);
}

.shadow-bottom {
  box-shadow: var(--shadow-bottom);
}

.bg-image {
  background-image: url('../src/assets/bg_image.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.bg-splash {
  background-image: url('../src/assets/splash.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.ant-table-row {
  cursor: pointer;
}

#reactSchedulerOutsideWrapper {
  position: initial;
}

.iHFxOs {
  display: none !important;
}

.controls {
  display: flex;
  border: 1px solid #ccc;
  border-top: 0;
  padding: 10px;
}

.controls-right {
  margin-left: auto;
}

.state {
  margin: 10px 0;
  font-family: monospace;
}

.state-title {
  color: #999;
  text-transform: uppercase;
}
