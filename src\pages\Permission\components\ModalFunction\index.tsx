import { colors } from '@/src/constants/colors'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { closeModalFunction } from '@/src/store/PermissionSlice'
import { SaveFilled } from '@ant-design/icons'
import { Button, Input, Modal, Form, Select } from 'antd'

interface Props {
  open: boolean
  id?: string
}

const ModalFunction = (props: Props) => {
  const { open, id } = props

  const [form] = Form.useForm()

  const dispatch = useAppDispatch()

  const onSubmit = async () => {
    // const values = form.getFieldsValue()
    // console.log('🚀 ~ onSubmit ~ values:', values)
  }

  return (
    <Modal
      open={open}
      title={id ? 'Cập nhật chức năng' : 'Tạo mới chức năng'}
      okText="Lưu"
      cancelText="Trở về"
      closable={false}
      className="p-4"
      footer={
        <div className="flex justify-between">
          <Button
            onClick={() => {
              dispatch(closeModalFunction())
            }}
            style={{
              borderColor: colors.primary,
              color: colors.primary,
            }}
          >
            Trở về
          </Button>
          <Button
            type="primary"
            icon={!id && <SaveFilled />}
            onClick={() => {
              onSubmit()
            }}
            style={{ backgroundColor: id ? colors.primary : colors.positive }}
          >
            {id ? 'Cập nhật' : 'Lưu'}
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        name="wrap"
        labelCol={{ flex: '140px' }}
        labelAlign="left"
        wrapperCol={{ flex: 1 }}
        colon={false}
        style={{ maxWidth: 600, paddingTop: 16 }}
      >
        <Form.Item
          label="Email"
          name="email"
          rules={[{ required: true, message: 'Nhập email' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="parent"
          label="Nhóm chức danh"
          rules={[{ required: true, message: 'Chọn nhóm chức danh' }]}
        >
          <Select
            options={[
              { value: 'jack', label: 'Jack' },
              { value: 'lucy', label: 'Lucy' },
              { value: 'Yiminghe', label: 'yiminghe' },
              { value: 'disabled', label: 'Disabled' },
            ]}
            allowClear
          />
        </Form.Item>
        <Form.Item label="Quyền" name="permissions">
          <Select
            mode="tags"
            options={[
              { value: 'jack', label: 'Jack' },
              { value: 'lucy', label: 'Lucy' },
              { value: 'Yiminghe', label: 'yiminghe' },
              { value: 'disabled', label: 'Disabled' },
            ]}
            allowClear
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ModalFunction
