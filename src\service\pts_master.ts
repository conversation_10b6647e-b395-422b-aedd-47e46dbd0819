/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/pts-masters`

const getPTSMaster = async (params: any) => {
  const api = `${prefix}`
  const response = await axios.get(api, { params })
  return response.data
}

const createPTSMaster = async (body: any) => {
  const api = `${prefix}`
  const response = await axios.post(api, body)
  return response.data
}

const updatePTSMaster = async (body: any) => {
  const api = `${prefix}/${body.id}`

  const response = await axios.put(api, body)
  return response.data
}

const removePTSMaster = async (id: string) => {
  const api = `${prefix}/${id}`

  const response = await axios.delete(api)
  return response.data
}

const getPTSMasterDetail = async (id: string) => {
  const api = `${prefix}/${id}`
  const response = await axios.get(api)
  return response.data
}

export {
  createPTSMaster,
  getPTSMaster,
  removePTSMaster,
  updatePTSMaster,
  getPTSMasterDetail,
}
