import { createSlice } from '@reduxjs/toolkit'

interface AirportState {
  selectedAirportId: string | null
  visibleAirportUserModal: boolean
  params: {
    SkipCount: number
    MaxResultCount: number
    Filter?: string
  }
  filterDebounce: string | null
  visibleUserModal: boolean
  selectedUserAirportId: string | null
  visibleDeleteUserModal: boolean
  shouldRefetchUserAirport: boolean
  selectRowKeys: React.Key[]
  paramsUserAirport: {
    SkipCount: number
    MaxResultCount: number
    KeyWord?: string
  }
  KeyWordUserAirportDebounce: string
}

const initialState: AirportState = {
  selectedAirportId: null,
  params: {
    SkipCount: 0,
    MaxResultCount: 20,
  },
  visibleAirportUserModal: false,
  visibleUserModal: false,
  selectedUserAirportId: null,
  visibleDeleteUserModal: false,
  shouldRefetchUserAirport: false,
  selectRowKeys: [],
  paramsUserAirport: {
    SkipCount: 0,
    MaxResultCount: 20,
  },
  KeyWordUserAirportDebounce: '',
  filterDebounce: null,
}

const airportSlice = createSlice({
  name: 'airport',
  initialState,
  reducers: {
    setParams(state, action) {
      state.params = { ...state.params, ...action.payload }
    },
    setSelectedAirportId(state, action) {
      state.selectedAirportId = action.payload
    },
    setFilterDebounce(state, action) {
      state.filterDebounce = action.payload
    },
    openAirportUserModal(state) {
      state.visibleAirportUserModal = true
    },
    closeAirportUserModal(state) {
      state.visibleAirportUserModal = false
    },
    openUserModal(state) {
      state.visibleUserModal = true
    },
    closeUserModal(state) {
      state.visibleUserModal = false
    },
    setSelectedUserAirportId(state, action) {
      state.selectedUserAirportId = action.payload
    },
    openDeleteUserModal(state) {
      state.visibleDeleteUserModal = true
    },
    closeDeleteUserModal(state) {
      state.visibleDeleteUserModal = false
    },
    setShouldRefetchUserAirport: (state, action) => {
      state.shouldRefetchUserAirport = action.payload
    },
    setSelectRowKeys(state, action) {
      state.selectRowKeys = action.payload
    },
    setParamsUserAirport(state, action) {
      state.paramsUserAirport = {
        ...state.paramsUserAirport,
        ...action.payload,
      }
    },
    setKeyWordUserAirportDebounce(state, action) {
      state.KeyWordUserAirportDebounce = action.payload
    },
    resetState() {
      return initialState
    },
  },
})

export const {
  setParams,
  setSelectedAirportId,
  setFilterDebounce,
  openAirportUserModal,
  closeAirportUserModal,
  openUserModal,
  closeUserModal,
  setSelectedUserAirportId,
  openDeleteUserModal,
  closeDeleteUserModal,
  setShouldRefetchUserAirport,
  setSelectRowKeys,
  setParamsUserAirport,
  setKeyWordUserAirportDebounce,
  resetState,
} = airportSlice.actions
export default airportSlice.reducer
