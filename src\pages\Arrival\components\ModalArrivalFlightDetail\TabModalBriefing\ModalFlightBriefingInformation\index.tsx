/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DISPLAY_DATE, UPLOAD_BRIEFING } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { normFile } from '@/src/helper/normFile'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  createBriefing,
  getBriefingDetail,
  getBriefingList,
  updateBriefing,
} from '@/src/service/briefing'
import { downloadFile, uploadFile } from '@/src/service/upload'
import {
  closeModalFlightBriefingInformation,
  setSelectedBriefingId,
} from '@/src/store/ModalArrivalFlightSlice'
import { UploadOutlined } from '@ant-design/icons'

import { useMutation, useQuery } from '@tanstack/react-query'
import {
  But<PERSON>,
  DatePicker,
  Form,
  Input,
  Modal,
  Select,
  Upload,
  message,
} from 'antd'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

const ModalFlightBriefingInformation = () => {
  const { visibleModalFlightBriefingInformation, selectedBriefingId } =
    useAppSelector(state => state.modalArrivalFlight)

  const { selectedFlightId } = useAppSelector(state => state.arrival)

  const dispatch = useAppDispatch()

  const { t } = useTranslation()

  const [form] = Form.useForm()

  const [isUploading, setIsUploading] = useState(false)

  const { refetch } = useQuery({
    queryKey: ['briefing-list', selectedFlightId],
    queryFn: () => getBriefingList(selectedFlightId as string),
    enabled: false,
  })

  const { data: briefingDetail, isLoading: isLoadingBriefingDetail } = useQuery(
    {
      queryKey: ['briefing-detail', selectedBriefingId],
      queryFn: () =>
        getBriefingDetail(
          selectedFlightId as string,
          selectedBriefingId as string
        ),
      enabled: !!selectedBriefingId && visibleModalFlightBriefingInformation,
    }
  )

  const mutationCreate = useMutation({
    mutationFn: async (values: any) => {
      return createBriefing(values)
    },
    onSuccess: () => {
      dispatch(closeModalFlightBriefingInformation())
      form.resetFields()
      refetch()
      message.success('Briefing created successfully!')
    },
    onError: handleApiError,
  })

  const mutationUpdate = useMutation({
    mutationFn: async (values: any) => {
      return updateBriefing(values)
    },
    onSuccess: () => {
      dispatch(closeModalFlightBriefingInformation())
      dispatch(setSelectedBriefingId(null))
      form.resetFields()
      refetch()
      message.success('Briefing updated successfully!')
    },
    onError: handleApiError,
  })

  const mutationUploadFile = useMutation({
    mutationFn: async (values: any) => {
      return uploadFile(values)
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationGetFile = useMutation({
    mutationFn: async (values: any) => {
      return downloadFile(values)
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const onSubmit = async () => {
    await form.validateFields()
    const values = form.getFieldsValue()

    let fileName = ''
    let filePath = ''

    if (values.file?.length > 0) {
      const file = await mutationUploadFile.mutateAsync({
        file: values.file[0].originFileObj,
        fileName: values.file[0].name,
        flightId: selectedBriefingId,
        type: UPLOAD_BRIEFING,
      })
      fileName = values.file[0].name
      filePath = file?.data?.filePath || file?.filePath || ''
    }

    const payload = {
      flightId: selectedFlightId,
      startTime: dayjs(values.fromDate).format('YYYY-MM-DD'),
      endTime: dayjs(values.toDate).format('YYYY-MM-DD'),
      filePath,
      privacy: values.privacy,
      briefingId: selectedBriefingId ?? '',
      content: values.content,
      topic: values.topic,
      fileName,
    }

    if (selectedBriefingId) {
      mutationUpdate.mutate(payload)
    } else {
      mutationCreate.mutate(payload)
    }
  }

  useEffect(() => {
    const loadFormData = async () => {
      if (!briefingDetail || !visibleModalFlightBriefingInformation) return

      const hasFileData = briefingDetail.filePath || briefingDetail.fileName

      if (hasFileData) {
        try {
          const fileResponse = await mutationGetFile.mutateAsync({
            filePath: briefingDetail?.filePath,
            fileName: briefingDetail?.fileName,
          })

          const fileObject = {
            uid: `file-${Date.now()}`,
            name: briefingDetail.fileName || 'Downloaded file',
            status: 'done',
            originFileObj: fileResponse, // Store the blob as originFileObj
            url: URL.createObjectURL(fileResponse), // Create object URL for preview
            response: fileResponse,
          }

          form.setFieldsValue({
            ...briefingDetail,
            file: [fileObject],
            fromDate: briefingDetail?.startTime
              ? dayjs(briefingDetail.startTime)
              : null,
            toDate: briefingDetail?.endTime
              ? dayjs(briefingDetail.endTime)
              : null,
          })
        } catch (error) {
          console.error('Error loading file data:', error)
          form.setFieldsValue({
            ...briefingDetail,
            fromDate: briefingDetail?.startTime
              ? dayjs(briefingDetail.startTime)
              : null,
            toDate: briefingDetail?.endTime
              ? dayjs(briefingDetail.endTime)
              : null,
          })
        }
      } else {
        form.setFieldsValue({
          ...briefingDetail,
          fromDate: briefingDetail?.startTime
            ? dayjs(briefingDetail.startTime)
            : null,
          toDate: briefingDetail?.endTime
            ? dayjs(briefingDetail.endTime)
            : null,
        })
      }
    }

    loadFormData()
  }, [briefingDetail, visibleModalFlightBriefingInformation])

  const handleModalCancel = () => {
    dispatch(closeModalFlightBriefingInformation())
    dispatch(setSelectedBriefingId(null))
    form.resetFields()
    setIsUploading(false)
  }

  return (
    <Modal
      title={`${selectedBriefingId ? 'Update' : 'Create'} Flight Briefing Information`}
      open={visibleModalFlightBriefingInformation}
      width={1000}
      onCancel={handleModalCancel}
      footer={null}
      loading={isLoadingBriefingDetail}
    >
      <Form
        form={form}
        labelAlign="left"
        labelCol={{ flex: '120px' }}
        initialValues={{
          fromDate: dayjs(),
        }}
      >
        <Form.Item
          label="Topic"
          name="topic"
          rules={[{ required: true, message: 'Topic is required' }]}
        >
          <Input.TextArea rows={3} />
        </Form.Item>
        <div className="flex justify-between">
          <div className="flex gap-x-4 w-full">
            <Form.Item label="Privacy" className="w-full" name="privacy">
              <Select
                defaultValue={0}
                options={[
                  { value: 0, label: 'Public' },
                  { value: 1, label: 'Private' },
                ]}
              />
            </Form.Item>
            <Form.Item className="w-full" name="fromDate" label="From Date">
              <DatePicker className="w-full" format={DISPLAY_DATE} disabled />
            </Form.Item>
            <Form.Item
              className="w-full"
              name="toDate"
              label="To Date"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const fromDate = getFieldValue('fromDate')

                    if (dayjs(value).isSame(dayjs(fromDate), 'day')) {
                      return Promise.resolve()
                    }

                    if (
                      value &&
                      dayjs(value).isBefore(dayjs(fromDate), 'day')
                    ) {
                      return Promise.reject(
                        new Error('To Date must be greater than From Date')
                      )
                    }
                    return Promise.resolve()
                  },
                }),
              ]}
            >
              <DatePicker
                className="w-full"
                format={DISPLAY_DATE}
                disabledDate={current => {
                  const fromDate = form.getFieldValue('fromDate')
                  return current && current.isBefore(dayjs(fromDate), 'day')
                }}
              />
            </Form.Item>
          </div>
        </div>

        <Form.Item
          label="Content"
          name="content"
          rules={[{ required: true, message: 'Content is required' }]}
        >
          <Input.TextArea rows={3} />
        </Form.Item>
        <Form.Item
          name="file"
          label="Files"
          valuePropName="fileList"
          getValueFromEvent={normFile}
        >
          <Upload
            maxCount={1}
            onPreview={async () => {
              const res = await mutationGetFile.mutateAsync({
                filePath: briefingDetail.filePath,
                fileName: briefingDetail.fileName,
              })
              FileSaver.saveAs(res, briefingDetail.fileName)
            }}
          >
            <Button icon={<UploadOutlined />}>Browser file</Button>
          </Upload>
        </Form.Item>
      </Form>
      <div className="flex justify-end gap-x-2">
        <Button onClick={handleModalCancel}>{t('common.cancel')}</Button>
        <Button
          type="primary"
          onClick={onSubmit}
          loading={mutationCreate.isPending || mutationUpdate.isPending}
          disabled={isUploading}
        >
          {t('common.save')}
        </Button>
      </div>
    </Modal>
  )
}

export default ModalFlightBriefingInformation
