import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

type OrganizationUnitState = {
  visibleOrganizationUnitModal: boolean
  selectedId: string | null
  parentSelectedId: string | null
  visibleSelectedId: string | null
  modalMode: string | null | 'create' | 'edit' | 'create-sub'
  organizationUnitMembers: {
    visibleAddModal: boolean
    visibleChangeRoleModal: boolean
    selectedId: string | null
    currentRoleId: string | null
  }
  organizationUnitRoles: {
    visibleAddModal: boolean
  }
  params: {
    maxResultCount: number
    skipCount: number
    filter?: string
  }
  searchText: string
  selectRowKeys: React.Key[]
  paramsMember: {
    maxResultCount: number
    skipCount: number
    filter?: string
  }
}

const initialState: OrganizationUnitState = {
  visibleOrganizationUnitModal: false,
  selectedId: null,
  parentSelectedId: null,
  visibleSelectedId: null,
  modalMode: null,
  organizationUnitMembers: {
    visibleAddModal: false,
    visibleChangeRoleModal: false,
    selectedId: null,
    currentRoleId: null,
  },
  organizationUnitRoles: {
    visibleAddModal: false,
  },
  params: {
    maxResultCount: 20,
    skipCount: 0,
    filter: '',
  },
  searchText: '',
  selectRowKeys: [],
  paramsMember: {
    maxResultCount: 20,
    skipCount: 0,
  },
}

const organizationUnitSlice = createSlice({
  name: 'organizationUnit',
  initialState,
  reducers: {
    toggleOrganizationUnitModal(state) {
      state.visibleOrganizationUnitModal = !state.visibleOrganizationUnitModal
    },
    setSelectedOrganizationUnit(state, action: PayloadAction<string | null>) {
      state.selectedId = action.payload
    },
    setParentSelectedOrganizationUnit(
      state,
      action: PayloadAction<string | null>
    ) {
      state.parentSelectedId = action.payload
    },
    setVisibleOrganizationUnit(state, action: PayloadAction<string | null>) {
      state.visibleSelectedId = action.payload
    },
    setModalMode(
      state,
      action: PayloadAction<string | null | 'create' | 'edit' | 'create-sub'>
    ) {
      state.modalMode = action.payload
    },
    toggleOrganizationUnitMemberModal(state) {
      state.organizationUnitMembers.visibleAddModal =
        !state.organizationUnitMembers.visibleAddModal
    },
    toggleChangeRoleMemberModal(state) {
      state.organizationUnitMembers.visibleChangeRoleModal =
        !state.organizationUnitMembers.visibleChangeRoleModal
    },
    setSelectedMember(state, action: PayloadAction<string | null>) {
      state.organizationUnitMembers.selectedId = action.payload
    },
    toggleOrganizationUnitRoleModal(state) {
      state.organizationUnitRoles.visibleAddModal =
        !state.organizationUnitRoles.visibleAddModal
    },
    setCurrentMemberRole(state, action: PayloadAction<string | null>) {
      state.organizationUnitMembers.currentRoleId = action.payload
    },
    setParams(
      state,
      action: PayloadAction<Partial<OrganizationUnitState['params']>>
    ) {
      state.params = { ...state.params, ...action.payload }
    },
    setSearchText(state, action: PayloadAction<string>) {
      state.searchText = action.payload
    },
    resetParamsAndSearchText(state) {
      state.params = { ...initialState.params }
      state.searchText = initialState.searchText
    },
    setSelectRowKeys(state, action: PayloadAction<React.Key[]>) {
      state.selectRowKeys = action.payload
    },
    setParamMember(
      state,
      action: PayloadAction<Partial<OrganizationUnitState['paramsMember']>>
    ) {
      state.paramsMember = { ...state.paramsMember, ...action.payload }
    },
    resetState() {
      return initialState
    },
  },
})

export const {
  toggleOrganizationUnitModal,
  setSelectedOrganizationUnit,
  setParentSelectedOrganizationUnit,
  setVisibleOrganizationUnit,
  setModalMode,
  toggleOrganizationUnitMemberModal,
  toggleChangeRoleMemberModal,
  setSelectedMember,
  toggleOrganizationUnitRoleModal,
  setCurrentMemberRole,
  setParams,
  setSearchText,
  resetParamsAndSearchText,
  setSelectRowKeys,
  setParamMember,
  resetState,
} = organizationUnitSlice.actions

export default organizationUnitSlice.reducer
