/* eslint-disable @typescript-eslint/no-explicit-any */
import { useTranslation } from 'react-i18next'

interface ShowTotalProps {
  total: number
  range?: any
}

const ShowTotal = (props: ShowTotalProps) => {
  const { total, range } = props

  const { t } = useTranslation()

  return (
    <div className="text-sm font-normal text-[#525050] mt-1">
      {t('common.from')} {range[0]} {t('common.to')} {range[1]} {t('common.in')}{' '}
      {total} {t('common.record')}
    </div>
  )
}

export default ShowTotal
