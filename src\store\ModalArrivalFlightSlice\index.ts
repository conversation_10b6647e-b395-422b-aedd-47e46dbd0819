import { createSlice } from '@reduxjs/toolkit'

type ModalFlightState = {
  tabName: string
  visibleModalArrivalFlightDetail: boolean
  visibleModalDepoInadDetail: boolean
  selectFlightModalId: string | null
  visibleModalReasonDetail: boolean
  selectedReasonId: string | null
  visibleModalFTH: boolean
  visibleModalUnusualInfoDetail: boolean
  visibleModalFlightBriefingInformation: boolean
  visibleModalBriefingResponses: boolean
  visibleModalFlight: boolean
  visibleModalRamps: boolean
  visibleModalGroundService: boolean
  selectedUnusualInfoId: string | null
  selectedBriefingId: string | null
  paramsHistory: {
    skipCount: number
    maxResultCount: number
  }
  selectedDepoInadId: string | null
  paramsDepoInad: {
    skipCount: number
    maxResultCount: number
  }
  paramsPassenger: {
    skipCount: number
    maxResultCount: number
  }
  paramsGd: {
    skipCount: number
    maxResultCount: number
  }
  visibleModalPassenger: boolean
  typePax: string
}

const initialState: ModalFlightState = {
  tabName: 'Flight Info',
  visibleModalArrivalFlightDetail: false,
  visibleModalDepoInadDetail: false,
  selectFlightModalId: null,
  visibleModalReasonDetail: false,
  selectedReasonId: null,
  visibleModalFTH: false,
  visibleModalUnusualInfoDetail: false,
  visibleModalFlightBriefingInformation: false,
  visibleModalBriefingResponses: false,
  visibleModalFlight: false,
  visibleModalRamps: false,
  visibleModalGroundService: false,
  selectedUnusualInfoId: null,
  selectedBriefingId: null,
  paramsHistory: {
    skipCount: 0,
    maxResultCount: 20,
  },
  paramsDepoInad: {
    skipCount: 0,
    maxResultCount: 20,
  },
  selectedDepoInadId: null,
  paramsPassenger: {
    skipCount: 0,
    maxResultCount: 20,
  },
  paramsGd: {
    skipCount: 0,
    maxResultCount: 20,
  },
  visibleModalPassenger: false,
  typePax: '',
}

const modalArrivalFlightSlice = createSlice({
  name: 'modalArrivalFlight',
  initialState,
  reducers: {
    setTabName(state, action) {
      state.tabName = action.payload
    },
    openModalArrivalFlightDetail(state) {
      state.visibleModalArrivalFlightDetail = true
    },
    closeModalArrivalFlightDetail(state) {
      state.visibleModalArrivalFlightDetail = false
    },
    openModalDepoInadDetail(state) {
      state.visibleModalDepoInadDetail = true
    },
    closeModalDepoInadDetail(state) {
      state.visibleModalDepoInadDetail = false
    },
    setSelectedFlightModalId(state, action) {
      state.selectFlightModalId = action.payload
    },
    openModalReasonDetail: state => {
      state.visibleModalReasonDetail = true
    },
    closeModalReasonDetail: state => {
      state.visibleModalReasonDetail = false
    },
    setSelectedReasonId(state, action) {
      state.selectedReasonId = action.payload
    },
    openModalFTH: state => {
      state.visibleModalFTH = true
    },
    closeModalFTH: state => {
      state.visibleModalFTH = false
    },
    openModalUnusualInfoDetail: state => {
      state.visibleModalUnusualInfoDetail = true
    },
    closeModalUnusualInfoDetail: state => {
      state.visibleModalUnusualInfoDetail = false
    },
    openModalFlightBriefingInformation: state => {
      state.visibleModalFlightBriefingInformation = true
    },
    closeModalFlightBriefingInformation: state => {
      state.visibleModalFlightBriefingInformation = false
    },
    openModalBriefingResponses: state => {
      state.visibleModalBriefingResponses = true
    },
    closeModalBriefingResponses: state => {
      state.visibleModalBriefingResponses = false
    },
    openModalFlight: state => {
      state.visibleModalFlight = true
    },
    closeModalFlight: state => {
      state.visibleModalFlight = false
    },
    openModalRamps: state => {
      state.visibleModalRamps = true
    },
    closeModalRamps: state => {
      state.visibleModalRamps = false
    },
    openModalGroundService: state => {
      state.visibleModalGroundService = true
    },
    closeModalGroundService: state => {
      state.visibleModalGroundService = false
    },
    setSelectedUnusualInfoId: (state, action) => {
      state.selectedUnusualInfoId = action.payload
    },
    setSelectedBriefingId(state, action) {
      state.selectedBriefingId = action.payload
    },
    setParamsHistory: (state, action) => {
      state.paramsHistory = action.payload
    },
    setParamsDepoInad: (state, action) => {
      state.paramsDepoInad = action.payload
    },
    setSelectedDepoInadId: (state, action) => {
      state.selectedDepoInadId = action.payload
    },
    setParamsPassenger: (state, action) => {
      state.paramsPassenger = action.payload
    },
    setParamsGd: (state, action) => {
      state.paramsGd = action.payload
    },
    openModalPassenger: state => {
      state.visibleModalPassenger = true
    },
    closeModalPassenger: state => {
      state.visibleModalPassenger = false
    },
    setTypePax(state, action) {
      state.typePax = action.payload
    },
  },
})

export const {
  setTabName,
  openModalArrivalFlightDetail,
  closeModalArrivalFlightDetail,
  openModalDepoInadDetail,
  closeModalDepoInadDetail,
  setSelectedFlightModalId,
  openModalReasonDetail,
  closeModalReasonDetail,
  setSelectedReasonId,
  openModalFTH,
  closeModalFTH,
  openModalUnusualInfoDetail,
  closeModalUnusualInfoDetail,
  openModalFlightBriefingInformation,
  closeModalFlightBriefingInformation,
  openModalBriefingResponses,
  closeModalBriefingResponses,
  openModalFlight,
  closeModalFlight,
  openModalRamps,
  closeModalRamps,
  openModalGroundService,
  closeModalGroundService,
  setSelectedUnusualInfoId,
  setSelectedBriefingId,
  setParamsHistory,
  setParamsDepoInad,
  setSelectedDepoInadId,
  setParamsPassenger,
  setParamsGd,
  openModalPassenger,
  closeModalPassenger,
  setTypePax,
} = modalArrivalFlightSlice.actions

export default modalArrivalFlightSlice.reducer
