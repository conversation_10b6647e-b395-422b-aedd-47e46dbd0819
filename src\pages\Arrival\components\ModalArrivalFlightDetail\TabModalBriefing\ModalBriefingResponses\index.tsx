/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  createBreifingReply,
  getBriefingDetail,
  getBriefingList,
} from '@/src/service/briefing'
import {
  closeModalBriefingResponses,
  setSelectedBriefingId,
} from '@/src/store/ModalArrivalFlightSlice'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Form, Input, Modal } from 'antd'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

const ModalBriefingResponses = () => {
  const { visibleModalBriefingResponses } = useAppSelector(
    state => state.modalArrivalFlight
  )

  const dispatch = useAppDispatch()

  const [form] = Form.useForm()

  const { t } = useTranslation()

  const { selectedFlightId } = useAppSelector(state => state.arrival)

  const { selectedBriefingId } = useAppSelector(
    state => state.modalArrivalFlight
  )

  const { refetch } = useQuery({
    queryKey: ['briefing-list', selectedFlightId],
    queryFn: () => getBriefingList(selectedFlightId as string),
    enabled: false,
  })

  const { data: briefingDetail, isLoading: isLoadingBriefingDetail } = useQuery(
    {
      queryKey: ['briefing-detail', visibleModalBriefingResponses],
      queryFn: () =>
        getBriefingDetail(
          selectedFlightId as string,
          selectedBriefingId as string
        ),
      enabled: !!selectedBriefingId && visibleModalBriefingResponses,
    }
  )

  const mutaionCreateReply = useMutation({
    mutationFn: async (values: any) => {
      return createBreifingReply(values)
    },
    onSuccess: () => {
      dispatch(closeModalBriefingResponses())
      form.resetFields()
      dispatch(setSelectedBriefingId(null))
      refetch()
    },
    onError: handleApiError,
  })

  const onSubmit = () => {
    const values = form.getFieldsValue()
    mutaionCreateReply.mutate({
      ...values,
      briefingId: selectedBriefingId as string,
      flightId: selectedFlightId as string,
    })
    dispatch(closeModalBriefingResponses())
    form.resetFields()
  }

  useEffect(() => {
    if (briefingDetail) {
      form.setFieldValue('topic', briefingDetail.topic)
    }
  }, [briefingDetail])

  return (
    <Modal
      width={800}
      open={visibleModalBriefingResponses}
      onCancel={() => {
        dispatch(closeModalBriefingResponses())
        form.resetFields()
        dispatch(setSelectedBriefingId(null))
      }}
      onOk={onSubmit}
      okText={t('common.save')}
      cancelText={t('common.cancel')}
      cancelButtonProps={{ style: { display: 'none' } }}
      loading={isLoadingBriefingDetail}
    >
      <div className="flex flex-col mt-6">
        <div className="font-bold text-lg">{briefingDetail?.topic}</div>
        <Form form={form} labelAlign="left" labelCol={{ flex: '120px' }}>
          <Form.Item label="Content" name="message">
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  )
}

export default ModalBriefingResponses
