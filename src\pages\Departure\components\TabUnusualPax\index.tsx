import { EditFilled } from '@ant-design/icons'
import { Button, Table } from 'antd'
import styles from './index.module.scss'

const TabUnusualPax = () => {
  const columns = [
    { title: 'Reason marked by DHKT', width: '60%' },
    { title: 'Service dept', width: '10%' },
    { title: 'Attribute', width: '10%' },
    { title: 'Delay time', width: '10%' },
    { title: 'Updated by', width: '10%' },
  ]

  return (
    <div className="w-full py-3 bg-[#F5F9FA] gap-y-4 flex flex-col">
      <div className="flex w-full justify-between">
        <div className="text-lg font-bold"> </div>
        <Button
          className="!hidden"
          icon={<EditFilled />}
          type="primary"
          onClick={() => {}}
        >
          Chỉnh sửa
        </Button>
      </div>
      <Table bordered columns={columns} className={`${styles.whiteHeader}`} />
    </div>
  )
}

export default TabUnusualPax
