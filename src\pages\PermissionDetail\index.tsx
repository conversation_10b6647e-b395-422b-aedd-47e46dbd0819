/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import ModalViewColumn from '@/src/pages/Permission/components/ModalViewColumn'
import type { RootState } from '@/src/store'
import {
  toggleAllChildPermissions,
  toggleAllPermissions,
  toggleExpandKey,
  togglePermission,
} from '@/src/store/PermissionSlice'
import { SaveFilled, SettingTwoTone } from '@ant-design/icons'
import {
  Button,
  Checkbox,
  Form,
  Input,
  message,
  Switch,
  Table,
  Tabs,
  type GetProp,
  type TableProps,
} from 'antd'
import { useNavigate, useParams } from 'react-router'
import ModalFunction from '../Permission/components/ModalFunction'
import styles from './index.module.scss'

type ColumnsType<T extends object> = GetProp<TableProps<T>, 'columns'>
type ExpandableConfig<T extends object> = TableProps<T>['expandable']

const tableProps: TableProps<any> = {
  title: undefined,
  showHeader: false,
}

const PermissionDetailPage = () => {
  const { id } = useParams()
  const [form] = Form.useForm()
  const navigate = useNavigate()

  const dispatch = useAppDispatch()

  const {
    permissions,
    allPermissions,
    expandedKeys,
    isLoading,
    visibleModalFunction,
    visibleModalViewColumn,
  } = useAppSelector((state: RootState) => state.permission)

  const defaultExpandable: ExpandableConfig<any> = {
    expandedRowRender: (record: any) => <p>{record.description}</p>,
    expandedRowKeys: expandedKeys,
    onExpand: (_expanded, record) => {
      dispatch(toggleExpandKey(record.key))
    },
  }

  const onSubmit = async () => {
    const values = await form.getFieldsValue()
    if (!values.name) {
      message.error('Vui lòng nhập tên nhóm quyền')
      return
    }
  }

  const TableChild = ({ parentKey }: { parentKey: string }) => {
    const columnsChild: ColumnsType<any> = [
      {
        key: '1',
        title: 'Chức năng',
        dataIndex: 'title',
      },
      {
        key: '2',
        title: 'Cho phép',
        width: 150,
        align: 'center',
        render: (_, record) => (
          <Checkbox
            checked={permissions[parentKey]?.[record.key] || false}
            onChange={() =>
              dispatch(
                togglePermission({
                  parentKey,
                  childKey: record.key,
                })
              )
            }
          />
        ),
      },
      {
        key: '3',
        title: 'Cấu hình dữ liệu',
        width: 150,
        align: 'center',
        render: () => (
          <SettingTwoTone
            twoToneColor={'#0000008A'}
            className="cursor-pointer hover:animate-wiggle"
            onClick={() => {}}
          />
        ),
      },
    ]

    const dataSourceChild = [
      { key: 'create', title: 'Tạo mới' },
      { key: 'update', title: 'Chỉnh sửa' },
      { key: 'delete', title: 'Xóa' },
      { key: 'sync', title: 'Đồng bộ' },
    ]

    return (
      <Table
        {...tableProps}
        columns={columnsChild}
        dataSource={dataSourceChild}
        pagination={false}
        rowHoverable={false}
        className={`${styles.ant_table_no_border} pl-6`}
      />
    )
  }

  const columnsParent: ColumnsType<any> = [
    {
      key: 'chuc_nang',
      title: 'Chức năng',
      dataIndex: 'title',
    },
    {
      key: 'cho_phep',
      title: 'Cho phép',
      width: 150,
      align: 'center',
      render: (_, record) => {
        const parentPerms = permissions[record.key] || {}
        const allChecked = Object.values(parentPerms).every(Boolean)
        const someChecked = Object.values(parentPerms).some(Boolean)

        return (
          <Checkbox
            checked={allChecked}
            indeterminate={someChecked && !allChecked}
            onChange={() => {
              dispatch(
                toggleAllChildPermissions({
                  parentKey: record.key,
                  value: !allChecked,
                })
              )
            }}
          />
        )
      },
    },
    {
      key: 'cau_hinh_du_lieu',
      title: 'Cấu hình dữ liệu',
      width: 150,
      align: 'center',
    },
  ]

  const dataSourceParent = [
    {
      key: 'lich_mua_bay',
      title: <div className="text-black font-bold text-xs">Lịch bay mùa</div>,
      description: <TableChild parentKey="lich_mua_bay" />,
    },
    {
      key: 'chuyen_bay_tong_hop',
      title: (
        <div className="text-black font-bold text-xs">Chuyến bay tổng hợp</div>
      ),
      description: <TableChild parentKey="chuyen_bay_tong_hop" />,
    },
  ]

  const InforTab = () => (
    <Form
      form={form}
      name="wrap"
      labelCol={{ flex: '150px' }}
      labelAlign="left"
      wrapperCol={{ flex: 1 }}
      colon={false}
      style={{ maxWidth: 600, paddingTop: 16 }}
    >
      <Form.Item
        label="Tên nhóm quyền"
        name="name"
        rules={[{ required: true, message: 'Nhập tên nhóm quyền' }]}
      >
        <Input placeholder="Nhập tên nhóm quyền:" />
      </Form.Item>

      <Form.Item label="Mô tả:" name="description">
        <Input.TextArea rows={4} placeholder="Nhập mô tả" />
      </Form.Item>
    </Form>
  )

  const PermissionTab = () => (
    <div className="flex flex-col w-full pt-4 gap-y-8">
      <div className="items-center flex gap-x-2">
        <Switch
          checked={allPermissions}
          onChange={val => dispatch(toggleAllPermissions(val))}
        />
        Tất cả các quyền
      </div>
      <Table
        pagination={false}
        expandable={defaultExpandable}
        columns={columnsParent}
        dataSource={dataSourceParent}
        size="small"
        className={`${styles.table_parent} !bg-white`}
        rowHoverable={false}
      />
    </div>
  )

  const tabs = [
    {
      key: 'thong_tin',
      label: 'Thông tin',
      children: <InforTab />,
    },
    {
      key: 'pnan_quyen',
      label: 'Phân quyền',
      children: <PermissionTab />,
    },
  ]

  return (
    <div className="flex flex-col gap-y-8">
      <div className="text-lg font-bold text-black">
        {id ? 'Cập nhật phân quyền' : 'Tạo mới phân quyền'}
      </div>
      <Tabs
        items={tabs}
        type="card"
        defaultActiveKey="thong_tin"
        className={styles.tab_active}
        tabBarExtraContent={
          <div className="flex flex-row gap-x-4 pb-2">
            <Button
              className="!text-primary"
              type="text"
              onClick={() => navigate(-1)}
            >
              Trở về
            </Button>
            <Button
              icon={<SaveFilled />}
              onClick={onSubmit}
              type="primary"
              className="!bg-positive"
              loading={isLoading}
            >
              Lưu
            </Button>
          </div>
        }
      />
      <ModalFunction open={visibleModalFunction} />
      <ModalViewColumn open={visibleModalViewColumn} />
    </div>
  )
}

export default PermissionDetailPage
