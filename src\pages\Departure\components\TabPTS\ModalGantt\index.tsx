/* eslint-disable react-hooks/exhaustive-deps */
import GanttChart from '@/src/components/GanttChart'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import type { IPTSFlightResponse } from '@/src/schema/IPTSMaster'
import { getPTSFlight } from '@/src/service/flight'
import { closeGantt } from '@/src/store/DepartureSlice'
import { useQuery } from '@tanstack/react-query'
import { Modal } from 'antd'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

const ModalGantt = () => {
  const { visibleGantt, selectedFlightId } = useAppSelector(
    state => state.departure
  )

  const {
    data: PTSFlightData,
    isLoading: isLoadingPTSFlight,
    refetch,
  } = useQuery<IPTSFlightResponse>({
    queryKey: ['flight-pts-detail-gantt', selectedFlightId],
    queryFn: async () => {
      return selectedFlightId && getPTSFlight(selectedFlightId)
    },
    enabled: false,
  })

  useEffect(() => {
    if (visibleGantt) refetch()
  }, [selectedFlightId, visibleGantt])

  const dispatch = useAppDispatch()

  const { t } = useTranslation()

  return (
    <Modal
      open={visibleGantt}
      loading={isLoadingPTSFlight}
      title={t('pts.ganttChart')}
      width="90%"
      cancelButtonProps={{ style: { display: 'none' } }}
      okButtonProps={{ style: { display: 'none' } }}
      onCancel={() => {
        dispatch(closeGantt())
      }}
    >
      <GanttChart data={[PTSFlightData]} />
    </Modal>
  )
}

export default ModalGantt
