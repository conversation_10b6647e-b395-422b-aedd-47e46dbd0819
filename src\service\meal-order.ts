/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/flights`

const getMealOrder = async (flightId: string) => {
  const api = `${prefix}/${flightId}/meal-order`
  const response = await axios.get(api)
  return response.data
}

const updateMealOrder = async (flightId: string, body: any) => {
  const api = `${prefix}/${flightId}/meal-order/${body.id}`
  const response = await axios.put(api, body)
  return response.data
}

export { getMealOrder, updateMealOrder }
