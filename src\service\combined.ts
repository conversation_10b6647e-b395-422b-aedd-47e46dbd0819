import axios from '@/src/config/axios-interceptor'
import qs from 'qs'
import type { IFlightParamsType } from '../schema/IFlightType'

const prefix = `/api/v1/flights`

const getFlightCombined = async (params: IFlightParamsType) => {
  const api = `${prefix}/combined`
  const response = await axios.get(api, {
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' }),
  })
  return response.data
}

export { getFlightCombined }
