/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import ShowTotal from '@/src/components/Showtotal'
import {
  DISPLAY_DATE,
  ISO_DATETIME,
  ISO_DATETIME_NOSECOND,
  REPORT_PTS_TASK_LATE,
} from '@/src/constants'
import { normalizeText } from '@/src/helper/normalizeText'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import { getDepartments } from '@/src/service/organization_unit'
import { getPTSTask } from '@/src/service/pts_task'
import {
  exportExcelReportPTSTaskList,
  getReportPTSTaskList,
} from '@/src/service/report'
import { setCheckListReportLateGT } from '@/src/store/ReportSlice'
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  DatePicker,
  Input,
  Select,
  Table,
  type TableColumnsType,
} from 'antd'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import moment from 'moment'
import QueryString from 'qs'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import styles from './index.module.scss'

const { RangePicker } = DatePicker

const isValidRangeDate = (from: any, to: any) => {
  if (!from || !to) return false
  return from && to && from.isBefore(to)
}

const legState_ARR = ['DEP', 'ARR', 'ON', 'OFF', 'IN', 'OUT']

type Unit = {
  organizationUnitId: string
  name: string
  children: Unit[]
}

const flattenUnits = (units: Unit[]): { label: string; value: string }[] => {
  return units.flatMap(unit => [
    { label: unit.name, value: unit.organizationUnitId },
    ...flattenUnits(unit.children || []),
  ])
}

const ReportPTSTaskLatePage = () => {
  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search
  const navigate = useNavigate()
  const { locale } = useAppSelector(state => state.global)

  const [paramReportPTSTaskLate, setParamReportPTSTaskLate] = useState<{
    skipCount: number
    maxResultCount: number
    fromDate: string
    toDate: string
    airport: string
    keyWord: string
    executeOUId: string | null
    ptsTaskId: string | null
  }>(() => {
    const params = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof params.fromDate === 'string' ? dayjs(params.fromDate) : null
    const to = typeof params.toDate === 'string' ? dayjs(params.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...params,
      skipCount: Number(params.skipCount) || 0,
      maxResultCount: Number(params.maxResultCount) || 20,
      fromDate:
        valid && from
          ? from.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
    }

    return value
  })

  const [keyword, setKeyword] = useState<undefined | string>(undefined)
  const debouncedKeyword = useDebounce(keyword, 500)
  const [airportKeyWord, setAirportKeyword] = useState<undefined | string>(
    undefined
  )
  const debouncedAirportKeyword = useDebounce(airportKeyWord, 500)

  const { t } = useTranslation()
  const dispatch = useAppDispatch()

  const columns: TableColumnsType<any> = [
    {
      title: t('table.order'),
      key: 'stt',
      width: 60,
      render: (_value, _record, index) =>
        paramReportPTSTaskLate.skipCount + index + 1,
    },
    {
      title: t('table.ptsTask'),
      dataIndex: 'description',
      key: 'description',
      width: 100,
    },
    {
      title: t('table.estimateTime'),
      dataIndex: 'estimateStart',
      key: 'estimateStart',
      width: 120,
      align: 'center',
      render: time => <>{dayjs(time).format(ISO_DATETIME_NOSECOND)}</>,
    },
    {
      title: t('table.actualTime'),
      key: 'actualStart',
      dataIndex: 'actualStart',
      width: 120,
      align: 'center',
      render: time => <>{dayjs(time).format(ISO_DATETIME_NOSECOND)}</>,
    },
    {
      title: t('table.delayTime'),
      key: 'actualEstimateDuration',
      dataIndex: 'actualEstimateDuration',
      width: 120,
      align: 'center',
      render: time => (
        <>{Math.round(moment.duration(time, 'minutes').asMinutes())} phút</>
      ),
    },
    {
      title: t('table.flightNo'),
      key: 'etd',
      width: 150,
      align: 'center',
      render: (_value, record) => record.fnCarrier + record.fnNumber,
    },
    {
      title: t('table.routing'),
      key: 'fht',
      width: 150,
      align: 'center',
      render: (_value, record) => `${record.depApSched}-${record.arrApSched}`,
    },
    {
      title: 'STD',
      dataIndex: 'depSchedDt',
      align: 'center',
      width: 150,
      render: (value: any) => moment(value).format(ISO_DATETIME_NOSECOND),
    },
    {
      key: 'atd',
      dataIndex: 'depDt',
      title: 'ATD',
      align: 'center',
      width: 150,
      render: (value: any, record: any) =>
        legState_ARR.includes(record.legState)
          ? moment(value).format(ISO_DATETIME_NOSECOND)
          : null,
    },
  ]

  const { data, isLoading } = useQuery({
    queryKey: ['report-flight-pts-task', paramReportPTSTaskLate, locale.locale],
    queryFn: () =>
      getReportPTSTaskList({
        ...paramReportPTSTaskLate,
        type: REPORT_PTS_TASK_LATE,
        locale: locale.locale,
      }),
  })

  const { data: unitData, isLoading: isLoadingUnit } = useQuery({
    queryKey: ['get-unit-list'],
    queryFn: () => getDepartments(),
  })

  const flattenedUnits = unitData ? flattenUnits(unitData) : []

  const { data: ptsTaskData, isLoading: isLoadingPTSTask } = useQuery({
    queryKey: ['pts-tasks-list-report'],
    queryFn: () =>
      getPTSTask({
        MaxResultCount: 1000,
        Status: 1,
      }),
  })

  const { refetch: exportExcelReport, isLoading: isLoadingExport } = useQuery({
    queryKey: [
      'report-flight-pts-task-late',
      paramReportPTSTaskLate,
      locale.locale,
    ],
    queryFn: () =>
      exportExcelReportPTSTaskList({
        ...paramReportPTSTaskLate,
        type: REPORT_PTS_TASK_LATE,
        locale: locale.locale,
        skipCount: 0,
      }),
    enabled: false,
  })

  const handleExport = async () => {
    const { data } = await exportExcelReport()
    const fileName = `${t('common.reportName')}_${normalizeText(t('report.reportPTSTaskLate'))}.csv`
    return FileSaver.saveAs(data, fileName)
  }

  useEffect(() => {
    dispatch(setCheckListReportLateGT(columns.map(item => item.key as string)))
  }, [])

  useEffect(() => {
    if (keyword !== undefined) {
      setParamReportPTSTaskLate({
        ...paramReportPTSTaskLate,
        skipCount: 0,
        keyWord: debouncedKeyword as string,
      })
    }
  }, [debouncedKeyword])

  useEffect(() => {
    if (airportKeyWord !== undefined) {
      setParamReportPTSTaskLate({
        ...paramReportPTSTaskLate,
        skipCount: 0,
        airport: debouncedAirportKeyword as string,
      })
    }
  }, [debouncedAirportKeyword])

  useEffect(() => {
    navigate(
      `/report/report-pts-task-late?${QueryString.stringify(paramReportPTSTaskLate)}`,
      { replace: true }
    )
  }, [paramReportPTSTaskLate])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex justify-between w-full">
        <div className="text-lg font-bold text-black">
          {t('report.reportPTSTaskLate')}
        </div>
        <div className="flex flex-row gap-x-2 max-2xl:flex-wrap max-2xl:gap-y-2">
          <RangePicker
            className="!h-max w-56"
            allowClear={false}
            defaultValue={[
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('fromDate'))
                : dayjs().startOf('day'),
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('toDate'))
                : dayjs().endOf('day'),
            ]}
            format={DISPLAY_DATE}
            onChange={value => {
              if (value) {
                const newParams = {
                  ...paramReportPTSTaskLate,
                  skipCount: 0,
                  fromDate: dayjs(value[0]).startOf('day').format(ISO_DATETIME),
                  toDate: dayjs(value[1]).endOf('day').format(ISO_DATETIME),
                }
                return setParamReportPTSTaskLate(newParams)
              }
            }}
          />
          <Input
            placeholder={t('report.searchFlightNoPlaceholder')}
            className="!w-48 h-max"
            prefix={<SearchOutlined />}
            onChange={e => {
              setKeyword(e.target.value)
            }}
            defaultValue={searchParams.get('keyWord') || ''}
          />
          <Input
            placeholder={t('report.searchAirportPlaceholder')}
            className="!w-48 h-max"
            prefix={<SearchOutlined />}
            onChange={e => {
              setAirportKeyword(e.target.value)
            }}
            defaultValue={searchParams.get('airport') || ''}
          />
          <Select
            loading={isLoadingUnit}
            defaultValue={paramReportPTSTaskLate.executeOUId}
            className="w-40"
            placeholder={t('report.allDepartmentPlaceholder')}
            options={[
              { value: '', label: `${t('report.allDepartmentPlaceholder')}` },
              ...flattenedUnits.map((item: any) => ({
                label: item.label,
                value: item.value,
              })),
            ]}
            onSelect={val => {
              searchParams.set('executeOUId', val)
              setParamReportPTSTaskLate({
                ...paramReportPTSTaskLate,
                executeOUId: val,
              })
            }}
          />
          <Select
            loading={isLoadingPTSTask}
            defaultValue={paramReportPTSTaskLate.ptsTaskId}
            className="w-44"
            placeholder={t('report.allPtsTaskPlaceholder')}
            options={[
              { value: '', label: `${t('report.allPtsTaskPlaceholder')}` },
              ...(ptsTaskData?.items.map((item: any) => ({
                label: `${item.code} - ${item.description}`,
                value: item.id,
              })) || []),
            ]}
            onSelect={val => {
              searchParams.set('ptsTaskId', val)
              setParamReportPTSTaskLate({
                ...paramReportPTSTaskLate,
                ptsTaskId: val,
              })
            }}
          />
          <Button
            icon={<DownloadOutlined />}
            loading={isLoadingExport}
            onClick={handleExport}
            disabled={isLoadingExport}
            className="!hidden"
          >
            {t('report.download')}
          </Button>
        </div>
      </div>
      <Table
        bordered
        dataSource={data?.items || []}
        columns={columns}
        className={`${styles.whiteHeader}`}
        size="small"
        loading={isLoading}
        rowKey={record => record.id}
        pagination={{
          pageSize: paramReportPTSTaskLate.maxResultCount,
          current:
            paramReportPTSTaskLate.skipCount /
              paramReportPTSTaskLate.maxResultCount +
            1,
          total: data?.totalCount,
          showSizeChanger: true,
          onChange(page, pageSize) {
            setParamReportPTSTaskLate({
              ...paramReportPTSTaskLate,
              skipCount: (page - 1) * pageSize,
              maxResultCount: pageSize,
            })
          },
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
    </div>
  )
}

export default ReportPTSTaskLatePage
