import { normFile } from '@/src/helper/normFile'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { closeModalGroundServiceReportDetail } from '@/src/store/ModalDepartureFlightSlice'
import {
  Button,
  Card,
  Checkbox,
  DatePicker,
  Form,
  Input,
  Modal,
  Upload,
} from 'antd'
import { useTranslation } from 'react-i18next'

const ModalGroundServiceReportDetail = () => {
  const { visibleModalGroundServiceReportDetail } = useAppSelector(
    state => state.modalDepartureFlight
  )

  const [form] = Form.useForm()

  const dispatch = useAppDispatch()

  const { t } = useTranslation()

  return (
    <Modal
      title="Ground Service Report Detail"
      open={visibleModalGroundServiceReportDetail}
      width={1000}
      cancelButtonProps={{ style: { display: 'none' } }}
      okButtonProps={{ style: { display: 'none' } }}
      onCancel={() => dispatch(closeModalGroundServiceReportDetail())}
    >
      <Card>
        <Form form={form} labelAlign="left" labelCol={{ flex: '120px' }}>
          <div className="flex gap-x-4">
            <Form.Item label="Flight">
              <DatePicker />
            </Form.Item>
            <Form.Item>
              <Input />
            </Form.Item>
          </div>
          <Form.Item label="Description">
            <Input.TextArea rows={3} />
          </Form.Item>
          <div className="flex pl-30">
            <Form.Item className="flex items-center !gap-x-2">
              <Checkbox>Is SMS (related to Safety and Security)</Checkbox>
            </Form.Item>
          </div>
          <Form.Item label="Solution">
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item label="Result">
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item label="Remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            label="Doc file(s)"
            valuePropName="fileList"
            getValueFromEvent={normFile}
            name="fileList"
          >
            <div className="flex justify-between items-center">
              <div className="flex gap-x-2 items-center">
                <Upload action="/upload.do">
                  <Button>{t('modalFlight.choosenFile')}</Button>
                </Upload>
              </div>
              <div className="text-primary">
                ({t('modalFlight.uploadLimit')})
              </div>
            </div>
          </Form.Item>
        </Form>
      </Card>
      <div className="flex justify-between mt-4">
        <div className="flex gap-x-4">
          <Button
            type="primary"
            onClick={() => {
              dispatch(closeModalGroundServiceReportDetail())
            }}
          >
            {t('common.save')}
          </Button>
          <Button type="primary">{t('common.export')}</Button>
        </div>
        <Button
          type="primary"
          onClick={() => {
            dispatch(closeModalGroundServiceReportDetail())
          }}
        >
          {t('common.cancel')}
        </Button>
      </div>
    </Modal>
  )
}

export default ModalGroundServiceReportDetail
