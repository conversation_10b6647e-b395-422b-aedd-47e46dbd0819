/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import ShowTotal from '@/src/components/Showtotal'
import {
  DISPLAY_DATE,
  ISO_DATETIME,
  ISO_DATETIME_NOSECOND,
  PIE_CHART_OSP,
  REPORT_LATE_GROUP_TASK_OSP,
  REPORT_LATE_GROUP_TASK_OTP,
  TIME_ONLY,
} from '@/src/constants'
import { normalizeText } from '@/src/helper/normalizeText'
import { isValidRangeDate } from '@/src/helper/validDate'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import type { IPTSTask } from '@/src/schema/IPTSTask'
import { getGroupPTSTask } from '@/src/service/group_pts_task'
import { getDepartments } from '@/src/service/organization_unit'
import {
  exportExcelReportPTSGroupTaskList,
  getReportPTSGroupTaskList,
} from '@/src/service/report'
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  DatePicker,
  Input,
  Select,
  Table,
  type TableColumnsType,
} from 'antd'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import moment from 'moment'
import QueryString from 'qs'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import styles from './index.module.scss'

const { RangePicker } = DatePicker

const legState_ARR = ['DEP', 'ARR', 'ON', 'OFF', 'IN', 'OUT']

type Unit = {
  organizationUnitId: string
  name: string
  children: Unit[]
}

const flattenUnits = (units: Unit[]): { label: string; value: string }[] => {
  return units.flatMap(unit => [
    { label: unit.name, value: unit.organizationUnitId },
    ...flattenUnits(unit.children || []),
  ])
}

const ReportPTSGroupTaskLatePage = () => {
  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search
  const navigate = useNavigate()
  const { locale } = useAppSelector(state => state.global)
  const [paramReportGroupTaskLate, setParamReportGroupTaskLate] = useState<{
    skipCount: number
    maxResultCount: number
    type: number
    fromDate: string
    toDate: string
    airport: string
    groupPtsTaskId: string | null
    executeOUId: string | null
  }>(() => {
    const params = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof params.fromDate === 'string' ? dayjs(params.fromDate) : null
    const to = typeof params.toDate === 'string' ? dayjs(params.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...params,
      type: Number(params.type) || PIE_CHART_OSP,
      skipCount: Number(params.skipCount) || 0,
      maxResultCount: Number(params.maxResultCount) || 20,
      fromDate:
        valid && from
          ? from.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
    }

    return value
  })

  const { data: unitData, isLoading: isLoadingUnit } = useQuery({
    queryKey: ['get-unit-list'],
    queryFn: () => getDepartments(),
  })

  const flattenedUnits = unitData ? flattenUnits(unitData) : []

  const { data: ptsGroupTaskData, isLoading: isLoadingGroupPTSTask } = useQuery(
    {
      queryKey: ['pts-group-tasks-list-report'],
      queryFn: () =>
        getGroupPTSTask({
          MaxResultCount: 1000,
        }),
    }
  )

  const { refetch: exportExcelReport, isLoading: isLoadingExport } = useQuery({
    queryKey: ['report-group-task-late', paramReportGroupTaskLate],
    queryFn: () =>
      exportExcelReportPTSGroupTaskList({
        ...paramReportGroupTaskLate,
        skipCount: 0,
      }),
    enabled: false,
  })

  const handleExport = async () => {
    const { data } = await exportExcelReport()
    const fileName = `${t('common.reportName')}_${normalizeText(`${t('report.reportGroupTaskLate')}_${paramReportGroupTaskLate.type === REPORT_LATE_GROUP_TASK_OTP ? 'OTP' : 'OSP'}`)}.csv`
    return FileSaver.saveAs(data, fileName)
  }

  const groupTaskPTSMap = useMemo(() => {
    const map = new Map<string, string>()
    ptsGroupTaskData?.items?.forEach((item: any) => {
      map.set(item.id, item.name)
    })
    return map
  }, [ptsGroupTaskData])

  const [keyword, setKeyword] = useState<undefined | string>(undefined)
  const debouncedKeyword = useDebounce(keyword, 500)
  const { t } = useTranslation()

  const columns: TableColumnsType<any> = [
    {
      title: t('table.order'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_value, _record, index) =>
        paramReportGroupTaskLate.skipCount + index + 1,
    },
    {
      title: t('table.ptsGroupTask'),
      dataIndex: 'groupPtsTaskId',
      key: 'groupPtsTaskId',
      width: 150,
      render: (value: string) => groupTaskPTSMap.get(value) || '',
    },
    {
      title: t('table.ptsTask'),
      dataIndex: 'ptsTasks',
      key: 'airCraft',
      width: 200,
      render: (record: any) => (
        <>
          {record?.length > 0
            ? record.map((item: IPTSTask) => (
                <div key={item.id}>
                  {item.code} - {item.description}
                </div>
              ))
            : ''}
        </>
      ),
    },
    {
      title: t('table.estimateTime'),
      key: 'estimateStart',
      width: 120,
      align: 'center',
      render: (_value, record) => (
        <>
          {dayjs(record.estimateStart).format(TIME_ONLY)} -{' '}
          {dayjs(record.estimateEnd).format(TIME_ONLY)}
        </>
      ),
    },
    {
      title: t('table.actualTime'),
      key: 'actualStart',
      width: 120,
      align: 'center',
      render: (_value, record) => (
        <>
          {dayjs(record.actualStart).format(TIME_ONLY)} -{' '}
          {dayjs(record.actualEnd).format(TIME_ONLY)}
        </>
      ),
    },
    {
      title: t('table.delayTime'),
      key: 'actualEstimateDuration',
      dataIndex: 'actualEstimateDuration',
      width: 120,
      align: 'center',
      render: time => (
        <>{Math.round(moment.duration(time, 'minutes').asMinutes())} phút</>
      ),
    },
    {
      title: t('table.flightNo'),
      key: 'etd',
      width: 150,
      align: 'center',
      render: (_value, record) => record.fnCarrier + record.fnNumber,
    },
    {
      title: t('table.routing'),
      key: 'fht',
      width: 150,
      align: 'center',
      render: (_value, record) =>
        `${record.depApSched}-${record.arrApSched}` /*  */,
    },
    {
      title: 'STD',
      dataIndex: 'depSchedDt',
      align: 'center',
      width: 150,
      render: (value: any, record: any) =>
        legState_ARR.includes(record.legState)
          ? moment(value).format(ISO_DATETIME_NOSECOND)
          : null,
    },
    {
      key: 'atd',
      dataIndex: 'depDt',
      title: 'ATD',
      align: 'center',
      width: 150,
      render: (value: any, record: any) =>
        legState_ARR.includes(record.legState)
          ? moment(value).format(ISO_DATETIME_NOSECOND)
          : null,
    },
  ]

  const { data, isLoading } = useQuery({
    queryKey: [
      'report-flight-pts-group-task-late',
      paramReportGroupTaskLate,
      locale.locale,
    ],
    queryFn: () =>
      getReportPTSGroupTaskList({
        ...paramReportGroupTaskLate,
        locale: locale.locale,
      }),
  })

  useEffect(() => {
    if (keyword !== undefined) {
      setParamReportGroupTaskLate({
        ...paramReportGroupTaskLate,
        skipCount: 0,
        airport: debouncedKeyword as string,
      })
    }
  }, [debouncedKeyword])

  useEffect(() => {
    navigate(
      `/report/group-task-late?${QueryString.stringify(paramReportGroupTaskLate)}`,
      { replace: true }
    )
  }, [paramReportGroupTaskLate])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex justify-between w-full">
        <div className="text-lg font-bold text-black">
          {t('report.reportGroupTaskLate')}
        </div>
        <div className="flex flex-row gap-x-2 max-2xl:flex-wrap max-2xl:gap-y-2">
          <Select
            defaultValue={
              Number(searchParams.get('type'))
                ? Number(searchParams.get('type'))
                : REPORT_LATE_GROUP_TASK_OSP
            }
            options={[
              { value: REPORT_LATE_GROUP_TASK_OTP, label: 'OTP' },
              {
                value: REPORT_LATE_GROUP_TASK_OSP,
                label: 'OSP',
              },
            ]}
            onChange={val => {
              setParamReportGroupTaskLate({
                ...paramReportGroupTaskLate,
                type: val,
                skipCount: 0,
              })
            }}
          />
          <RangePicker
            className="!h-max"
            allowClear={false}
            defaultValue={[
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('fromDate'))
                : dayjs().startOf('day'),
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('toDate'))
                : dayjs().endOf('day'),
            ]}
            format={DISPLAY_DATE}
            onChange={value => {
              if (value) {
                const newParams = {
                  ...paramReportGroupTaskLate,
                  skipCount: 0,
                  fromDate: dayjs(value[0]).startOf('day').format(ISO_DATETIME),
                  toDate: dayjs(value[1]).endOf('day').format(ISO_DATETIME),
                }
                return setParamReportGroupTaskLate(newParams)
              }
            }}
          />
          <Input
            placeholder={t('report.searchAirportPlaceholder')}
            className="!w-48 h-max"
            prefix={<SearchOutlined />}
            onChange={e => {
              setKeyword(e.target.value)
            }}
            defaultValue={paramReportGroupTaskLate.airport}
          />
          <Select
            loading={isLoadingUnit}
            defaultValue={paramReportGroupTaskLate.executeOUId}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            className="w-40"
            placeholder={t('report.allDepartmentPlaceholder')}
            options={[
              { value: '', label: `${t('report.allDepartmentPlaceholder')}` },
              ...flattenedUnits.map((item: any) => ({
                label: item.label,
                value: item.value,
              })),
            ]}
            onSelect={val => {
              setParamReportGroupTaskLate({
                ...paramReportGroupTaskLate,
                executeOUId: val,
              })
            }}
          />
          <Select
            loading={isLoadingGroupPTSTask}
            className="w-52"
            defaultValue={paramReportGroupTaskLate.groupPtsTaskId}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            placeholder={t('report.allGroupTaskPlaceholder')}
            options={[
              { value: '', label: `${t('report.allGroupTask')}` },
              ...(ptsGroupTaskData?.items.map((item: any) => ({
                value: item.id,
                label: `${item.code} - ${item.name}`,
              })) || []),
            ]}
            onSelect={val => {
              setParamReportGroupTaskLate({
                ...paramReportGroupTaskLate,
                groupPtsTaskId: val,
              })
            }}
          />
          <Button
            icon={<DownloadOutlined />}
            loading={isLoadingExport}
            onClick={handleExport}
            disabled={isLoadingExport}
          >
            {t('report.download')}
          </Button>
        </div>
      </div>
      <Table
        bordered
        dataSource={data?.items || []}
        columns={columns}
        className={`${styles.whiteHeader}`}
        size="small"
        loading={isLoading}
        rowKey={record => record.id}
        pagination={{
          pageSize: paramReportGroupTaskLate.maxResultCount,
          current:
            paramReportGroupTaskLate.skipCount /
              paramReportGroupTaskLate.maxResultCount +
            1,
          total: data?.totalCount,
          showSizeChanger: true,
          onChange(page, pageSize) {
            setParamReportGroupTaskLate({
              ...paramReportGroupTaskLate,
              skipCount: (page - 1) * pageSize,
              maxResultCount: pageSize,
            })
          },
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
    </div>
  )
}

export default ReportPTSGroupTaskLatePage
