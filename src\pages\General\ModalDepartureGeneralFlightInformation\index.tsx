/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import { DISPLAY_DATE, TIME_ONLY } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getFlightDetail, updateFlightDetail } from '@/src/service/flight'
import {
  closeModalDepartureGeneralFlightInformation,
  openModalArrivalGeneralFlightInformation,
} from '@/src/store/GeneralSlice'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Col, Form, Input, message, Modal, Row } from 'antd'
import moment from 'moment'
import { useEffect } from 'react'

const ModalDepartureGeneralFlightInformation = () => {
  const {
    visibleModalDepartureGeneralFlightInformation,
    selectedFlightId,
    combined,
  } = useAppSelector(state => state.general)
  useAppSelector(state => state.general)

  const dispatch = useAppDispatch()

  const [form] = Form.useForm()

  const { data: flightDetail, isLoading } = useQuery({
    queryKey: ['flight-detail-combined-departure', selectedFlightId],
    queryFn: () => getFlightDetail(selectedFlightId as string),
    enabled:
      !!selectedFlightId && visibleModalDepartureGeneralFlightInformation,
  })

  const mutationUpdate = useMutation({
    mutationFn: async (values: any) => {
      return updateFlightDetail(values)
    },
    onSuccess: () => {
      dispatch(closeModalDepartureGeneralFlightInformation())
      form.resetFields()
      message.success('Flight detail updated successfully!')
    },
    onError: handleApiError,
  })

  const onSubmit = async () => {
    await form.validateFields()
    const values = form.getFieldsValue()
    const data = {
      remark: values.remark,
      id: selectedFlightId,
    }
    await mutationUpdate.mutateAsync(data)
  }

  useEffect(() => {
    if (flightDetail) {
      form.setFieldsValue({
        ...flightDetail,
        flightNo: flightDetail?.fnCarrier + flightDetail?.fnNumber,
        routing: flightDetail?.depApSched + ' - ' + flightDetail?.arrApSched,
        registerNo: flightDetail?.acRegistration,
        depDt: moment(flightDetail?.depDt).format(DISPLAY_DATE),
        std: moment(flightDetail?.depSchedDt).format(TIME_ONLY),
        sta: moment(flightDetail?.arrSchedDt).format(TIME_ONLY),
        etd: moment(flightDetail?.depDt).format(TIME_ONLY),
        eta: moment(flightDetail?.arrDt).format(TIME_ONLY),
        atd:
          flightDetail.legState === 'ARR'
            ? moment(flightDetail?.depDt).format(TIME_ONLY)
            : null,
        ata:
          flightDetail.legState === 'ARR'
            ? moment(flightDetail?.arrDt).format(TIME_ONLY)
            : null,
        nature: flightDetail?.fltType,
        configuration: `${flightDetail?.seatsC + 'J' + flightDetail?.seatsW + 'W' + flightDetail?.seatsY + 'Y'}`,
        fht: flightDetail?.fht
          ? moment(flightDetail?.fht).format(TIME_ONLY)
          : null,
      })
    }
  }, [
    selectedFlightId,
    flightDetail,
    visibleModalDepartureGeneralFlightInformation,
  ])

  return (
    <Modal
      width={800}
      open={visibleModalDepartureGeneralFlightInformation}
      onCancel={() => {
        dispatch(closeModalDepartureGeneralFlightInformation())
      }}
      title="Departure Flight"
      footer={null}
      closable={false}
      centered
      loading={isLoading}
    >
      <div className="bg-[#F0F1F3FF] p-4 flex flex-col">
        <Form form={form} layout="vertical">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="Date" name="depDt">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Aircraft" name="acSubType">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Register No" name="registerNo">
                <Input readOnly />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item label="Flight No" name="flightNo">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Next Flight" name="nextFlight">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Next Destination" name="nextDestination">
                <Input readOnly />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item label="Routing" name="routing">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Status" name="legState">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Nature" name="nature">
                <Input readOnly />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="STD" name="std">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="STA" name="sta">
                <Input readOnly />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Code share" name="codeShare">
                <Input readOnly />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="ETD" name="etd">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="ETA" name="eta">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="ATD" name="atd">
                <Input readOnly />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="ATA" name="ata">
                <Input readOnly />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="Remark" name="remark">
            <Input.TextArea rows={2} />
          </Form.Item>

          {/* <Form.Item label="Update reason" name="updateReason">
          <Input.TextArea rows={2} />
        </Form.Item> */}
        </Form>
      </div>
      <div className="flex justify-end gap-x-4 items-center mt-4">
        <div className="flex gap-x-4">
          <Button
            className={`${!combined ? '!hidden' : ''}`}
            type="primary"
            onClick={() => {
              dispatch(closeModalDepartureGeneralFlightInformation())
              dispatch(openModalArrivalGeneralFlightInformation())
            }}
          >
            Go to Arrival Flt.
          </Button>
          <Button type="primary" onClick={onSubmit}>
            Save
          </Button>
          <Button
            onClick={() => {
              dispatch(closeModalDepartureGeneralFlightInformation())
            }}
          >
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default ModalDepartureGeneralFlightInformation
