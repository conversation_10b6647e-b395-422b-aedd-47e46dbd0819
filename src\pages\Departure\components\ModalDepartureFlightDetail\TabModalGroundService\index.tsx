/* eslint-disable @typescript-eslint/no-explicit-any */
import { Table, type TableProps } from 'antd'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { openModalGroundServiceReportDetail } from '@/src/store/ModalDepartureFlightSlice'
import ModalGroundServiceReportDetail from '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalGroundService/ModalGroundServiceReportDetail'

const TabGroundService = () => {
  const { t } = useTranslation()

  const dispatch = useAppDispatch()

  const columns: TableProps<any>['columns'] = [
    {
      key: 'index',
      title: t('table.order'),
      dataIndex: 'index',
      width: '10%',
    },
    {
      key: 'description',
      title: t('pts.description'),
      dataIndex: 'description',
    },
  ]

  return (
    <div>
      <div className="flex justify-end">
        <div
          className="text-primary cursor-pointer"
          onClick={() => {
            dispatch(openModalGroundServiceReportDetail())
          }}
        >
          {t('common.create')}
        </div>
      </div>
      <Table
        columns={columns}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        dataSource={[
          {
            index: 1,
            description: 'Description 1',
          },
        ]}
      />
      <ModalGroundServiceReportDetail />
    </div>
  )
}

export default TabGroundService
