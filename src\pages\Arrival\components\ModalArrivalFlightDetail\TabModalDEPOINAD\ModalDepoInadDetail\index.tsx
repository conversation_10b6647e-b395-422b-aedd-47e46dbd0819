/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DISPLAY_DATE, UPLOAD_DEPO_INAD } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { normFile } from '@/src/helper/normFile'
import {
  selectFilterOption,
  selectFilterValueOption,
} from '@/src/helper/selectFilterOption'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getAirport } from '@/src/service/airport'
import { getCountry, getProvinces } from '@/src/service/country'
import {
  createDepoInad,
  getDepoInad,
  getDepoInadDetail,
  getDepoInadGroup,
  updateDepoInad,
} from '@/src/service/depo_inad'
import { downloadFile, uploadFile } from '@/src/service/upload'
import {
  closeModalDepoInadDetail,
  setSelectedDepoInadId,
} from '@/src/store/ModalArrivalFlightSlice'
import { CloseOutlined, UploadOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
  Upload,
} from 'antd'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import { useEffect } from 'react'

const ModalDepoInadDetail = () => {
  const { visibleModalDepoInadDetail, selectedDepoInadId, paramsDepoInad } =
    useAppSelector(state => state.modalArrivalFlight)

  const { selectedFlightId } = useAppSelector(state => state.arrival)

  const dispatch = useAppDispatch()

  const [form] = Form.useForm()

  const { data: depoInadDetail } = useQuery({
    queryKey: ['depo-inad-detail', selectedDepoInadId],
    queryFn: () =>
      getDepoInadDetail(
        selectedFlightId as string,
        selectedDepoInadId as string
      ),
  })

  const { refetch } = useQuery({
    queryKey: ['depo-inad-list', selectedFlightId, paramsDepoInad],
    queryFn: () =>
      getDepoInad(selectedFlightId as string, {
        ...paramsDepoInad,
      }),
    enabled: false,
  })

  const { data: airportData } = useQuery({
    queryKey: ['airport-list'],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
  })

  const { data: countryData } = useQuery({
    queryKey: ['country-list'],
    queryFn: () => getCountry(),
  })

  const { data: depoInadGroupData } = useQuery({
    queryKey: ['depo-inad-group-list'],
    queryFn: () => getDepoInadGroup(),
  })

  const { data: provinceData } = useQuery({
    queryKey: ['province-list'],
    queryFn: () => getProvinces(),
  })

  const mutationCreateDepoInad = useMutation({
    mutationFn: async (values: any) => {
      return createDepoInad(values)
    },
    onSuccess: () => {
      dispatch(closeModalDepoInadDetail())
      form.resetFields()
      refetch()
      message.success('Depo Inad created successfully!')
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationUpdateDepoInad = useMutation({
    mutationFn: async (values: any) => {
      return updateDepoInad(values)
    },
    onSuccess: () => {
      dispatch(closeModalDepoInadDetail())
      dispatch(setSelectedDepoInadId(null))
      form.resetFields()
      refetch()
      message.success('Depo Inad updated successfully!')
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const onSubmit = async () => {
    await form.validateFields()
    const values = form.getFieldsValue()

    let fileName = ''
    let filePath = ''

    if (values.file?.length > 0) {
      const file = await mutationUploadFile.mutateAsync({
        file: values.file[0].originFileObj,
        fileName: values.file[0].name,
        flightId: selectedDepoInadId,
        type: UPLOAD_DEPO_INAD,
      })
      fileName = values.file[0].name
      filePath = file?.data?.filePath || file?.filePath || ''
    }

    const data = {
      ...values,
      id: selectedDepoInadId,
      flightId: selectedFlightId,
      surname: values.surname,
      givenName: values.givenName,
      dob: values.dob ? values.dob.format('YYYY-MM-DD') : null,
      gender: values.gender,
      passport: values.passport,
      citizenship: values.citizenship,
      pobCountry: values.pobCountry,
      pobCity: values.pobCity,
      pobDetail: values.pobDetail,
      refuseLocation: values.refuseLocation,
      group: values.group,

      preFlightDate: values.preFlight_date
        ? values.preFlight_date.format('YYYY-MM-DD')
        : null,
      preFlightNo: values.preFlight_flightNo || null,
      preFlightRouting: values.preFlight_routing || null,

      arrFlightDate: values.arrFlight_date
        ? values.arrFlight_date.format('YYYY-MM-DD')
        : null,
      arrFlightNo: values.arrFlight_flightNo || null,
      arrFlightRouting: values.arrFlight_routing || null,

      nextFlightDate: values.nextFlight_date
        ? values.nextFlight_date.format('YYYY-MM-DD')
        : null,
      nextFlightNo: values.nextFlight_flightNo || null,
      nextFlightRouting: values.nextFlight_routing || null,

      depoInadFrees: values.depoInadFees || [],

      fileName,
      filePath,
    }

    if (selectedDepoInadId) {
      await mutationUpdateDepoInad.mutateAsync(data)
    } else {
      await mutationCreateDepoInad.mutateAsync(data)
    }

    dispatch(closeModalDepoInadDetail())
    form.resetFields()
  }

  const mutationUploadFile = useMutation({
    mutationFn: async (values: any) => {
      return uploadFile(values)
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationGetFile = useMutation({
    mutationFn: async (values: any) => {
      return downloadFile(values)
    },
    onError: error => {
      handleApiError(error)
    },
  })

  useEffect(() => {
    const loadFormData = async () => {
      if (!depoInadDetail) return

      // Check nếu có filePath/fileName thì download file
      const hasFileData = depoInadDetail.filePath || depoInadDetail.fileName

      let fileList: any[] = []
      if (hasFileData) {
        try {
          const fileResponse = await mutationGetFile.mutateAsync({
            filePath: depoInadDetail.filePath,
            fileName: depoInadDetail.fileName,
          })

          fileList = [
            {
              uid: `file-${Date.now()}`,
              name: depoInadDetail.fileName || 'Downloaded file',
              status: 'done',
              originFileObj: fileResponse,
              url: URL.createObjectURL(fileResponse),
              response: fileResponse,
            },
          ]
        } catch (error) {
          console.error('Error loading file data:', error)
        }
      }

      form.setFieldsValue({
        ...depoInadDetail,
        dob: depoInadDetail.dob ? dayjs(depoInadDetail.dob) : null,

        preFlight_date: depoInadDetail.preFlightDate
          ? dayjs(depoInadDetail.preFlightDate)
          : null,
        preFlight_flightNo: depoInadDetail.preFlightNo,
        preFlight_routing: depoInadDetail.preFlightRouting,

        arrFlight_date: depoInadDetail.arrFlightDate
          ? dayjs(depoInadDetail.arrFlightDate)
          : null,
        arrFlight_flightNo: depoInadDetail.arrFlightNo,
        arrFlight_routing: depoInadDetail.arrFlightRouting,

        nextFlight_date: depoInadDetail.nextFlightDate
          ? dayjs(depoInadDetail.nextFlightDate)
          : null,
        nextFlight_flightNo: depoInadDetail.nextFlightNo,
        nextFlight_routing: depoInadDetail.nextFlightRouting,

        depoInadFees: depoInadDetail.depoInadFees?.map((item: any) => ({
          seFee: item.seFee,
          remark: item.remark,
        })) || [{}],

        file: fileList,
      })
    }

    loadFormData()
  }, [depoInadDetail])

  return (
    <Modal
      title={selectedDepoInadId ? 'Update Depo Inad' : 'Create Depo Inad'}
      width={1000}
      open={visibleModalDepoInadDetail}
      onCancel={() => {
        dispatch(closeModalDepoInadDetail())
        dispatch(setSelectedDepoInadId(null))
        form.resetFields()
      }}
      footer={null}
    >
      <Form
        form={form}
        labelCol={{ style: { width: 140 } }}
        labelAlign="left"
        colon={false}
        labelWrap={true}
        className="!p-6 shadow-lg"
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Surname" name="surname">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="givenName"
              label="Given Name"
              rules={[
                {
                  required: true,
                  message: 'Given Name is required',
                },
              ]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="DOB"
              name="dob"
              rules={[
                {
                  required: true,
                  message: 'DOB is required',
                },
              ]}
            >
              <DatePicker
                className="w-full"
                format={DISPLAY_DATE}
                placeholder="DD/MM/YYYY"
                disabledDate={current => {
                  return current && current > dayjs().endOf('day')
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Gender"
              name="gender"
              rules={[
                {
                  required: true,
                  message: 'Gender is required',
                },
              ]}
            >
              <Select
                options={[
                  { value: 0, label: 'Male' },
                  { value: 1, label: 'Female' },
                  { value: 2, label: 'Other' },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Passport"
              name="passport"
              rules={[
                {
                  required: true,
                  message: 'Passport is required',
                },
              ]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Citizenship"
              name="citizenship"
              rules={[
                {
                  required: true,
                  message: 'Citizenship is required',
                },
              ]}
            >
              <Select
                showSearch
                filterOption={selectFilterOption}
                options={countryData?.map((item: any) => ({
                  value: item.alpha2,
                  label: item.citizenship,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={10}>
            <Form.Item
              label="POB"
              name="pobCountry"
              rules={[
                {
                  required: true,
                  message: 'Nationality is required',
                },
              ]}
            >
              <Select
                showSearch
                filterOption={selectFilterOption}
                options={countryData?.map((item: any) => ({
                  value: item.alpha2,
                  label: item.name,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="pobCity"
              // rules={[{ required: true, message: 'City is required' }]}
            >
              <Select
                showSearch
                filterOption={selectFilterOption}
                options={provinceData?.map((item: any) => ({
                  value: item.code,
                  label: item.name,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="pobDetail"
              // rules={[{ required: true, message: 'pobDetail is required' }]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Refuse Location"
              name="refuseLocation"
              rules={[
                { required: true, message: 'Refuse Location is required' },
              ]}
            >
              <Select
                options={airportData?.items.map((item: any) => ({
                  value: item.iataCode,
                  label: `${item.iataCode} - ${item.apName}`,
                }))}
                showSearch
                filterOption={selectFilterValueOption}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Group"
              name="group"
              rules={[{ required: true, message: 'Group is required' }]}
            >
              <Select
                options={depoInadGroupData?.map((item: any) => ({
                  value: item.value,
                  label: item.displayName,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={10}>
            <Form.Item label="Pre. Flight" name="preFlight_date">
              <DatePicker
                className="w-full"
                format={DISPLAY_DATE}
                placeholder="DD/MM/YYYY"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="preFlight_flightNo">
              <Input placeholder="Flight No" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="preFlight_routing">
              <Input placeholder="Routing" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={10}>
            <Form.Item label="Arr. Flight" name="arrFlight_date">
              <DatePicker
                className="w-full"
                format={DISPLAY_DATE}
                placeholder="DD/MM/YYYY"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="arrFlight_flightNo">
              <Input placeholder="Flight No" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="arrFlight_routing">
              <Input placeholder="Routing" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={10}>
            <Form.Item label="Next Flight" name="nextFlight_date">
              <DatePicker
                className="w-full"
                format={DISPLAY_DATE}
                placeholder="DD/MM/YYYY"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="nextFlight_flightNo">
              <Input placeholder="Flight No" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="nextFlight_routing">
              <Input placeholder="Routing" />
            </Form.Item>
          </Col>
        </Row>
        <Col span={24}>
          <Form.List name="depoInadFees" initialValue={[{}]}>
            {(fields, { add, remove }) => (
              <div className="flex flex-col">
                {fields.map((field, index) => (
                  <div key={field.key} className="flex items-center gap-x-4">
                    <Form.Item
                      label={index === 0 ? 'SE Fee' : ' '}
                      name={[field.name, 'seFee']}
                      className="w-full"
                    >
                      <Input
                        pattern="[0-9]*"
                        onKeyPress={e => {
                          if (!/[0-9]/.test(e.key)) {
                            e.preventDefault()
                          }
                        }}
                      />
                    </Form.Item>
                    <Form.Item
                      label={index === 0 ? 'Remark' : ' '}
                      name={[field.name, 'remark']}
                      className="w-full"
                    >
                      <Input />
                    </Form.Item>
                    <CloseOutlined
                      onClick={() => remove(field.name)}
                      className={`${fields.length <= 1 ? '!hidden' : ''} -mt-6`}
                    />
                  </div>
                ))}
                <div className="flex justify-end">
                  <Button
                    type="link"
                    onClick={() => add()}
                    className="underline !text-primary"
                  >
                    Add more fee
                  </Button>
                </div>
              </div>
            )}
          </Form.List>
        </Col>
        <Col span={24}>
          <Form.Item
            label="File"
            valuePropName="fileList"
            getValueFromEvent={normFile}
            name="file"
          >
            <Upload
              maxCount={1}
              onPreview={async () => {
                const res = await mutationGetFile.mutateAsync({
                  filePath: depoInadDetail.filePath,
                  fileName: depoInadDetail.fileName,
                })
                FileSaver.saveAs(res, depoInadDetail.fileName)
              }}
            >
              <Button icon={<UploadOutlined />}>Browser files</Button>
            </Upload>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Reason" name="reason">
            <Input.TextArea rows={1} />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Source of Ticket" name="sourceOfTicket">
            <Input.TextArea rows={1} />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Object cause" name="objectiveCause">
            <Input.TextArea rows={1} />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Subjective cause" name="subjectiveCause">
            <Input.TextArea rows={1} />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            label="Solution/Result/Recommendation"
            layout="vertical"
            name="solutionResultRecommendation"
          >
            <Input.TextArea rows={1} />
          </Form.Item>
        </Col>
      </Form>
      <div className="flex justify-end mt-12">
        <Button type="primary" onClick={onSubmit}>
          Save
        </Button>
      </div>
    </Modal>
  )
}

export default ModalDepoInadDetail
