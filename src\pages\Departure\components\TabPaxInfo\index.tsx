import { EditFilled } from '@ant-design/icons'
import { Button, Checkbox, Col, Form, Input, Row } from 'antd'

const TabPaxInfo = () => {
  return (
    <div className="w-full py-3 bg-[#F5F9FA]">
      <Form colon={false}>
        <div className="flex w-full justify-between">
          <div className="text-lg font-bold">Pre-Flight</div>
          <Button
            icon={<EditFilled />}
            type="primary"
            onClick={() => {}}
            className="!hidden"
          >
            Chỉnh sửa
          </Button>
        </div>
        <div className="bg-[#E6F0F3] p-3 my-3 rounded-sm">
          <div className="flex gap-x-8 mt-3">
            <Form.Item label="CAPC" name="CAPC">
              <Input />
            </Form.Item>

            <Form.Item label="BKED" name="BKED">
              <Input />
            </Form.Item>

            <Form.Item label="Infant" name="Infant">
              <Input />
            </Form.Item>

            <Form.Item label="VIP" name="VIP">
              <Input />
            </Form.Item>
          </div>
          <div className="flex gap-x-8">
            <Form.Item label="CIP" name="CIP">
              <Input />
            </Form.Item>

            <Form.Item label="FFP" name="FFP">
              <Input />
            </Form.Item>

            <Form.Item label="Transit" name="Transit">
              <Input />
            </Form.Item>

            <Form.Item label="Outbound" name="Outbound">
              <Input />
            </Form.Item>

            <Form.Item label="PR" name="PR">
              <Input />
            </Form.Item>
          </div>
          <div className="flex gap-x-8">
            <Form.Item label="Sp. service">
              <Input />
            </Form.Item>
            <Form.Item label="Inbound">
              <Input />
            </Form.Item>
            <Form.Item label="Option town">
              <Input />
            </Form.Item>
          </div>
          <Form.Item className="w-full" label="Special meals">
            <Input />
          </Form.Item>
        </div>

        <div className="text-lg font-bold mb-3">Post-Flight</div>
        <div className="bg-[#E6F0F3] p-3 rounded-sm">
          <Row gutter={16}>
            <Col span={3}>
              <Form.Item label="CAPC">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="OBRD">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="CK_INF">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="CK_TRN">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="OFFLN">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="MISCT">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="GOSHO">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="VOLDN">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={3}>
              <Form.Item label="OFFLK">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="NOSHO">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="IVLUP">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="IVLDN">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="CHGFL">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="NOREC">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="VOLUP">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item label="TSFRD">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row className="flex gap-x-3">
            <Col span={3}>
              <Form.Item label="WEBKIN">
                <Input />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item>
                <Checkbox>Define unusual flight</Checkbox>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item className="w-full" label="Remark">
            <Input />
          </Form.Item>
        </div>
      </Form>
    </div>
  )
}

export default TabPaxInfo
