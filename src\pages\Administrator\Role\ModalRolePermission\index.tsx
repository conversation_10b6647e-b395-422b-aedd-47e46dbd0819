/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { PROVIDER_NAME_ROLE } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getPermission, updatePermission } from '@/src/service/permission'
import {
  closeRolePermissionModal,
  setPermission,
  setPermissionsFromApi,
  setSelectedRoleId,
  setSelectedRoleName,
} from '@/src/store/RoleSlice'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Checkbox, Divider, Modal, Tabs, Tree } from 'antd'
import { useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

const buildTreeDataFromPermissions = (permissions: any[]) => {
  const map: Record<string, any> = {}
  const tree: any[] = []

  permissions.map(p => {
    map[p.name] = {
      title: p.displayName,
      key: p.name,
      children: [],
    }
  })

  permissions.map(p => {
    if (p.parentName && map[p.parentName]) {
      map[p.parentName].children.push(map[p.name])
    } else if (!p.parentName) {
      tree.push(map[p.name])
    }
  })

  return tree
}

const ModalRolePermission = () => {
  const { visibleRolePermissionModal, selectedRoleName, permissions } =
    useAppSelector(state => state.role)
  const { locale } = useAppSelector(state => state.global)
  const dispatch = useAppDispatch()

  const { t } = useTranslation()

  const { data: permissionData, isLoading: isPermissionLoading } = useQuery({
    queryKey: ['permission-list', selectedRoleName, locale.locale],
    queryFn: () =>
      getPermission(
        PROVIDER_NAME_ROLE,
        selectedRoleName ?? '',
        locale.locale === 'vi' ? 'vi-VN' : 'en-US'
      ),
    enabled: !!selectedRoleName,
  })

  const mutation = useMutation({
    mutationFn: async (values: any) => {
      return updatePermission(
        PROVIDER_NAME_ROLE,
        selectedRoleName ?? '',
        values
      )
    },
    onSuccess: () => {
      dispatch(closeRolePermissionModal())
      dispatch(setSelectedRoleName(''))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const onOK = () => {
    const allGroupPermissions =
      permissionData?.groups.flatMap((group: any) => group.permissions) || []

    const currentPermissionsMap = Object.entries(permissions).reduce(
      (acc: Record<string, boolean>, [name, value]) => {
        acc[name] = value
        return acc
      },
      {}
    )

    allGroupPermissions.forEach((parent: any) => {
      if (!parent.parentName) {
        // This is a parent permission
        const children = allGroupPermissions.filter(
          (child: any) => child.parentName === parent.name
        )

        if (children.length > 0) {
          const anyChildChecked = children.some(
            (child: any) => currentPermissionsMap[child.name] === true
          )

          if (anyChildChecked) {
            currentPermissionsMap[parent.name] = true
          }
        }
      }
    })

    const result = Object.entries(currentPermissionsMap).map(
      ([key, value]) => ({
        name: key,
        isGranted: value,
      })
    )
    mutation.mutate({
      permissions: result,
    })
    dispatch(setSelectedRoleId(''))
  }

  const onCancel = () => {
    dispatch(closeRolePermissionModal())
    dispatch(setSelectedRoleName(''))
    dispatch(setSelectedRoleId(''))
  }

  const items = useMemo(() => {
    return (
      permissionData?.groups
        .filter((group: any) => ['AbpIdentity', 'Lao'].includes(group.name))
        .map((group: any) => {
          const checkedCount = group.permissions.filter((p: any) => {
            const isChecked = permissions[p.name]
            const hasNoParent = !p.parentName
            const hasNoChildren = !group.permissions.some(
              (child: any) => child.parentName === p.name
            )

            return isChecked && (p.parentName || (hasNoParent && hasNoChildren))
          }).length

          return {
            key: group.name,
            label: (
              <div className="w-40 text-start">
                {group.displayName} ({checkedCount})
              </div>
            ),
            children: (
              <>
                <Checkbox
                  checked={group.permissions.every(
                    (p: any) => permissions[p.name]
                  )}
                  indeterminate={
                    group.permissions.some((p: any) => permissions[p.name]) &&
                    !group.permissions.every((p: any) => permissions[p.name])
                  }
                  onChange={e => {
                    const checked = e.target.checked
                    group.permissions.forEach((p: any) => {
                      dispatch(
                        setPermission({
                          key: p.name,
                          value: checked,
                        })
                      )
                    })
                  }}
                >
                  {t('role.selectAll')}
                </Checkbox>

                <Divider />

                <Tree
                  treeData={buildTreeDataFromPermissions(group.permissions)}
                  checkable
                  checkStrictly
                  defaultExpandAll
                  checkedKeys={group.permissions
                    .filter((p: any) => permissions[p.name])
                    .map((p: any) => p.name)}
                  onCheck={checkedKeysValue => {
                    const checkedKeys = Array.isArray(checkedKeysValue)
                      ? checkedKeysValue
                      : checkedKeysValue.checked

                    // Xác định node nào vừa được thay đổi
                    const currentCheckedKeys = group.permissions
                      .filter((p: any) => permissions[p.name])
                      .map((p: any) => p.name)

                    const addedKeys = checkedKeys.filter(
                      key => !currentCheckedKeys.includes(key)
                    )
                    const removedKeys = currentCheckedKeys.filter(
                      (key: any) => !checkedKeys.includes(key)
                    )

                    let finalCheckedKeys = [...checkedKeys]

                    // Xử lý khi check/uncheck
                    if (addedKeys.length > 0) {
                      // Có node được check
                      const addedKey = addedKeys[0]
                      const addedPermission = group.permissions.find(
                        (p: any) => p.name === addedKey
                      )

                      if (addedPermission) {
                        // if (!addedPermission.parentName) {
                        //   // Check cha -> check tất cả child
                        //   const children = group.permissions.filter(
                        //     (child: any) =>
                        //       child.parentName === addedPermission.name
                        //   )

                        //   children.forEach((child: any) => {
                        //     if (!finalCheckedKeys.includes(child.name)) {
                        //       finalCheckedKeys.push(child.name)
                        //     }
                        //   })
                        // } else {
                        // Check child -> check cha của nó
                        if (
                          !finalCheckedKeys.includes(addedPermission.parentName)
                        ) {
                          finalCheckedKeys.push(addedPermission.parentName)
                        }
                      }
                      // }
                    } else if (removedKeys.length > 0) {
                      // Có node được uncheck
                      const removedKey = removedKeys[0]
                      const removedPermission = group.permissions.find(
                        (p: any) => p.name === removedKey
                      )

                      if (removedPermission) {
                        if (!removedPermission.parentName) {
                          // Uncheck cha -> uncheck tất cả child
                          const children = group.permissions.filter(
                            (child: any) =>
                              child.parentName === removedPermission.name
                          )

                          children.forEach((child: any) => {
                            finalCheckedKeys = finalCheckedKeys.filter(
                              key => key !== child.name
                            )
                          })
                        } else {
                          // Uncheck child -> kiểm tra có child nào khác còn check không
                          const siblings = group.permissions.filter(
                            (sibling: any) =>
                              sibling.parentName ===
                                removedPermission.parentName &&
                              sibling.name !== removedPermission.name
                          )

                          const anyChildrenStillChecked = siblings.some(
                            (sibling: any) =>
                              finalCheckedKeys.includes(sibling.name)
                          )

                          if (!anyChildrenStillChecked) {
                            // Không có child nào được check -> uncheck cha
                            finalCheckedKeys = finalCheckedKeys.filter(
                              key => key !== removedPermission.parentName
                            )
                          }
                        }
                      }
                    }

                    // Cập nhật Redux state với kết quả cuối cùng
                    group.permissions.forEach((p: any) => {
                      const isChecked = finalCheckedKeys.includes(p.name)
                      dispatch(
                        setPermission({
                          key: p.name,
                          value: isChecked,
                        })
                      )
                    })
                  }}
                />
              </>
            ),
          }
        }) ?? []
    )
  }, [permissionData, permissions])

  useEffect(() => {
    if (permissionData?.groups) {
      const flatPermissions = permissionData.groups
        .flatMap((group: any) => group.permissions)
        .map((p: any) => ({
          name: p.name,
          isGranted: p.isGranted,
        }))
      dispatch(setPermissionsFromApi({ permissions: flatPermissions }))
    }
  }, [permissionData, dispatch])

  return (
    <Modal
      width={1000}
      open={visibleRolePermissionModal}
      title={`${t('role.permission')} ${selectedRoleName ?? ''}`}
      onOk={onOK}
      onCancel={onCancel}
      confirmLoading={mutation.isPending}
      loading={isPermissionLoading}
    >
      <Tabs tabPosition="left" items={items} />
    </Modal>
  )
}

export default ModalRolePermission
