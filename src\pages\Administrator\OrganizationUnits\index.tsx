import { Col, Row } from 'antd'
import OrganizationTree from './components/OrganizationTree'
import OrganizationUnitInfo from './components/OrganizationUnitInfo'
import { useTranslation } from 'react-i18next'
const OrganizationUnitsPage = () => {
  const { t } = useTranslation()
  return (
    <div>
      <h1 className="text-black text-lg font-bold mb-5">
        {t('organization.management')}
      </h1>
      <Row gutter={[16, 16]}>
        <Col sm={24} md={8}>
          <OrganizationTree />
        </Col>
        <Col sm={24} md={16}>
          <OrganizationUnitInfo />
        </Col>
      </Row>
    </div>
  )
}

export default OrganizationUnitsPage
