/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/flights`

const getUnusualInfoList = async (flightId: string, params?: any) => {
  const api = `${prefix}/${flightId}/unusual-info`
  const response = await axios.get(api, params)
  return response.data
}

const createUnusualInfo = async (flightId: string, body: any) => {
  const api = `${prefix}/${flightId}/unusual-info`
  const response = await axios.post(api, body)
  return response.data
}

const updateUnusualInfo = async (
  flightId: string,
  unusualInfoId: string,
  body: any
) => {
  const api = `${prefix}/${flightId}/unusual-info/${unusualInfoId}`
  const response = await axios.put(api, body)
  return response.data
}

const getUnusualInfoDetail = async (
  flightId: string,
  unusualInfoId: string
) => {
  const api = `${prefix}/${flightId}/unusual-info/${unusualInfoId}`
  const response = await axios.get(api)
  return response.data
}

const deleteUnusualInfo = async (flightId: string, unusualInfoId: string) => {
  const api = `${prefix}/${flightId}/unusual-info/${unusualInfoId}`
  const response = await axios.delete(api)
  return response.data
}

export {
  createUnusualInfo,
  getUnusualInfoDetail,
  updateUnusualInfo,
  getUnusualInfoList,
  deleteUnusualInfo,
}
