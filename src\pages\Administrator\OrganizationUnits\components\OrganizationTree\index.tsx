import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  getDepartments,
  removeDepartment,
} from '@/src/service/organization_unit'
import {
  setModalMode,
  setParams,
  setParentSelectedOrganizationUnit,
  setSelectedOrganizationUnit,
  setVisibleOrganizationUnit,
  toggleOrganizationUnitModal,
} from '@/src/store/OrganizationUnitSlice'
import { FolderOutlined, PlusOutlined } from '@ant-design/icons'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Button, Card, Modal, type MenuProps } from 'antd'
import DirectoryTree from 'antd/es/tree/DirectoryTree'
import { Dropdown } from 'antd/lib'
import { useTranslation } from 'react-i18next'
import AddOrganizationModal from './AddOrganizationModal'
import { handleApiError } from '@/src/helper/handleApiError'
import {
  LAO_DEPARTMENT_CREATE,
  LAO_DEPARTMENT_DELETE,
  LAO_DEPARTMENT_EDIT,
} from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'

type TreeNode = {
  key: string
  title: string
  children: TreeNode[]
  organizationUnitId: string | null
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function mapOrganizationTree(data: any[]): TreeNode[] {
  return data.map(item => ({
    key: item.id,
    title: item.name,
    children: mapOrganizationTree(item.children || []),
    organizationUnitId: item.organizationUnitId,
  }))
}

const OrganizationTree = () => {
  const { t } = useTranslation()
  const { selectedId } = useAppSelector(state => state.organizationUnit)
  const dispatch = useAppDispatch()
  const { hasPermission } = usePermission()
  const queryClient = useQueryClient()

  const { mutate: remove } = useMutation({
    mutationFn: removeDepartment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departmentsTree'] })
      dispatch(setSelectedOrganizationUnit(null))
      dispatch(setParentSelectedOrganizationUnit(null))
      dispatch(setVisibleOrganizationUnit(null))
    },
    onError: error => {
      handleApiError(error)
    },
  })
  const menuItems: MenuProps['items'] = [
    hasPermission(LAO_DEPARTMENT_EDIT) && {
      label: t('organization.organizationTree.edit'),
      key: '1',
      onClick: () => {
        dispatch(setModalMode('edit'))
        dispatch(toggleOrganizationUnitModal())
      },
    },
    hasPermission(LAO_DEPARTMENT_CREATE) && {
      label: t('organization.organizationTree.addSub'),
      key: '2',
      onClick: () => {
        dispatch(setModalMode('create-sub'))
        dispatch(toggleOrganizationUnitModal())
      },
    },
    hasPermission(LAO_DEPARTMENT_DELETE) && {
      label: t('organization.organizationTree.delete'),
      key: '3',
      onClick: () => {
        Modal.confirm({
          title: t('common.confirmDeleteTitle'),
          content: t('common.confirmDeleteMessage'),
          okText: t('common.yes'),
          cancelText: t('common.no'),
          onOk: () => remove(selectedId!),
        })
      },
    },
  ].filter(Boolean) as MenuProps['items']

  const { data: departments = [], isLoading } = useQuery({
    queryKey: ['departmentsTree'],
    queryFn: () => getDepartments(),
  })

  return (
    <>
      <Card
        title={t('organization.organizationTree._')}
        styles={{
          header: {
            borderBottom: 'none',
          },
          body: {
            paddingTop: 0,
          },
        }}
        loading={isLoading}
        extra={
          hasPermission(LAO_DEPARTMENT_CREATE) && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                dispatch(setModalMode('create'))
                dispatch(setSelectedOrganizationUnit(null))
                dispatch(toggleOrganizationUnitModal())
              }}
            >
              {t('organization.organizationTree.add')}
            </Button>
          )
        }
      >
        <Dropdown menu={{ items: menuItems }} trigger={['contextMenu']}>
          <DirectoryTree
            treeData={mapOrganizationTree(departments)}
            showIcon
            draggable={false}
            icon={<FolderOutlined />}
            multiple={false}
            onSelect={(_, event) => {
              dispatch(setVisibleOrganizationUnit(event.node.key))
              dispatch(setParams({ skipCount: 0 }))
            }}
            onRightClick={event => {
              dispatch(setSelectedOrganizationUnit(event.node.key))
              dispatch(
                setParentSelectedOrganizationUnit(event.node.organizationUnitId)
              )
            }}
            defaultExpandAll
          />
        </Dropdown>
      </Card>
      <AddOrganizationModal />
    </>
  )
}

export default OrganizationTree
