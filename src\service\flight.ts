/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'
import qs from 'qs'
import type { IFlightParamsType } from '../schema/IFlightType'

const prefix = `/api/v1/flights`

const getFlight = async (params: IFlightParamsType) => {
  const api = `${prefix}`
  const response = await axios.get(api, {
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' }),
  })
  return response.data
}

const getPTSFlight = async (flightId: string, language?: any) => {
  const api = `${prefix}/${flightId}/pts`
  const response = await axios.get(api, {
    headers: {
      'Accept-Language': language === 'vi' ? 'vi-VN' : language,
    },
  })
  return response.data
}

const recordPTSFlight = async (flightId: string, ptsId: string, body: any) => {
  const api = `${prefix}/${flightId}/pts/${ptsId}`

  const response = await axios.put(api, body)
  return response.data
}

const addPTStoFlight = async (flightId: string) => {
  const api = `${prefix}/${flightId}/pts`

  const response = await axios.post(api)
  return response.data
}

const getFlightDetail = async (id: string) => {
  const api = `${prefix}/${id}/info`
  const response = await axios.get(api)
  return response.data
}

const updatePTSMasterFlight = async (
  flightId: string,
  ptsId: string,
  body: any
) => {
  const api = `${prefix}/${flightId}/pts/${ptsId}`

  const response = await axios.put(api, body)
  return response.data
}

const addPTSMasterAssignableToFlight = async (flightId: string, body: any) => {
  const api = `${prefix}/${flightId}/pts`
  const response = await axios.put(api, body)
  return response.data
}

const resetPTSFlight = async (flightId: string) => {
  const api = `${prefix}/${flightId}/reset-pts`
  const response = await axios.put(api)
  return response.data
}

const getLateGroupTask = async (
  flightId: string,
  ptsId: string,
  params: any
) => {
  const api = `${prefix}/${flightId}/pts/${ptsId}/late-group-tasks`
  const response = await axios.get(api, { params })
  return response.data
}

const updateReasonLateGroupTask = async (
  flightId: string,
  ptsId: string,
  body: any
) => {
  const api = `${prefix}/${flightId}/pts/${ptsId}/late-group-tasks`
  const response = await axios.post(api, body)
  return response.data
}

const updateFlightDetail = async (body: any) => {
  const api = `${prefix}/${body.id}`
  const response = await axios.put(api, body)
  return response.data
}

export {
  addPTSMasterAssignableToFlight,
  addPTStoFlight,
  getFlight,
  getFlightDetail,
  getLateGroupTask,
  getPTSFlight,
  recordPTSFlight,
  resetPTSFlight,
  updatePTSMasterFlight,
  updateReasonLateGroupTask,
  updateFlightDetail,
}
