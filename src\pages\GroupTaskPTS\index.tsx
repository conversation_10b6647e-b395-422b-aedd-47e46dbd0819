/* eslint-disable @typescript-eslint/no-explicit-any */
import { DISPLAY_DATE } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import type { IGroupPTSTask } from '@/src/schema/IGroupPTSTask'
import {
  createGroupPTSTask,
  getGroupPTSTask,
  removeGroupPTSTask,
  updateGroupPTSTask,
} from '@/src/service/group_pts_task'
import {
  closeGroupTaskPTSModal,
  openGroupTaskPTSModal,
  setParams,
  setSelectedId,
} from '@/src/store/GroupTaskPTSSlice'
import { DeleteFilled, EditFilled, PlusOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Space,
  Table,
  type TableProps,
} from 'antd'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import ShowTotal from '@/src/components/Showtotal'
import {
  LAO_GROUP_PTS_TASKS_CREATE,
  LAO_GROUP_PTS_TASKS_DELETE,
  LAO_GROUP_PTS_TASKS_EDIT,
} from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'

const GroupTaskPTS = () => {
  const { t } = useTranslation()
  const { hasPermission } = usePermission()
  const { params } = useAppSelector(state => state.groupTaskPTS)

  const dispatch = useAppDispatch()

  const [form] = Form.useForm()

  const { visibleModal, selectedId } = useAppSelector(
    state => state.groupTaskPTS
  )

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['group-pts-task-list', params],
    queryFn: () => getGroupPTSTask(params),
  })

  const mutationCreate = useMutation({
    mutationFn: createGroupPTSTask,
    onSuccess: () => {
      refetch()
      dispatch(closeGroupTaskPTSModal())
      form.resetFields()
      message.success(t('groupTaskPTS.createSuccess'))
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationUpdate = useMutation({
    mutationFn: updateGroupPTSTask,
    onSuccess: () => {
      refetch()
      dispatch(closeGroupTaskPTSModal())
      form.resetFields()
      dispatch(setSelectedId(''))
      message.success(t('groupTaskPTS.updateSuccess'))
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationRemove = useMutation({
    mutationFn: async (id: string) => removeGroupPTSTask(id),
    onSuccess: () => {
      refetch()
      message.success(t('groupTaskPTS.deleteSuccess'))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const onSubmit = async () => {
    const values = await form.validateFields()

    if (selectedId) {
      mutationUpdate.mutate({ ...values, id: selectedId })
    } else {
      mutationCreate.mutate(values)
    }
  }

  const columns: TableProps<IGroupPTSTask>['columns'] = [
    { title: t('groupTaskPTS.code'), dataIndex: 'code', key: 'code' },
    { title: t('groupTaskPTS.name'), dataIndex: 'name', key: 'name' },
    {
      title: t('groupTaskPTS.description'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('groupTaskPTS.createdDate'),
      dataIndex: 'creationTime',
      key: 'creationTime',
      align: 'center',
      render: (creationTime: string) => (
        <>{dayjs(creationTime).format(DISPLAY_DATE)}</>
      ),
    },
    {
      title: t('groupTaskPTS.action'),
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      hidden:
        !hasPermission(LAO_GROUP_PTS_TASKS_EDIT) &&
        !hasPermission(LAO_GROUP_PTS_TASKS_DELETE),
      width: 100,
      render: (_: any, record: IGroupPTSTask) => (
        <Space key={record.id}>
          {hasPermission(LAO_GROUP_PTS_TASKS_EDIT) && (
            <EditFilled
              key={record.id + 'edit'}
              className="hover:bg-slate-200 p-1 rounded-sm"
              onClick={() => {
                dispatch(openGroupTaskPTSModal())
                dispatch(setSelectedId(record.id))
                form.setFieldsValue(record)
              }}
            />
          )}
          {hasPermission(LAO_GROUP_PTS_TASKS_DELETE) && (
            <DeleteFilled
              key={record.id + 'delete'}
              className="hover:bg-slate-200 p-1 rounded-sm"
              onClick={() => {
                Modal.confirm({
                  title: t('common.confirmDeleteTitle'),
                  content: t('common.confirmDeleteMessage'),
                  okText: t('common.yes'),
                  cancelText: t('common.no'),
                  onOk: () => mutationRemove.mutate(record.id),
                })
              }}
            />
          )}
        </Space>
      ),
    },
  ]

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex justify-between">
        <div className="text-lg font-bold text-black">
          {t('groupTaskPTS.list')}
        </div>
        {hasPermission(LAO_GROUP_PTS_TASKS_CREATE) && (
          <Button
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => {
              dispatch(openGroupTaskPTSModal())
            }}
          >
            {t('groupTaskPTS.add')}
          </Button>
        )}
      </div>
      <Table
        bordered
        size="small"
        loading={isLoading}
        dataSource={isLoading ? [] : data?.items}
        columns={columns}
        className={`${styles.whiteHeader}`}
        pagination={{
          total: isLoading ? 0 : data?.totalCount,
          current: params.SkipCount / params.MaxResultCount + 1,
          pageSize: params.MaxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                SkipCount: (page - 1) * pageSize,
                MaxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        rowKey={record => record.id}
      />
      <Modal
        className="!w-[800px]"
        open={visibleModal}
        onOk={onSubmit}
        onCancel={() => {
          form.resetFields()
          dispatch(closeGroupTaskPTSModal())
          dispatch(setSelectedId(''))
        }}
        closable={false}
        title={selectedId ? t('groupTaskPTS.edit') : t('groupTaskPTS.add')}
      >
        <Form
          form={form}
          className="!p-5"
          labelCol={{ flex: '220px' }}
          labelAlign="left"
        >
          <Form.Item
            label={t('groupTaskPTS.code')}
            name="code"
            rules={[
              { required: true, message: t('groupTaskPTS.codeRequired') },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('groupTaskPTS.name')}
            name="name"
            rules={[
              { required: true, message: t('groupTaskPTS.nameRequired') },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item label={t('groupTaskPTS.description')} name="description">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default GroupTaskPTS
