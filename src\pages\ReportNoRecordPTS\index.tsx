/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import DropdownChangeColumn from '@/src/components/DropdownColumn'
import ShowTotal from '@/src/components/Showtotal'
import {
  DISPLAY_DATE,
  ISO_DATETIME,
  ISO_DATETIME_NOSECOND,
  REPORT_NO_RECORD_PTS,
} from '@/src/constants'
import { normalizeText } from '@/src/helper/normalizeText'
import { isValidRangeDate } from '@/src/helper/validDate'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import { exportExcelReportGTList, getReportGTList } from '@/src/service/report'
import {
  closeViewColumnNoRecordPTS,
  openViewColumnNoRecordPTS,
  setCheckListReportNoRecordPTS,
} from '@/src/store/ReportSlice'
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { Button, DatePicker, Input, Table, type TableColumnsType } from 'antd'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import moment from 'moment'
import QueryString from 'qs'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import styles from './index.module.scss'

const { RangePicker } = DatePicker

const legState_ARR = ['DEP', 'ARR', 'ON', 'OFF', 'IN', 'OUT']

const ReportNoRecordPTSPage = () => {
  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search
  const navigate = useNavigate()
  const [paramReportNoRecordPTS, setParamReportNoRecordPTS] = useState<{
    skipCount: number
    maxResultCount: number
    fromDate: string
    toDate: string
    airport: string
    keyWord: string
  }>(() => {
    const params = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof params.fromDate === 'string' ? dayjs(params.fromDate) : null
    const to = typeof params.toDate === 'string' ? dayjs(params.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...params,
      skipCount: Number(params.skipCount) || 0,
      maxResultCount: Number(params.maxResultCount) || 20,
      fromDate:
        valid && from
          ? from.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
    }

    return value
  })

  const [flightKeyWord, setFlightKeyWord] = useState<undefined | string>(
    undefined
  )
  const [airportKeyWord, setAirportKeyWord] = useState<undefined | string>(
    undefined
  )
  const debouncedflightKeyword = useDebounce(flightKeyWord, 500)
  const debouncedAirportKeyword = useDebounce(airportKeyWord, 500)
  const { t } = useTranslation()
  const dispatch = useAppDispatch()
  const { visibleViewColumnNoRecordPTS, checkListReportNoRecordPTS } =
    useAppSelector(state => state.report)

  const columns: TableColumnsType<any> = [
    {
      title: t('table.order'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_value, _record, index) =>
        paramReportNoRecordPTS.skipCount + index + 1,
    },
    {
      title: t('table.aircraft'),
      dataIndex: 'acSubType',
      key: 'airCraft',
      width: 100,
      align: 'center',
    },
    {
      title: 'Register No',
      dataIndex: 'acRegistration',
      key: 'registerNo',
      width: 100,
      align: 'center',
    },
    {
      title: 'Flight No',
      key: 'flightNo',
      width: 100,
      align: 'center',
      render: (_value, record) => record.fnCarrier + record.fnNumber,
    },
    {
      title: 'Routing',
      key: 'routing',
      width: 100,
      align: 'center',
      render: (_value, record) => `${record.depApSched}-${record.arrApSched}`,
    },
    {
      title: 'STD',
      dataIndex: 'depSchedDt',
      key: 'std',
      width: 120,
      align: 'center',
      render: value => <>{moment(value).format(ISO_DATETIME_NOSECOND)}</>,
    },
    {
      title: 'ETD',
      dataIndex: 'depDt',
      key: 'etd',
      width: 120,
      align: 'center',
      render: (value: any) => moment(value).format(ISO_DATETIME_NOSECOND),
    },
    {
      title: 'BH',
      dataIndex: 'flightHrs',
      key: 'bh',
      align: 'center',
      width: 80,
    },
    {
      title: 'ATD',
      key: 'actual',
      dataIndex: 'depDt',
      align: 'center',
      width: 120,
      render: (value: any, record: any) =>
        legState_ARR.includes(record.legState)
          ? moment(value).format(ISO_DATETIME_NOSECOND)
          : null,
    },
    {
      title: t('table.status'),
      dataIndex: 'legState',
      key: 'status',
      width: 80,
      align: 'center',
    },
    {
      title: t('table.reason'),
      dataIndex: 'reason',
      key: 'reason',
      width: 240,
    },
  ]

  const { data, isLoading } = useQuery({
    queryKey: ['report-flight', paramReportNoRecordPTS],
    queryFn: () =>
      getReportGTList({
        ...paramReportNoRecordPTS,
        type: REPORT_NO_RECORD_PTS,
      }),
  })

  const { refetch: exportExcelReport, isLoading: isLoadingExport } = useQuery({
    queryKey: ['report-no-record-pts', paramReportNoRecordPTS],
    queryFn: () =>
      exportExcelReportGTList({
        ...paramReportNoRecordPTS,
        type: REPORT_NO_RECORD_PTS,
        skipCount: 0,
      }),
    enabled: false,
  })

  const handleExport = async () => {
    const { data } = await exportExcelReport()
    const fileName = `${t('common.reportName')}_${normalizeText(t('report.reportNoRecordPTS'))}.csv`
    return FileSaver.saveAs(data, fileName)
  }

  const newColumns = columns.map(item => ({
    ...item,
    hidden: !checkListReportNoRecordPTS.includes(item.key as string),
  }))

  useEffect(() => {
    dispatch(
      setCheckListReportNoRecordPTS(columns.map(item => item.key as string))
    )
  }, [])

  useEffect(() => {
    if (flightKeyWord !== undefined) {
      setParamReportNoRecordPTS({
        ...paramReportNoRecordPTS,
        skipCount: 0,
        keyWord: debouncedflightKeyword as string,
      })
    }
  }, [debouncedflightKeyword])

  useEffect(() => {
    if (airportKeyWord !== undefined) {
      setParamReportNoRecordPTS({
        ...paramReportNoRecordPTS,
        skipCount: 0,
        airport: debouncedAirportKeyword as string,
      })
    }
  }, [debouncedAirportKeyword])

  useEffect(() => {
    navigate(
      `/report/report-no-record-pts?${QueryString.stringify(paramReportNoRecordPTS)}`,
      { replace: true }
    )
  }, [paramReportNoRecordPTS])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex justify-between w-full">
        <div className="text-lg font-bold text-black">
          {t('report.reportNoRecordPTS')}
        </div>
        <div className="flex flex-row gap-x-2 max-2xl:flex-wrap max-2xl:gap-y-2">
          <RangePicker
            className="!h-max"
            allowClear={false}
            defaultValue={[
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('fromDate'))
                : dayjs().startOf('day'),
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('toDate'))
                : dayjs().endOf('day'),
            ]}
            format={DISPLAY_DATE}
            onChange={value => {
              if (value) {
                const newParams = {
                  ...paramReportNoRecordPTS,
                  skipCount: 0,
                  fromDate: dayjs(value[0]).startOf('day').format(ISO_DATETIME),
                  toDate: dayjs(value[1]).endOf('day').format(ISO_DATETIME),
                }
                return setParamReportNoRecordPTS(newParams)
              }
            }}
          />
          <Input
            placeholder={t('report.searchFlightPlaceholder')}
            className="!w-48 h-max"
            prefix={<SearchOutlined />}
            onChange={e => {
              setFlightKeyWord(e.target.value)
            }}
            defaultValue={searchParams.get('keyWord') || ''}
          />
          <Input
            placeholder={t('report.searchAirportPlaceholder')}
            className="!w-48 h-max"
            prefix={<SearchOutlined />}
            onChange={e => {
              setAirportKeyWord(e.target.value)
            }}
            defaultValue={searchParams.get('airport') || ''}
          />
          <Button
            icon={<DownloadOutlined />}
            loading={isLoadingExport}
            onClick={handleExport}
            disabled={isLoadingExport}
          >
            {t('report.download')}
          </Button>
          <DropdownChangeColumn
            columns={columns || []}
            onChangeColumn={val => {
              dispatch(setCheckListReportNoRecordPTS(val))
            }}
            onOk={() => dispatch(openViewColumnNoRecordPTS())}
            onCancel={() => dispatch(closeViewColumnNoRecordPTS())}
            open={visibleViewColumnNoRecordPTS}
          />
        </div>
      </div>
      <Table
        bordered
        dataSource={data?.items || []}
        columns={newColumns}
        className={`${styles.whiteHeader}`}
        size="small"
        loading={isLoading}
        rowKey={record => record.id}
        pagination={{
          pageSize: paramReportNoRecordPTS.maxResultCount,
          current:
            paramReportNoRecordPTS.skipCount /
              paramReportNoRecordPTS.maxResultCount +
            1,
          total: data?.totalCount,
          showSizeChanger: true,
          onChange(page, pageSize) {
            setParamReportNoRecordPTS({
              ...paramReportNoRecordPTS,
              skipCount: (page - 1) * pageSize,
              maxResultCount: pageSize,
            })
          },
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
    </div>
  )
}

export default ReportNoRecordPTSPage
