/* eslint-disable react-hooks/exhaustive-deps */
import { notask } from '@/src/assets/image'
import { DISPLAY_DATE, ISO_DATETIME, TIME_DEBOUNCE } from '@/src/constants'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import type { IAirport } from '@/src/schema/IAirport'
import type { IFleet } from '@/src/schema/IFleet'
import type { IPTSMaster } from '@/src/schema/IPTSMaster'
import { getAirport } from '@/src/service/airport'
import { getFleet } from '@/src/service/fleet'
import { getPTSMaster } from '@/src/service/pts_master'
import {
  closeFilterModal,
  openFilterModal,
  resetState,
  setParams,
  setRecord,
  setSearchDebounce,
} from '@/src/store/PTSSlice'
import {
  CopyOutlined,
  EditFilled,
  FilterFilled,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  DatePicker,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Popover,
  Select,
  Space,
  Table,
  type TableProps,
} from 'antd'
import dayjs from 'dayjs'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import styles from './index.module.scss'
import ShowTotal from '@/src/components/Showtotal'
import {
  LAO_PTS_MASTERS_CREATE,
  LAO_PTS_MASTERS_EDIT,
} from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'

const PTSPage = () => {
  const navigate = useNavigate()

  const [form] = Form.useForm()

  const { params } = useAppSelector(state => state.pts)

  const dispatch = useAppDispatch()

  const { t } = useTranslation()
  const { hasPermission } = usePermission()
  const { KeyWord, visibleFilterModal } = useAppSelector(state => state.pts)

  const KeyWordDebounce = useDebounce(KeyWord, TIME_DEBOUNCE)

  const { data, isLoading } = useQuery({
    queryKey: ['pts-masters-list', params],
    queryFn: () => getPTSMaster(params),
  })

  const { data: fleetData } = useQuery({
    queryKey: ['fleet-list'],
    queryFn: () =>
      getFleet({
        MaxResultCount: 1000,
      }),
  })

  const { data: airportData } = useQuery({
    queryKey: ['airport-list'],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
  })

  const columns: TableProps<IPTSMaster>['columns'] = [
    { title: t('pts.code'), dataIndex: 'code', key: 'code' },
    { title: t('pts.name'), dataIndex: 'name', key: 'name' },
    {
      title: t('pts.aircraftType'),
      dataIndex: 'fleetCode',
      key: 'fleetCode',
      align: 'center',
    },
    {
      title: t('pts.airport'),
      dataIndex: 'airportName',
      key: 'airportName',
      align: 'center',
    },
    {
      title: t('pts.network'),
      dataIndex: 'network',
      key: 'network',
      align: 'center',
    },
    {
      title: t('pts.time'),
      dataIndex: '',
      key: 'time',
      align: 'center',
      render: (_value, record: IPTSMaster) => (
        <>
          {record.bhFrom} - {record.bhTo}
        </>
      ),
    },
    {
      key: 'groundTime',
      dataIndex: 'groundTime',
      align: 'center',
      title: (
        <>
          <span>{t('pts.groundTime')}</span>
        </>
      ),
    },
    {
      key: 'fromDate',
      dataIndex: 'fromDate',
      title: t('pts.fromDate'),
      align: 'center',
      render: (date: string) => dayjs(date).format(DISPLAY_DATE),
    },
    {
      key: 'toDate',
      dataIndex: 'toDate',
      title: t('pts.toDate'),
      align: 'center',
      render: (date: string) => dayjs(date).format(DISPLAY_DATE),
    },
    // {
    //   key: 'status',
    //   title: t('pts.status'),
    //   align: 'center',
    //   render: (status: number) => (
    //     <Tag
    //       color={`${status === 1 ? '#E6F7EC' : '#F7E6E6'}`}
    //       className={`${status === 1 ? '!text-positive' : '!text-negative'} !text-xs !font-medium !rounded-full`}
    //     >
    //       {t('pts.using')}
    //     </Tag>
    //   ),
    // },
    {
      key: '#',
      title: t('pts.action'),
      align: 'center',
      width: 100,
      hidden:
        !hasPermission(LAO_PTS_MASTERS_CREATE) &&
        !hasPermission(LAO_PTS_MASTERS_EDIT),
      render: (record: IPTSMaster) => (
        <Space>
          {hasPermission(LAO_PTS_MASTERS_CREATE) && (
            <CopyOutlined
              className="hover:bg-slate-200 p-1 rounded-sm"
              onClick={() => {
                Modal.confirm({
                  width: 600,
                  title: t('pts.copyMasterPTS'),
                  content: t('pts.copyMasterPTSMessage'),
                  okText: t('common.apply'),
                  cancelText: t('common.cancel'),
                  okButtonProps: {
                    style: { backgroundColor: '#006885', color: '#fff' },
                  },
                  onOk: () => {
                    navigate(`/category/pts/copy/${record.id}`)
                    dispatch(setRecord(record))
                  },
                })
              }}
            />
          )}
          {hasPermission(LAO_PTS_MASTERS_EDIT) && (
            <EditFilled
              className="hover:bg-slate-200 p-1 rounded-sm"
              onClick={() => {
                navigate(`/category/pts/edit/${record.id}`)
              }}
            />
          )}
        </Space>
      ),
    },
  ]

  const onFilter = async () => {
    const values = await form.getFieldsValue()

    const fromDate = values.fromDate
      ? dayjs(values.fromDate).format(ISO_DATETIME)
      : null

    const toDate = values.toDate
      ? dayjs(values.toDate).format(ISO_DATETIME)
      : null

    dispatch(
      setParams({
        ...params,
        network: values.network,
        airportName: airportData.items.find(
          (item: IAirport) => item.id === values.airport
        )?.iataCode,
        fleetCode: fleetData.items.find(
          (item: IFleet) => item.id === values.aircraftType
        )?.code,
        fromDate,
        toDate,
        filterStatus: values.status,
        bhFrom: values.bhFrom,
        bhTo: values.bhTo,
        SkipCount: 0,
      })
    )

    dispatch(closeFilterModal())
  }

  useEffect(() => {
    dispatch(setParams({ ...params, SkipCount: 0, KeyWord: KeyWordDebounce }))
  }, [KeyWordDebounce])

  useEffect(() => {
    return () => {
      dispatch(resetState())
    }
  }, [])

  return (
    <Flex gap={16} vertical={true}>
      <Flex justify="space-between">
        <div className="text-lg font-bold text-black">{t('pts.ptsList')}</div>
        <Space>
          {hasPermission(LAO_PTS_MASTERS_CREATE) && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                navigate('/category/pts/create')
              }}
            >
              {t('pts.ptsCreate')}
            </Button>
          )}
          <Input
            prefix={<SearchOutlined />}
            placeholder={t('pts.searchPlaceholder')}
            onChange={val => {
              dispatch(setSearchDebounce(val.target.value))
            }}
          />
          <Popover
            style={{ width: 500 }}
            arrow={false}
            open={visibleFilterModal}
            placement="bottomRight"
            content={
              <Form layout="vertical" style={{ width: 500 }} form={form}>
                <Form.Item label={t('pts.aircraftType')} name="aircraftType">
                  <Select
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    placeholder={t('common.all')}
                    options={[
                      { value: null, label: `${t('common.all')}` },
                      ...(fleetData?.items.map((item: IFleet) => ({
                        value: item.id,
                        label: item.code,
                      })) || []),
                    ]}
                  />
                </Form.Item>
                <Form.Item label={t('pts.airport')} name="airport">
                  <Select
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    placeholder={t('common.all')}
                    options={[
                      { value: null, label: `${t('common.all')}` },
                      ...(airportData?.items.map((item: IAirport) => ({
                        value: item.id,
                        label: item.iataCode,
                      })) || []),
                    ]}
                  />
                </Form.Item>
                <Form.Item label={t('pts.network')} name="network">
                  <Select
                    allowClear
                    placeholder={t('common.all')}
                    options={[
                      { value: null, label: `${t('common.all')}` },
                      { value: 'INT', label: 'INT' },
                      { value: 'DOM', label: 'DOM' },
                    ]}
                  />
                </Form.Item>

                <div className="w-full flex flex-row gap-x-4">
                  <div className="w-full flex flex-row gap-x-4">
                    <Form.Item
                      label={t('pts.fromTime')}
                      name="bhFrom"
                      className="w-full"
                      rules={[{ type: 'number', min: 0 }]}
                    >
                      <InputNumber
                        placeholder={t('pts.fromTime')}
                        className="!w-full"
                        min={0}
                        controls={false}
                      />
                    </Form.Item>
                    <Form.Item
                      label={t('pts.toTime')}
                      name="bhTo"
                      className="w-full"
                      rules={[{ type: 'number', min: 0, max: 24 }]}
                    >
                      <InputNumber
                        placeholder={t('pts.toTime')}
                        className="!w-full"
                        min={0}
                        controls={false}
                      />
                    </Form.Item>
                  </div>
                </div>
                <div className="w-full flex flex-row gap-x-4">
                  <Form.Item
                    label={t('pts.fromDate')}
                    name="fromDate"
                    className="w-full"
                  >
                    <DatePicker
                      className="!w-full"
                      placeholder="dd/mm/yyyy"
                      format={DISPLAY_DATE}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t('pts.toDate')}
                    name="toDate"
                    className="w-full"
                  >
                    <DatePicker
                      className="!w-full"
                      placeholder="dd/mm/yyyy"
                      format={DISPLAY_DATE}
                    />
                  </Form.Item>
                </div>
                {/* <Form.Item label={t('pts.status')} name="status">
                  <Radio.Group
                    options={[
                      { value: 1, label: t('pts.using') },
                      { value: 0, label: t('pts.notUsing') },
                    ]}
                  />
                </Form.Item> */}
                <Flex justify="flex-end" gap={16}>
                  <Button
                    onClick={() => {
                      form.resetFields()
                      dispatch(closeFilterModal())
                      dispatch(resetState())
                    }}
                  >
                    {t('common.cancel')}
                  </Button>
                  <Button type="primary" onClick={onFilter}>
                    {t('common.apply')}
                  </Button>
                </Flex>
              </Form>
            }
          >
            <Button
              icon={<FilterFilled />}
              onClick={() => {
                if (visibleFilterModal) {
                  dispatch(closeFilterModal())
                } else {
                  dispatch(openFilterModal())
                }
              }}
            >
              {t('common.filter')}
            </Button>
          </Popover>
        </Space>
      </Flex>
      <Table
        size="small"
        columns={columns}
        bordered
        loading={isLoading}
        dataSource={data?.items}
        locale={{
          emptyText: (
            <>
              {KeyWordDebounce ||
              params.airportName ||
              params.fleetCode ||
              params.network ? (
                <div className="text-sm text-primary font-bold">
                  {t('pts.noResult')}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center w-full gap-y-1 py-4">
                  <img src={notask} alt="notask" className="w-32 h-32" />
                  <div className="text-base font-semibold text-black">
                    {t('pts.noMasterPTS')}
                  </div>
                  <div className="font-normal text-xs">
                    {t('pts.createFirstMasterPTS')}
                  </div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    className="w-max"
                    onClick={() => {
                      navigate('/category/pts/create')
                    }}
                  >
                    {t('pts.ptsCreate')}
                  </Button>
                </div>
              )}
            </>
          ),
        }}
        className={styles.whiteHeader}
        pagination={{
          total: data?.totalCount,
          current: params.SkipCount / params.MaxResultCount + 1,
          pageSize: params.MaxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                SkipCount: (page - 1) * pageSize,
                MaxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        rowKey={record => record.id}
      />
    </Flex>
  )
}

export default PTSPage
