import { EditFilled } from '@ant-design/icons'
import { Button, Table } from 'antd'
import styles from './index.module.scss'

const TabVipCIP = () => {
  const columns = [
    { title: 'Full Name', width: '30%' },
    { title: 'Booking Class', width: '30%' },
    { title: 'Note', width: '40%' },
  ]

  return (
    <div className="w-full py-3 bg-[#F5F9FA]">
      <div className="flex w-full justify-between mb-3">
        <div className="text-lg font-bold">VIP CIP</div>
        <Button
          className="!hidden"
          icon={<EditFilled />}
          type="primary"
          onClick={() => {}}
        >
          Chỉnh sửa
        </Button>
      </div>
      <Table bordered columns={columns} className={`${styles.whiteHeader}`} />
    </div>
  )
}

export default TabVipCIP
