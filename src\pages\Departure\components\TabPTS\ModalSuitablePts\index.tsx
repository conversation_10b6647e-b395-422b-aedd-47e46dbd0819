/* eslint-disable @typescript-eslint/no-explicit-any */
import { DISPLAY_DATE } from '@/src/constants'
import { flattenTree } from '@/src/helper/flattenTree'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import type { IPTSTask } from '@/src/schema/IPTSTask'
import {
  addPTSMasterAssignableToFlight,
  getFlight,
  getPTSFlight,
} from '@/src/service/flight'
import { getGroupPTSTask } from '@/src/service/group_pts_task'
import { getDepartments } from '@/src/service/organization_unit'
import { getPTSMaster, getPTSMasterDetail } from '@/src/service/pts_master'
import { getPTSTask } from '@/src/service/pts_task'
import {
  closeModalSuitablePTS,
  setActiveCollapseKey,
  setSelectedPtsId,
  setSelectedPTSMasterId,
  setSelectedSuitablePTSIds,
} from '@/src/store/DepartureSlice'
import { CloseOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Collapse, message, Modal, Spin } from 'antd'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'

const ModalSuitablePTS = () => {
  const {
    visibleModalSuitablePTS,
    selectedSuitablePTSIds,
    selectedPtsId,
    selectedPTSMasterId,
    activeCollapseKey,
  } = useAppSelector(state => state.departure)

  const { params: paramsFlight } = useAppSelector(state => state.departure)

  const { locale } = useAppSelector(state => state.global)

  const { selectedFlightId } = useAppSelector(state => state.departure)

  const { t } = useTranslation()

  const dispatch = useAppDispatch()

  const query = useQuery({
    queryKey: ['departure', paramsFlight],
    queryFn: () => getFlight(paramsFlight),
  })

  const { refetch: refreshPTSFlight } = useQuery<any>({
    queryKey: ['flight-pts-detail', selectedFlightId, locale.locale],
    queryFn: async () => {
      return selectedFlightId && getPTSFlight(selectedFlightId)
    },
    enabled: false,
  })

  const indexSelected = query.data?.items.find(
    (item: any) => item.id === selectedFlightId
  )?.ptsId

  const fleetCode = query.data?.items.find(
    (item: any) => item.id === selectedFlightId
  )?.fleetType

  const airportName = query.data?.items.find(
    (item: any) => item.id === selectedFlightId
  )?.depApActual

  const network = query.data?.items.find(
    (item: any) => item.id === selectedFlightId
  )?.network

  const flightHours = query.data?.items.find(
    (item: any) => item.id === selectedFlightId
  )?.flightHrs

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['pts-master-assignable-to-flight', selectedFlightId],
    queryFn: () =>
      selectedFlightId &&
      getPTSMaster({
        fleetCode,
        airportName,
        network,
        MaxResultCount: 1000,
        flightHours,
        SkipCount: 0,
      }),
    enabled: !!selectedFlightId && visibleModalSuitablePTS,
  })

  const { data: ptsDetailData, isLoading: isLoadingPTSDetail } = useQuery({
    queryKey: ['pts-detail', selectedPtsId],
    queryFn: () => selectedPtsId && getPTSMasterDetail(selectedPtsId),
    enabled: !!selectedPtsId,
  })

  const { data: groupPtsTaskData } = useQuery({
    queryKey: ['group-pts-task-list'],
    queryFn: () =>
      getGroupPTSTask({
        MaxResultCount: 1000,
      }),
    enabled: !!selectedPtsId,
  })

  const { data: ptsTaskData, isLoading: isLoadingPTSTask } = useQuery({
    queryKey: ['pts-tasks-list'],
    queryFn: () =>
      getPTSTask({
        MaxResultCount: 1000,
        Status: 1,
      }),
    enabled: !!selectedPtsId,
  })

  const { data: departureData } = useQuery({
    queryKey: ['departure-list'],
    queryFn: () => getDepartments(),
    enabled: !!selectedPtsId,
  })

  const flattenedDepartureData = departureData ? flattenTree(departureData) : []

  const mutationUpdate = useMutation({
    mutationFn: async (values: any) => {
      return (
        selectedFlightId &&
        addPTSMasterAssignableToFlight(selectedFlightId, values)
      )
    },
    onSuccess: () => {
      message.success(t('pts.updatePtsSuccess'))
      refetch()
      dispatch(setActiveCollapseKey(null))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const onSubmit = async () => {
    const finalSelectedId = selectedSuitablePTSIds || indexSelected

    if (!finalSelectedId) {
      message.error(t('pts.selectPTS1Message'))
      return
    }

    if (finalSelectedId !== selectedPTSMasterId) {
      await mutationUpdate.mutateAsync({ ptsId: finalSelectedId })
    }

    query.refetch()
    dispatch(setSelectedPTSMasterId(finalSelectedId))
    dispatch(setSelectedSuitablePTSIds(''))
    dispatch(closeModalSuitablePTS())
    refreshPTSFlight()
  }

  return (
    <Modal
      loading={isLoading}
      open={visibleModalSuitablePTS}
      width={1000}
      onCancel={() => {
        dispatch(closeModalSuitablePTS())
        dispatch(setActiveCollapseKey(null))
      }}
      okText={t('common.confirm')}
      cancelButtonProps={{ style: { display: 'none' } }}
      className="!h-[700px] overflow-y-auto relative w-[900px] scrollbar-hidden rounded-xl !p-0"
      footer={null}
      closable={false}
    >
      <div className="">
        <div className="bg-white z-10 sticky top-0 flex text-lg font-bold justify-between w-full items-center py-2">
          <div>{t('pts.suitablePTSList')}</div>

          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={() => {
              dispatch(closeModalSuitablePTS())
              dispatch(setActiveCollapseKey(null))
            }}
          />
        </div>
        {selectedFlightId && (
          <div className="-mt-1 mb-2 font-bold">
            {t('ptsDetail.aircraftType')}:{' '}
            <span className="text-primary">{fleetCode}</span> -{' '}
            {t('ptsDetail.airport')}:{' '}
            <span className="text-primary">{airportName}</span> -{' '}
            {t('ptsDetail.flightNetwork')}:{' '}
            <span className="text-primary">{network}</span> - {t('pts.time')}:{' '}
            <span className="text-primary">{flightHours}</span>
          </div>
        )}
        <div className="flex flex-col gap-y-2">
          {data ? (
            data.items.map((item: any) => {
              const current = item.id === indexSelected

              return (
                <Collapse
                  key={item.id}
                  expandIconPosition="end"
                  collapsible="icon"
                  accordion
                  activeKey={activeCollapseKey}
                  onChange={keys => {
                    const lastActiveKey = Array.isArray(keys)
                      ? keys.slice(-1)[0]
                      : keys
                    dispatch(setActiveCollapseKey(lastActiveKey || null))
                    dispatch(setSelectedPtsId(lastActiveKey))
                  }}
                  items={[
                    {
                      key: item.id,
                      label: (
                        <div
                          className={`text-base w-full flex font-semibold ${selectedSuitablePTSIds.includes(item.id) && 'text-white'}`}
                          onClick={() => {
                            const isSelected =
                              selectedSuitablePTSIds === item.id
                            const newSelected = isSelected ? '' : item.id
                            dispatch(setSelectedSuitablePTSIds(newSelected))
                          }}
                        >
                          <div className="w-1/3 line-clamp-1 mr-10">
                            {item.name}
                          </div>
                          <div className="w-1/3">
                            {t('pts.groundTime')}: {item.groundTime}{' '}
                            {t('pts.minute')}
                          </div>
                          <div className="w-1/3">
                            {t('pts.toDate')}:{' '}
                            {dayjs(item.toDate).format(DISPLAY_DATE)}
                          </div>
                        </div>
                      ),
                      children:
                        isLoadingPTSDetail ||
                        !ptsDetailData ||
                        isLoadingPTSTask ? (
                          <Spin />
                        ) : (
                          <div>
                            {ptsDetailData.listPtsDetailDtos
                              .filter((pts: any) => pts.status === 1)
                              .map((pts: any, index: number) => {
                                const task = ptsTaskData?.items?.find(
                                  (t: IPTSTask) => t.id === pts.ptsTaskId
                                )

                                const groupTask = groupPtsTaskData?.items?.find(
                                  (g: any) => g.id === pts.groupPtsTaskId
                                )

                                const department = flattenedDepartureData.find(
                                  (d: any) =>
                                    d.organizationUnitId === pts.organizationId
                                )

                                return (
                                  <div
                                    className="flex flex-row gap-x-6 border-b-1 py-4 border-[#E5E5E5]"
                                    key={pts.id}
                                  >
                                    <div className="font-bold">
                                      #{index + 1}
                                    </div>
                                    <div
                                      key={pts.id}
                                      className="flex flex-col font-semibold gap-y-1 text-base"
                                    >
                                      <div>
                                        {t('ptsDetail.jobName')}:{' '}
                                        <span className="font-normal">
                                          {task?.description}
                                        </span>
                                      </div>
                                      <div className="columns-2">
                                        <div>
                                          {t('ptsDetail.jobCode')}:{' '}
                                          <span className="font-normal">
                                            {task?.code}
                                          </span>
                                        </div>
                                        <div>
                                          {t('ptsDetail.completeTime')}:{' '}
                                          {pts.completeTime}
                                        </div>
                                      </div>
                                      <div>
                                        {t(
                                          'ptsDetail.managementResponsibility'
                                        )}
                                        :{' '}
                                        <span className="font-normal">
                                          {department?.name}
                                        </span>
                                      </div>
                                      <div>
                                        {t('ptsDetail.jobGroup')}:{' '}
                                        <span className="font-normal">
                                          {groupTask?.description}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                )
                              })}
                          </div>
                        ),
                      classNames: {
                        header: `!cursor-pointer !rounded-lg ${selectedSuitablePTSIds.includes(item.id) && '!bg-[#6699AA]'} ${current ? '!bg-red-300' : ''}`,
                      },
                    },
                  ]}
                />
              )
            })
          ) : (
            <div className="flex justify-center items-center text-base font-semibold">
              {t('pts.noPTS')}
            </div>
          )}
        </div>

        <div className="py-3 bg-white sticky bottom-0 flex justify-end z-10">
          <Button
            type="primary"
            onClick={() => {
              onSubmit()
            }}
          >
            {t('common.confirm')}
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default ModalSuitablePTS
