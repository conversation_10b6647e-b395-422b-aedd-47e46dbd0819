import {
  ABP_IDENTITY_ROLES,
  ABP_IDENTITY_USERS,
  LAO_AIRPORTS,
  LAO_DEPARTMENT,
  LAO_FLEETS,
  LAO_FLIGHTS,
  LAO_GROUP_PTS_TASKS,
  LAO_PTS_MASTERS,
  LAO_PTS_TASKS,
} from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'
import { ProjectOutlined } from '@ant-design/icons'
import {
  faHouse,
  faPlaneDeparture,
  faTableList,
  faUserTie,
} from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useTranslation } from 'react-i18next'

interface MenuItem {
  key: string
  label: string
  icon?: React.ReactNode
  permission?: string | string[]
  children?: MenuItem[]
}

export const useRoleMap = () => {
  const { t } = useTranslation()
  const { hasPermission } = usePermission()

  const menuItems: MenuItem[] = [
    {
      key: '/',
      label: t('nav.home'),
      icon: <FontAwesomeIcon icon={faHouse} />,
    },
    {
      key: '/flight-schedule',
      label: t('nav.flightSchedule'),
      icon: <FontAwesomeIcon icon={faPlaneDeparture} />,
      permission: LAO_FLIGHTS,
      children: [
        {
          key: '/departure',
          label: t('nav.departure'),
          permission: LAO_FLIGHTS,
        },
        { key: '/arrival', label: t('nav.arrival'), permission: LAO_FLIGHTS },
        {
          key: '/general',
          label: t('nav.general'),
          permission: LAO_FLIGHTS,
        },
        // {
        //   key: '/seasonal',
        //   label: 'Seasonal schedule',
        //   permission: LAO_FLIGHTS,
        // },
      ],
    },
    {
      key: '/category',
      label: t('nav.category'),
      icon: <FontAwesomeIcon icon={faTableList} />,
      permission: [
        LAO_PTS_MASTERS,
        LAO_PTS_TASKS,
        LAO_GROUP_PTS_TASKS,
        LAO_AIRPORTS,
        LAO_FLEETS,
      ],
      children: [
        {
          key: '/pts',
          label: t('nav.categoryPTS'),
          permission: LAO_PTS_MASTERS,
        },
        {
          key: '/job',
          label: t('nav.categoryJobs'),
          permission: LAO_PTS_TASKS,
        },
        {
          key: '/group-pts-task',
          label: t('nav.groupPtsTask'),
          permission: LAO_GROUP_PTS_TASKS,
        },
        {
          key: '/airport',
          label: t('nav.airport'),
          permission: LAO_AIRPORTS,
        },
        {
          key: '/fleet',
          label: t('nav.fleet'),
          permission: LAO_FLEETS,
        },
      ],
    },
    {
      key: '/report',
      label: t('nav.report'),
      icon: <ProjectOutlined />,
      children: [
        {
          key: '/report-on-time-gt',
          label: t('nav.flightOnTimeGT'),
        },
        {
          key: '/report-late-gt',
          label: t('nav.flightLateGT'),
        },
        {
          key: '/report-no-record-pts',
          label: t('nav.reportNoRecordPTS'),
        },
        {
          key: '/flight-late',
          label: t('nav.flightLate'),
        },
        {
          key: '/department-late',
          label: t('nav.departmentLate'),
        },
        {
          key: '/group-task-late',
          label: t('nav.groupTaskLate'),
        },
      ],
    },
    {
      key: '/administrator',
      label: t('nav.administrator'),
      icon: <FontAwesomeIcon icon={faUserTie} />,
      permission: [LAO_DEPARTMENT, ABP_IDENTITY_ROLES, ABP_IDENTITY_USERS],
      children: [
        {
          key: '/user',
          label: t('nav.userManagement'),
          permission: ABP_IDENTITY_USERS,
        },
        {
          key: '/organization-unit',
          label: t('nav.OrganizationUnits'),
          permission: LAO_DEPARTMENT,
        },
        {
          key: '/role',
          label: t('nav.roleManagement'),
          permission: ABP_IDENTITY_ROLES,
        },
      ],
    },
  ]

  // Helper function to check if user has any of the required permissions
  const hasAnyPermission = (
    permission: string | string[] | undefined
  ): boolean => {
    if (!permission) return true

    if (typeof permission === 'string') {
      return hasPermission(permission)
    }

    if (Array.isArray(permission)) {
      return permission.some(perm => hasPermission(perm))
    }

    return false
  }

  // Filter menu items based on permissions
  const filterMenuItems = (items: MenuItem[]): MenuItem[] => {
    return items
      .filter(item => hasAnyPermission(item.permission))
      .map(item => {
        if (item.children) {
          const filteredChildren = filterMenuItems(item.children)
          return {
            ...item,
            children:
              filteredChildren.length > 0 ? filteredChildren : undefined,
          }
        }
        return item
      })
      .filter(item => !item.children || item.children.length > 0)
  }

  return filterMenuItems(menuItems)
}
