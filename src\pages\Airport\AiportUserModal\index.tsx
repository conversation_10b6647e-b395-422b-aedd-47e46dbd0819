/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { TIME_DEBOUNCE } from '@/src/constants'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import { getUserOnAirport, removeUserFromAirport } from '@/src/service/airport'
import {
  closeAirportUserModal,
  closeDeleteUserModal,
  openDeleteUserModal,
  openUserModal,
  resetState,
  setKeyWordUserAirportDebounce,
  setParamsUserAirport,
  setSelectedUserAirportId,
  setShouldRefetchUserAirport,
} from '@/src/store/AirportSlice'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Input, message, Modal, Space, Table } from 'antd'
import type { TableProps } from 'antd/lib'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import { handleApiError } from '@/src/helper/handleApiError'
import ShowTotal from '@/src/components/Showtotal'
import {
  LAO_AIRPORTS_ASSIGN_USER,
  LAO_AIRPORTS_UNASSIGN_USER,
} from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'

const AirportUserModal = () => {
  const { t } = useTranslation()
  const { hasPermission } = usePermission()
  const {
    visibleAirportUserModal,
    selectedAirportId,
    selectedUserAirportId,
    visibleDeleteUserModal,
    shouldRefetchUserAirport,
    KeyWordUserAirportDebounce,
    paramsUserAirport,
  } = useAppSelector(state => state.airport)

  const dispatch = useAppDispatch()

  const KeyWordDebounce = useDebounce(KeyWordUserAirportDebounce, TIME_DEBOUNCE)

  const {
    data: userAirportData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['airport-user-list', [selectedAirportId, paramsUserAirport]],
    queryFn: () =>
      selectedAirportId &&
      getUserOnAirport(selectedAirportId, paramsUserAirport),
    enabled: !!selectedAirportId,
  })

  const onSubmit = async () => {
    dispatch(closeAirportUserModal())
  }

  const mutation = useMutation({
    mutationFn: async (values: any) => {
      return (
        selectedAirportId &&
        selectedUserAirportId &&
        removeUserFromAirport(selectedAirportId, values)
      )
    },
    onSuccess: () => {
      dispatch(closeDeleteUserModal())
      message.success(t('airport.remove_employee_success'))
      refetch()
      dispatch(setSelectedUserAirportId(''))
      dispatch(setKeyWordUserAirportDebounce(''))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const columns: TableProps<any>['columns'] = [
    {
      title: t('airport.STT'),
      key: 'stt',
      render: (_, __, index) => index + 1,
    },
    {
      title: t('airport.employee'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('airport.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('common.action'),
      dataIndex: '',
      width: 100,
      align: 'center',
      hidden: !hasPermission(LAO_AIRPORTS_UNASSIGN_USER),
      render: (record: any) => (
        <Space>
          <Button
            type="primary"
            onClick={() => {
              dispatch(setSelectedUserAirportId(record.id))
              dispatch(openDeleteUserModal())
            }}
          >
            {t('common.delete')}
          </Button>
        </Space>
      ),
    },
  ]

  useEffect(() => {
    dispatch(
      setParamsUserAirport({
        ...paramsUserAirport,
        SkipCount: 0,
        KeyWord: KeyWordDebounce,
      })
    )
  }, [KeyWordDebounce])

  useEffect(() => {
    if (shouldRefetchUserAirport) {
      refetch()
      dispatch(setShouldRefetchUserAirport(false))
    }
  }, [shouldRefetchUserAirport])

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <>
      <Modal
        width={1000}
        open={visibleAirportUserModal}
        title={t('airport.employee_list')}
        onOk={onSubmit}
        onCancel={() => {
          dispatch(closeAirportUserModal())
          dispatch(setKeyWordUserAirportDebounce(''))
        }}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
        okButtonProps={{ style: { display: 'none' } }}
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <Table
          loading={isLoading}
          size="small"
          title={() => (
            <div className="flex justify-end items-center gap-x-6">
              {hasPermission(LAO_AIRPORTS_ASSIGN_USER) && (
                <Button
                  icon={<PlusOutlined />}
                  type="primary"
                  onClick={() => {
                    dispatch(openUserModal())
                  }}
                >
                  {t('airport.add_employee')}
                </Button>
              )}
              <Input
                value={KeyWordUserAirportDebounce}
                className="!w-44"
                prefix={<SearchOutlined />}
                placeholder={t('airport.searchUserPlaceholder')}
                onChange={e => {
                  dispatch(setKeyWordUserAirportDebounce(e.target.value))
                }}
              />
            </div>
          )}
          columns={columns}
          bordered
          rowKey={record => record.id}
          className={`${styles.whiteHeader}`}
          dataSource={isLoading ? [] : userAirportData?.items || []}
          pagination={{
            showTotal: (total, range) => (
              <ShowTotal total={total} range={range} />
            ),
            total: isLoading ? 0 : userAirportData?.totalCount,
            current:
              paramsUserAirport.SkipCount / paramsUserAirport.MaxResultCount +
              1,
            pageSize: paramsUserAirport.MaxResultCount,
            onChange: (page, pageSize) => {
              dispatch(
                setParamsUserAirport({
                  SkipCount: (page - 1) * pageSize,
                  MaxResultCount: pageSize,
                })
              )
            },
          }}
        />
      </Modal>
      <Modal
        open={visibleDeleteUserModal}
        title={t('airport.remove_employee')}
        onOk={() => mutation.mutate([selectedUserAirportId])}
        onCancel={() => dispatch(closeDeleteUserModal())}
        closable={false}
      >
        <div>{t('airport.remove_employee_confirm')}</div>
      </Modal>
    </>
  )
}

export default AirportUserModal
