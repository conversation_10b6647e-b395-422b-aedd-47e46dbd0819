/* eslint-disable @typescript-eslint/no-explicit-any */
import { briefing, notask } from '@/src/assets/image'
import { ISO_FULLDATETIME } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  approveBriefing,
  getBriefingList,
  updateBriefing,
} from '@/src/service/briefing'
import { downloadFile } from '@/src/service/upload'
import {
  openModalBriefingResponses,
  openModalFlightBriefingInformation,
  setSelectedBriefingId,
} from '@/src/store/ModalArrivalFlightSlice'
import { LinkOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Card, Collapse, Divider, Modal, Space, Typography } from 'antd'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import ModalFlightBriefingInformation from '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalBriefing/ModalFlightBriefingInformation'
import ModalBriefingResponses from '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalBriefing/ModalBriefingResponses'

const TabModalBriefing = () => {
  const { t } = useTranslation()
  const dispatch = useAppDispatch()

  const { selectedFlightId } = useAppSelector(state => state.arrival)

  const { visibleModalArrivalFlightDetail } = useAppSelector(
    state => state.modalArrivalFlight
  )

  const { locale } = useAppSelector(state => state.global)

  const { data, refetch } = useQuery({
    queryKey: ['briefing-list', selectedFlightId],
    queryFn: () => getBriefingList(selectedFlightId as string),
    enabled: !!selectedFlightId && visibleModalArrivalFlightDetail,
  })

  const mutationUpdate = useMutation({
    mutationFn: async (values: any) => {
      return updateBriefing(values)
    },
    onSuccess: () => {
      refetch()
    },
    onError: handleApiError,
  })

  const mutationApprove = useMutation({
    mutationFn: async (values: any) => {
      return approveBriefing(values)
    },
    onSuccess: () => {
      refetch()
    },
    onError: handleApiError,
  })

  const mutationGetFile = useMutation({
    mutationFn: async (values: any) => {
      return downloadFile(values)
    },
    onError: error => {
      handleApiError(error)
    },
  })

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      {data?.items?.length === 0 ? (
        <div className="flex flex-col items-center justify-center min-h-[calc(65vh)] bg-[#F0F1F3FF]">
          <img src={notask} alt="" />
          <Button
            onClick={() => {
              dispatch(openModalFlightBriefingInformation())
            }}
            icon={<img src={briefing} alt="briefing" />}
            type="primary"
          >
            {t('common.createBreifing')}
          </Button>
        </div>
      ) : (
        <div>
          <div className="gap-y-6 flex flex-col h-[calc(85vh)] overflow-y-auto scrollbar">
            {data?.items?.map((item: any) => (
              <div className={`flex flex-col gap-y-1`} key={item.id}>
                <Card
                  key={item.id}
                  className={`flex flex-col ${styles.custom_card} ${item.isApprove ? '!bg-[#DDEFFF]' : ''}`}
                >
                  <div className="flex w-full justify-between items-center">
                    <div className="flex flex-col">
                      <div
                        className={`font-bold text-lg uppercase ${item.isClosed ? 'line-through text-slate-400' : ''}`}
                      >
                        {item.topic}
                      </div>
                      <span className="text-slate-400">
                        {t('common.messageBy')}&nbsp;
                        <span className="text-primary italic font-semibold">
                          {item.creatorName}
                        </span>
                        &nbsp;{t('common.at')}&nbsp;
                        {dayjs(item.creationTime)
                          .locale(locale.locale)
                          .format(ISO_FULLDATETIME)}
                      </span>
                      {item.isApprove && (
                        <span className="text-slate-400">
                          {t('common.approvedBy')}&nbsp;
                          <span className="text-primary italic font-semibold">
                            {item?.approveName}
                          </span>
                          &nbsp;{t('common.at')}&nbsp;
                          {dayjs(item.approvedAt)
                            .locale(locale.locale)
                            .format(ISO_FULLDATETIME)}
                        </span>
                      )}
                    </div>
                    <Space>
                      <Button
                        type="primary"
                        className={`mr-4`}
                        onClick={() => {
                          Modal.confirm({
                            title: `${item.isApprove ? 'Revoke' : 'Approve'} briefing?`,
                            content: `Are you sure you want to ${item.isApprove ? 'revoke' : 'approve'} this briefing?`,
                            okText: t('common.confirm'),
                            cancelText: t('common.cancel'),
                            onOk: async () => {
                              mutationApprove.mutate({
                                briefingId: item.id,
                                isApprove: !item.isApprove,
                                flightId: selectedFlightId as string,
                              })
                            },
                          })
                        }}
                      >
                        {item.isApprove
                          ? t('button.revoke')
                          : t('button.approve')}
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => {
                          Modal.confirm({
                            title: item.isClosed
                              ? 'Open briefing?'
                              : 'Close briefing?',
                            content: `Do you want to ${item.isClosed ? 'open' : 'close'} this briefing?`,
                            okText: 'Yes',
                            cancelText: 'No',
                            onOk: async () => {
                              mutationUpdate.mutate({
                                ...item,
                                briefingId: item.id,
                                isClosed: !item.isClosed,
                                flightId: selectedFlightId as string,
                              })
                            },
                          })
                        }}
                      >
                        {item.isClosed ? 'Open' : t('common.close')}
                      </Button>
                      <Button
                        className={`${item.isClosed || item.isApprove ? '!hidden' : ''}`}
                        type="primary"
                        onClick={() => {
                          dispatch(openModalFlightBriefingInformation())
                          dispatch(setSelectedBriefingId(item.id))
                        }}
                      >
                        {t('common.update')}
                      </Button>
                      <Button
                        className={`${item.isClosed ? '!hidden' : ''}`}
                        type="primary"
                        onClick={() => {
                          dispatch(openModalBriefingResponses())
                          dispatch(setSelectedBriefingId(item.id))
                        }}
                      >
                        {t('common.reply')}
                      </Button>
                      <Button type="primary" className="!hidden">
                        {t('common.print')}
                      </Button>
                    </Space>
                  </div>
                  <Divider className="bg-primary" />
                  <Typography.Paragraph
                    style={{ whiteSpace: 'pre-line' }}
                    ellipsis={{
                      rows: 5,
                      expandable: 'collapsible',
                      symbol: (expanded: boolean) => (
                        <>
                          {expanded ? (
                            <span className="!text-primary italic">Hide</span>
                          ) : (
                            <span className="!text-primary italic">More</span>
                          )}
                        </>
                      ),
                    }}
                  >
                    {item?.content}
                  </Typography.Paragraph>

                  {item.fileName && (
                    <div className="!text-primary cursor-pointer mt-2 !w-max">
                      <div
                        onClick={async () => {
                          const file = await mutationGetFile.mutateAsync({
                            filePath: item?.filePath,
                            fileName: item?.fileName,
                          })
                          return FileSaver.saveAs(file, item?.fileName)
                        }}
                      >
                        <LinkOutlined /> {item?.fileName}
                      </div>
                    </div>
                  )}
                </Card>
                {item.replies.length > 0 && (
                  <div className="pl-20">
                    <Collapse
                      key={item.id}
                      className={`${item.isApprove ? styles.custom_collapse_approve : styles.custom_collapse} ${item.isApprove ? '!bg-[#DDEFFF]' : 'bg-white'}`}
                      items={[
                        {
                          key: item.id,
                          label: (
                            <div className="font-bold">
                              <div className="">
                                <span className="text-red-400">
                                  ({item.replies.length})
                                </span>
                                &nbsp;
                                <span className="">
                                  {item.replies.length > 1
                                    ? 'Replies'
                                    : 'Reply'}
                                </span>
                              </div>
                            </div>
                          ),
                          children: (
                            <div
                              className={`${item.isApprove ? '!bg-[#DDEFFF]' : ''}`}
                            >
                              {item.replies.map((child: any, index: number) => (
                                <div className="p-4">
                                  <div className="flex flex-col w-full justify-between ">
                                    <div className="text-slate-400 italic">
                                      {t('common.messageBy')}&nbsp;
                                      <span className="text-primary  font-semibold">
                                        {child.name}
                                      </span>
                                      &nbsp;{t('common.at')}&nbsp;
                                      {dayjs(child.creationTime)
                                        .locale(locale.locale)
                                        .format(ISO_FULLDATETIME)}
                                    </div>
                                    <Typography.Paragraph
                                      style={{ whiteSpace: 'pre-line' }}
                                      ellipsis={{
                                        rows: 5,
                                        expandable: 'collapsible',
                                        symbol: (expanded: boolean) => (
                                          <>
                                            {expanded ? (
                                              <span className="!text-primary italic">
                                                Hide
                                              </span>
                                            ) : (
                                              <span className="!text-primary italic">
                                                More
                                              </span>
                                            )}
                                          </>
                                        ),
                                      }}
                                    >
                                      {child?.message}
                                    </Typography.Paragraph>
                                  </div>
                                  {index !== item?.replies.length - 1 && (
                                    <Divider />
                                  )}
                                </div>
                              ))}
                            </div>
                          ),
                        },
                      ]}
                      defaultActiveKey={['1']}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
          <div className="justify-end flex mt-2">
            <Button
              onClick={() => {
                dispatch(openModalFlightBriefingInformation())
              }}
              icon={<img src={briefing} alt="briefing" />}
              type="primary"
            >
              {t('common.createBreifing')}
            </Button>
          </div>
        </div>
      )}
      <ModalFlightBriefingInformation />
      <ModalBriefingResponses />
    </div>
  )
}

export default TabModalBriefing
