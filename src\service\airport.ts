/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'
import { handleApiError } from '@/src/helper/handleApiError'

const prefix = `/api/v1/airports`

const getAirport = async (params: any) => {
  const api = `${prefix}`
  const response = await axios.get(api, { params })
  return response.data
}

const createAirport = async (body: any) => {
  const api = `${prefix}`

  const response = await axios.post(api, body)
  return response.data
}

const updateAirport = async (body: any) => {
  const api = `${prefix}/${body.id}`

  const response = await axios.put(api, body)
  return response.data
}

const removeAirport = async (id: string) => {
  const api = `${prefix}/${id}`

  const response = await axios.delete(api)
  return response.data
}

const getUserOnAirport = async (airportId: string, params: any) => {
  const api = `${prefix}/${airportId}/users`
  try {
    const response = await axios.get(api, { params })
    return response.data
  } catch (error) {
    handleApiError(error)
  }
}

const addUserToAirport = async (airportId: string, body: any) => {
  const api = `${prefix}/${airportId}/users`
  try {
    const response = await axios.post(api, body)
    return response.data
  } catch (error: any) {
    throw new Error(error)
  }
}

const removeUserFromAirport = async (airportId: string, body: any) => {
  const api = `${prefix}/${airportId}/users`
  try {
    const response = await axios.delete(api, { data: body })
    return response.data
  } catch (error: any) {
    throw new Error(error)
  }
}

const getAirportByUser = async () => {
  const api = `${prefix}/get-by-user`
  const response = await axios.get(api)
  return response.data
}

export {
  createAirport,
  getAirport,
  removeAirport,
  updateAirport,
  getUserOnAirport,
  addUserToAirport,
  removeUserFromAirport,
  getAirportByUser,
}
