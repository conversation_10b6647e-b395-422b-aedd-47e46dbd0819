import { colors } from '@/src/constants/colors'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  closeModalFlightDetail,
  setTabName,
} from '@/src/store/ModalDepartureFlightSlice'
import { ConfigProvider, Modal, Tabs } from 'antd'
import styles from './index.module.scss'
import { LAO_FLIGHTS_VIEW_PTS } from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'
import { lazy, Suspense } from 'react'
// const TabModalACTechInfo = lazy(
//   () =>
//     import(
//       '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalACTechInfo'
//     )
// )
const TabModalBriefing = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalBriefing'
    )
)

const TabModalHistory = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalHistory'
    )
)
const TabModalIrregularService = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalIrregularService'
    )
)

const TabModalReasons = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalReasons'
    )
)
const TabModalUnusualInfo = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalUnusualInfo'
    )
)
const TabModalFlightInfo = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalFlightInfo'
    )
)

const TabModalMealOrder = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalMealOrder'
    )
)

const TabModalPassenger = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalPassenger'
    )
)

const TabModalGD = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalGD'
    )
)

const TabModalACTechInfo = lazy(
  () =>
    import(
      '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalACTechInfo'
    )
)

const TabPTS = lazy(() => import('@/src/pages/Departure/components/TabPTS'))

const ModalDepartureFlightDetail = () => {
  const { hasPermission } = usePermission()
  const dispatch = useAppDispatch()

  const {
    tabName,
    visibleModalDepartureFlightDetail: visibleModalFlightDetail,
  } = useAppSelector(state => state.modalDepartureFlight)

  const items = [
    {
      key: 'flight_info',
      label: 'Flight Info',
      children: (
        <Suspense>
          <TabModalFlightInfo />
        </Suspense>
      ),
    },
    {
      key: 'ac_tech_info',
      label: 'AC Tech Info',
      children: (
        <Suspense>
          <TabModalACTechInfo />
        </Suspense>
      ),
    },
    // {
    //   key: 'passenger',
    //   label: 'Passenger',
    //   children: <TabModalPassenger />,
    // },
    {
      key: 'meal_order',
      label: 'Meal Order',
      children: (
        <Suspense>
          <TabModalMealOrder />
        </Suspense>
      ),
    },
    // {
    //   key: 'gd',
    //   label: 'GD',
    //   children: <TabModalGD />,
    // },
    {
      key: 'pax_info',
      label: 'Pax Infor',
      children: (
        <Suspense>
          <TabModalPassenger />
        </Suspense>
      ),
    },
    // {
    //   key: 'meal_order',
    //   label: 'Meal Order',
    //   children: <TabModalMealOrder />,
    // },
    {
      key: 'gd',
      label: 'GD',
      children: (
        <Suspense>
          <TabModalGD />
        </Suspense>
      ),
    },
    // {
    //   key: 'vip_cip',
    //   label: 'VIP CIP',
    //   children: <TabModalVipCIP />,
    // },
    {
      key: 'reasons',
      label: 'Reasons',
      children: (
        <Suspense>
          <TabModalReasons />
        </Suspense>
      ),
    },
    // {
    //   key: 'ground_service',
    //   label: 'Ground Service',
    //   children: <TabGroundService />,
    // },

    {
      key: 'unusual_info',
      label: 'Unusual Info',
      children: (
        <Suspense>
          <TabModalUnusualInfo />
        </Suspense>
      ),
    },
    {
      key: 'briefing',
      label: 'Briefing',
      children: (
        <Suspense>
          <TabModalBriefing />
        </Suspense>
      ),
    },
    // {
    //   key: 'unusual_pax',
    //   label: 'Unusual Pax',
    //   children: <TabModalUnusualPax />,
    // },
    // {
    //   key: 'unusual_luggage',
    //   label: 'Unusual Luggage',
    //   children: <TabModalUnusualLuggage />,
    // },
    // {
    //   key: 'sla_evaluation',
    //   label: 'SLA Evaluation',
    //   children: <TabModalSLAEvaluation />,
    // },
    {
      key: 'irregular_service',
      label: 'Irregular Service',
      children: (
        <Suspense>
          <TabModalIrregularService />
        </Suspense>
      ),
    },
    {
      key: 'pts',
      label: 'PTS',
      children: (
        <Suspense>
          <TabPTS />
        </Suspense>
      ),
      hidden: !hasPermission(LAO_FLIGHTS_VIEW_PTS),
    },
    {
      key: 'history',
      label: 'History',
      children: (
        <Suspense>
          <TabModalHistory />
        </Suspense>
      ),
    },
  ]

  return (
    <Modal
      open={visibleModalFlightDetail}
      title={`${tabName}`}
      width={1200}
      centered
      okButtonProps={{ style: { display: 'none' } }}
      cancelButtonProps={{ style: { display: 'none' } }}
      onCancel={() => dispatch(closeModalFlightDetail())}
      className="h-screen"
    >
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              itemActiveBg: colors.primary,
              itemSelectedBg: colors.primary,
              itemSelectedColor: '#fff',
              itemBorderRadius: 4,
              itemDisabledColor: colors.white,
              subMenuItemSelectedColor: colors.white,
              itemMarginBlock: 8,
            },
          },
        }}
      >
        <ConfigProvider>
          <Tabs
            tabPosition="left"
            items={items}
            defaultActiveKey="sub1"
            className={`${styles.custom_tabs}`}
            onTabClick={key => {
              const tab = items.find(item => item.key === key)
              dispatch(setTabName(tab?.label))
            }}
          />
        </ConfigProvider>
      </ConfigProvider>
    </Modal>
  )
}

export default ModalDepartureFlightDetail
