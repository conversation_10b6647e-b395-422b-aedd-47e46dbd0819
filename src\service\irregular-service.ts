/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/flights`

const getIrregularServiceDetail = async (flightId: string) => {
  const api = `${prefix}/${flightId}/irregular-service`
  const response = await axios.get(api)
  return response.data
}

const postIrregularService = async (flightId: string, body: any) => {
  const api = `${prefix}/${flightId}/irregular-service`
  const response = await axios.post(api, body)
  return response.data
}

export { postIrregularService, getIrregularServiceDetail }
