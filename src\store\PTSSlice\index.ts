/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

type PTSState = {
  selectedJobId: string | null
  params: {
    SkipCount: number
    MaxResultCount: number
    KeyWord?: string
    filterStatus?: any
    network?: string
    airportName?: string
    fleetCode?: string
    fromDate?: string
    toDate?: string
    bhFrom?: string
    bhTo?: string
  }
  sort: string
  visibleDeleteModal: boolean
  visibleFilterModal: boolean
  KeyWord?: string
  record: any
  groupPtsTask: any[]
  selectedFlight: any
}

const initialState: PTSState = {
  selectedJobId: null,
  params: {
    SkipCount: 0,
    MaxResultCount: 20,
  },
  visibleDeleteModal: false,
  visibleFilterModal: false,
  record: null,
  groupPtsTask: [],
  selectedFlight: null,
  sort: '',
}

const jobSlice = createSlice({
  name: 'pts',
  initialState,
  reducers: {
    selectPTS(state, action: PayloadAction<string>) {
      state.selectedJobId = action.payload
    },
    openDeleteModal(state) {
      state.visibleDeleteModal = true
    },
    closeDeleteModal(state) {
      state.visibleDeleteModal = false
    },
    openFilterModal(state) {
      state.visibleFilterModal = true
    },
    closeFilterModal(state) {
      state.visibleFilterModal = false
    },
    setParams(state, action: PayloadAction<any>) {
      state.params = { ...state.params, ...action.payload }
    },
    setSearchDebounce(state, action: PayloadAction<string>) {
      state.KeyWord = action.payload
    },
    setRecord(state, action: PayloadAction<any>) {
      state.record = action.payload
    },
    setGroupPtsTask(state, action: PayloadAction<any>) {
      state.groupPtsTask = action.payload
    },
    resetState() {
      return initialState
    },
    setSelectedFlight(state, action: PayloadAction<any>) {
      state.selectedFlight = action.payload
    },
    setSort(state, action: PayloadAction<string>) {
      state.sort = action.payload
    },
  },
})

export const {
  selectPTS,
  setParams,
  openDeleteModal,
  closeDeleteModal,
  openFilterModal,
  closeFilterModal,
  setSearchDebounce,
  setGroupPtsTask,
  setRecord,
  resetState,
  setSelectedFlight,
  setSort,
} = jobSlice.actions
export default jobSlice.reducer
