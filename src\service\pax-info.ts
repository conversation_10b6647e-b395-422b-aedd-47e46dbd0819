/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/flights`

const getPaxInfo = async (flightId: string) => {
  const api = `${prefix}/${flightId}/pax-info`
  const response = await axios.get(api)
  return response.data
}

const updatePaxInfo = async (flightId: string, body: any) => {
  const api = `${prefix}/${flightId}/pax-info/${body.id}`
  const response = await axios.put(api, body)
  return response.data
}

const getPassenger = async (flightId: string, params?: any) => {
  const api = `${prefix}/${flightId}/passengers`
  const response = await axios.get(api, { params })
  return response.data
}

export { getPaxInfo, updatePaxInfo, getPassenger }
