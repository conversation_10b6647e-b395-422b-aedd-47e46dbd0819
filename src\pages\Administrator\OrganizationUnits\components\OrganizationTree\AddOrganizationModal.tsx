import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  setModalMode,
  setParentSelectedOrganizationUnit,
  setSelectedOrganizationUnit,
  toggleOrganizationUnitModal,
} from '@/src/store/OrganizationUnitSlice'
import { Form, Input, Modal } from 'antd'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import {
  createDepartment,
  getDepartmentDetail,
  updateDepartment,
} from '@/src/service/organization_unit'
import { useTranslation } from 'react-i18next'
import { handleApiError } from '@/src/helper/handleApiError'
const AddOrganizationModal = () => {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const dispatch = useAppDispatch()
  const [form] = Form.useForm()

  const {
    visibleOrganizationUnitModal,
    modalMode,
    selectedId,
    parentSelectedId,
  } = useAppSelector(state => state.organizationUnit)

  const getModalTitle = (mode: string | null) => {
    switch (mode) {
      case 'edit':
        return t('organization.organizationTree.edit')
      case 'create-sub':
        return t('organization.organizationTree.addSub')
      case 'create':
        dispatch(setParentSelectedOrganizationUnit(null))
        return t('organization.organizationTree.add')
      default:
        return t('organization.organizationTree.add')
    }
  }

  const { data: departmentDetail, isFetching: isLoading } = useQuery({
    queryKey: ['departmentDetail', selectedId],
    queryFn: () => getDepartmentDetail(selectedId!),
    enabled: !!selectedId && visibleOrganizationUnitModal,
  })

  useEffect(() => {
    if (departmentDetail && modalMode == 'edit') {
      form.setFieldsValue(departmentDetail)
    } else {
      form.resetFields()
    }
  }, [departmentDetail, form, modalMode, visibleOrganizationUnitModal])

  const { mutate: create, isPending: isCreating } = useMutation({
    mutationFn: createDepartment,
    onSuccess: () => {
      dispatch(toggleOrganizationUnitModal())
      form.resetFields()
      queryClient.invalidateQueries({ queryKey: ['departmentsTree'] })
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const { mutate: update, isPending: isUpdating } = useMutation({
    mutationFn: updateDepartment,
    onSuccess: () => {
      dispatch(toggleOrganizationUnitModal())
      form.resetFields()
      queryClient.invalidateQueries({ queryKey: ['departmentsTree'] })
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const toggleModal = () => {
    dispatch(toggleOrganizationUnitModal())
    form.resetFields()
    dispatch(setParentSelectedOrganizationUnit(null))
    dispatch(setSelectedOrganizationUnit(null))
    dispatch(setModalMode(null))
  }

  const handleOk = async () => {
    const values = await form.validateFields()
    if (modalMode === 'edit' && selectedId) {
      update({
        id: selectedId,
        name: values.name,
        code: values.code,
      })
    } else {
      create({
        name: values.name,
        code: values.code,
        parentId: parentSelectedId,
      })
    }
    dispatch(setParentSelectedOrganizationUnit(null))
    dispatch(setSelectedOrganizationUnit(null))
    dispatch(setModalMode(null))
  }

  return (
    <Modal
      open={visibleOrganizationUnitModal}
      title={getModalTitle(modalMode)}
      okText={t('common.save')}
      cancelText={t('common.cancel')}
      onCancel={toggleModal}
      onOk={handleOk}
      okButtonProps={{ autoFocus: true }}
      confirmLoading={isCreating || isUpdating}
      destroyOnHidden={true}
      loading={isLoading}
      modalRender={dom => (
        <Form layout="vertical" form={form} name="form_in_modal">
          {dom}
        </Form>
      )}
    >
      <Form.Item
        name="name"
        label={t('organization.name')}
        rules={[{ required: true, message: t('organization.nameRequired') }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="code"
        label={t('organization.code')}
        rules={[{ required: true, message: t('organization.codeRequired') }]}
        hidden={modalMode === 'edit'}
      >
        <Input />
      </Form.Item>
    </Modal>
  )
}

export default AddOrganizationModal
