/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  DISPLAY_DATE,
  ISO_DATETIME,
  ISO_DATETIME_NOSECOND,
} from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import type { IAirport } from '@/src/schema/IAirport'
import type { IFleet } from '@/src/schema/IFleet'
import type { IGroupPTSTask } from '@/src/schema/IGroupPTSTask'
import type { IPtsDetailDto, IPTSMaster } from '@/src/schema/IPTSMaster'
import type { IPTSTask } from '@/src/schema/IPTSTask'
import { getAirport } from '@/src/service/airport'
import { getFleet } from '@/src/service/fleet'
import { getGroupPTSTask } from '@/src/service/group_pts_task'
import { getNetwork } from '@/src/service/network'
import { getDepartments } from '@/src/service/organization_unit'
import { createPTSMaster, getPTSMasterDetail } from '@/src/service/pts_master'
import { getPTSTask } from '@/src/service/pts_task'
import { setSort } from '@/src/store/PTSSlice'
import {
  CalendarOutlined,
  CaretDownOutlined,
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  MoreOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  DatePicker,
  Dropdown,
  Form,
  Input,
  InputNumber,
  message,
  Select,
  Space,
  Spin,
  Switch,
} from 'antd'
import dayjs from 'dayjs'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate, useParams } from 'react-router'

const flattenTree = (tree: any[]): any[] => {
  return tree.reduce((acc: any[], node: any) => {
    acc.push(node)
    if (node.children && node.children.length > 0) {
      acc.push(...flattenTree(node.children))
    }
    return acc
  }, [])
}

const selectFilterOption = (input: string, option: any) => {
  const label =
    typeof option?.label === 'string'
      ? option.label.toLowerCase()
      : String(option?.label).toLowerCase()
  return label.includes(input.toLowerCase())
}

const PTSCopyPage = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const param = useParams()
  const id = param.id
  const { t } = useTranslation()

  const { sort } = useAppSelector(state => state.pts)

  const { locale, profile } = useAppSelector(state => state.global)

  const dispatch = useAppDispatch()

  const { data: fleetData, isLoading: fleetLoading } = useQuery({
    queryKey: ['fleet-list'],
    queryFn: () =>
      getFleet({
        MaxResultCount: 1000,
      }),
  })

  const { data: airportData, isLoading: aiportLoading } = useQuery({
    queryKey: ['airport-list'],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
  })

  const { data: groupPtsTaskData } = useQuery({
    queryKey: ['group-pts-task-list', locale],
    queryFn: () =>
      getGroupPTSTask(
        {
          MaxResultCount: 1000,
        },
        locale.locale
      ),
  })

  const { data: ptsTaskData, isLoading: ptsTaskLoading } = useQuery({
    queryKey: ['pts-tasks-list', locale],
    queryFn: () =>
      getPTSTask(
        {
          MaxResultCount: 1000,
          Status: 1,
        },
        locale.locale
      ),
  })

  const { data: _ } = useQuery({
    queryKey: ['pts-master-detail', id],
    queryFn: async () => {
      if (id) {
        const res = await getPTSMasterDetail(id)
        form.setFieldsValue({
          ...res,
          code: '',
          fromDate: dayjs(res.fromDate),
          toDate: dayjs(res.toDate),
          ptsDetailDtos: res.listPtsDetailDtos
            ? res.listPtsDetailDtos.map((item: IPtsDetailDto) => ({
                ...item,
                status: item.status ?? false,
                completeTime: item.completeTime,
                organizationId: item.organizationId,
                groupPtsTaskId: item.groupPtsTaskId,
                groupPtsTaskName: item.groupPtsTaskName,
              }))
            : [],
        })
        return res
      }
    },
  })

  const { data: networkData } = useQuery({
    queryKey: ['network-list'],
    queryFn: () => getNetwork(),
  })

  const { data: departureData } = useQuery({
    queryKey: ['departure-list'],
    queryFn: () => getDepartments(),
  })

  const flattenedDepartureData = departureData ? flattenTree(departureData) : []

  const mutation = useMutation({
    mutationFn: async (values: IPTSMaster) => {
      return createPTSMaster(values)
    },
    onSuccess: () => {
      navigate('/category/pts')
      message.success(t('pts.copySuccess'))
    },
    onError: handleApiError,
  })

  const onSubmit = async () => {
    await form.validateFields()
    if (form.getFieldValue('ptsDetailDtos').length === 0) {
      message.error(t('ptsDetail.createPTSMessage'))
      return
    }

    try {
      const values = form.getFieldsValue()

      const data = {
        ...values,
        fromDate: dayjs(values.fromDate).format(ISO_DATETIME),
        toDate: dayjs(values.toDate).format(ISO_DATETIME),
        fleetCode: fleetData.items.find(
          (item: IFleet) => item.id === values.fleetId
        )?.code,
        airportName: airportData.items.find(
          (item: IAirport) => item.id === values.airportId
        )?.iataCode,
        network: values.network,
        ptsDetailDtos: values.ptsDetailDtos.map((item: IPtsDetailDto) => ({
          status: item.status ? 1 : 0,
          ptsTaskId: item.ptsTaskId,
          completeTime: item.completeTime,
          organizationId: item.organizationId,
          groupPtsTaskId: item.groupPtsTaskId,
          executeOUIds: item.executeOUIds,
        })),
      }

      mutation.mutate(data)
    } catch (error) {
      // console.error('Validation failed:', error)
    }
  }

  useEffect(() => {
    dispatch(setSort('none'))
  }, [])

  return (
    <Spin spinning={ptsTaskLoading || fleetLoading || aiportLoading}>
      <div className="flex flex-col gap-y-6">
        <div className="flex flex-row justify-between w-full items-center">
          <div className="text-lg font-bold text-black">
            {t('common.copy')} {t('ptsDetail.standardPTS')}
          </div>
          <div className="flex flex-row gap-x-4 items-center">
            <div>
              {t('ptsDetail.createdBy')}: {profile?.userName}&nbsp;
              {dayjs().format(ISO_DATETIME_NOSECOND)}
            </div>
            <Button
              icon={<CloseOutlined />}
              className="!border-primary !text-primary"
              onClick={() => {
                navigate(-1)
                form.resetFields()
              }}
            >
              {t('common.cancel')}
            </Button>
            <Button
              icon={<CheckOutlined />}
              type="primary"
              onClick={() => {
                onSubmit()
              }}
            >
              {t('common.save')}
            </Button>
          </div>
        </div>
        <Form layout="vertical" form={form}>
          <div className="rounded-lg flex flex-col border border-[#E6F0F3]">
            <div className="w-full items-center justify-between flex px-5 py-3 bg-[#E6F0F3] rounded-t-lg">
              <div className="font-medium text-base">
                {t('ptsDetail.detailInfo')}
              </div>
            </div>
            <div className="px-5 py-3 w-full">
              <div className="w-full flex justify-between gap-x-5">
                <Form.Item
                  label={t('ptsDetail.code')}
                  name="code"
                  className="w-1/4"
                  rules={[
                    { required: true, message: t('ptsDetail.codeMessage') },
                    { max: 50, message: t('ptsDetail.codeLengthMessage') },
                  ]}
                >
                  <Input placeholder={t('ptsDetail.codePlaceholder')} />
                </Form.Item>
                <Form.Item
                  label={t('ptsDetail.name')}
                  name="name"
                  className="w-1/4"
                  rules={[
                    { max: 200, message: t('ptsDetail.nameLengthMessage') },
                    { required: true, message: t('ptsDetail.nameMessage') },
                  ]}
                >
                  <Input placeholder={t('ptsDetail.namePlaceholder')} />
                </Form.Item>
                {/* <Form.Item
                label={t('ptsDetail.nameEng')}
                name="nameEng"
                className="w-1/4"
              >
                <Input placeholder={t('ptsDetail.nameEngPlaceholder')} />
              </Form.Item> */}
                <Form.Item
                  name="fleetId"
                  label={t('ptsDetail.aircraftType')}
                  className="w-1/4"
                  rules={[
                    {
                      required: true,
                      message: t('ptsDetail.aircraftTypeMessage'),
                    },
                  ]}
                >
                  <Select
                    allowClear
                    showSearch
                    filterOption={selectFilterOption}
                    loading={fleetLoading}
                    options={fleetData?.items.map((item: IFleet) => ({
                      value: item.id,
                      label: item.code,
                    }))}
                    placeholder={t('ptsDetail.aircraftTypePlaceholder')}
                  />
                </Form.Item>
                <Form.Item
                  name="airportId"
                  label={t('ptsDetail.airport')}
                  className="w-1/4"
                  rules={[
                    {
                      required: true,
                      message: t('ptsDetail.airportMessage'),
                    },
                  ]}
                >
                  <Select
                    allowClear
                    showSearch
                    filterOption={selectFilterOption}
                    loading={aiportLoading}
                    options={airportData?.items.map((item: IAirport) => ({
                      value: item.id,
                      label: item.iataCode,
                    }))}
                    placeholder={t('ptsDetail.airportPlaceholder')}
                  />
                </Form.Item>
              </div>
              <div className="w-full flex justify-between gap-x-5">
                <Form.Item
                  name="network"
                  label={t('ptsDetail.flightNetwork')}
                  className="w-1/4"
                  rules={[
                    {
                      required: true,
                      message: t('ptsDetail.flightNetworkMessage'),
                    },
                  ]}
                >
                  <Select
                    allowClear
                    showSearch
                    options={networkData?.map((item: any) => ({
                      value: item.name,
                      label: item.name,
                    }))}
                  />
                </Form.Item>

                <Form.Item
                  name="groundTime"
                  label={
                    <div className="line-clamp-1">
                      {t('ptsDetail.groundTime')}
                    </div>
                  }
                  className="w-1/4"
                  rules={[
                    {
                      required: true,
                      message: t('ptsDetail.groundTimeMessage'),
                    },
                  ]}
                >
                  <InputNumber
                    min={0}
                    className="!w-full"
                    placeholder={t('ptsDetail.groundTimePlaceholder')}
                  />
                </Form.Item>
                <Form.Item
                  name="bhFrom"
                  label={
                    <div className="line-clamp-1">
                      {t('ptsDetail.effectiveHourFrom')}
                    </div>
                  }
                  className="w-1/4"
                >
                  <InputNumber
                    min={0}
                    className="!w-full"
                    placeholder={t('ptsDetail.effectiveHourFromPlaceholder')}
                  />
                </Form.Item>
                <Form.Item
                  name="bhTo"
                  label={
                    <div className="line-clamp-1">
                      {t('ptsDetail.effectiveHourTo')}
                    </div>
                  }
                  className="w-1/4"
                >
                  <InputNumber
                    min={0}
                    className="!w-full"
                    placeholder={t('ptsDetail.effectiveHourToPlaceholder')}
                  />
                </Form.Item>
              </div>
              <div className="flex gap-x-5 w-full pr-14">
                <Form.Item
                  name="fromDate"
                  label={t('ptsDetail.effectiveDateFrom')}
                  className="w-1/4"
                  rules={[
                    {
                      required: true,
                      message: t('ptsDetail.effectiveDateFromMessage'),
                    },
                    // ({ getFieldValue }) => ({
                    //   validator(_, value) {
                    //     if (
                    //       !value ||
                    //       !getFieldValue('toDate') ||
                    //       value.isBefore(getFieldValue('toDate'))
                    //     ) {
                    //       return Promise.resolve()
                    //     }
                    //     return Promise.reject(
                    //       new Error(t('ptsDetail.fromDateMustBeBeforeToDate'))
                    //     )
                    //   },
                    // }),
                  ]}
                >
                  <DatePicker
                    className="w-full"
                    prefix={<CalendarOutlined />}
                    suffixIcon={null}
                    placeholder={t('ptsDetail.effectiveDateToPlaceholder')}
                    format={DISPLAY_DATE}
                  />
                </Form.Item>
                <Form.Item
                  name="toDate"
                  label={t('ptsDetail.effectiveDateTo')}
                  className="w-1/4"
                  rules={[
                    {
                      required: true,
                      message: t('ptsDetail.effectiveDateToMessage'),
                    },
                    // ({ getFieldValue }) => ({
                    //   validator(_, value) {
                    //     if (
                    //       !value ||
                    //       !getFieldValue('fromDate') ||
                    //       value.isAfter(getFieldValue('fromDate'))
                    //     ) {
                    //       return Promise.resolve()
                    //     }
                    //     return Promise.reject(
                    //       new Error(t('ptsDetail.toDateMustBeAfterFromDate'))
                    //     )
                    //   },
                    // }),
                  ]}
                >
                  <DatePicker
                    className="w-full"
                    prefix={<CalendarOutlined />}
                    suffixIcon={null}
                    placeholder={t('ptsDetail.effectiveDateToPlaceholder')}
                    format={DISPLAY_DATE}
                  />
                </Form.Item>
              </div>
            </div>

            <Form.List name="ptsDetailDtos">
              {(fields, { add, remove }) => {
                const sortedFields = [...fields].sort((a, b) => {
                  const aTime =
                    form.getFieldValue([
                      'ptsDetailDtos',
                      a.name,
                      'completeTime',
                    ]) || 0
                  const bTime =
                    form.getFieldValue([
                      'ptsDetailDtos',
                      b.name,
                      'completeTime',
                    ]) || 0

                  if (sort === 'asc') {
                    return aTime - bTime
                  } else {
                    return bTime - aTime
                  }
                })

                return (
                  <div className="flex flex-col">
                    <div className="px-5 py-3 bg-[#E6F0F3] flex w-full justify-between items-center mb-4">
                      <div className="font-medium text-base">
                        {t('ptsDetail.listPTS')}
                      </div>
                      <div>
                        <Dropdown
                          menu={{
                            items: [
                              {
                                key: 'asc',
                                label: t('ptsDetail.sortAscending'),
                                onClick: () => {
                                  dispatch(setSort('asc'))
                                },
                              },
                              {
                                key: 'desc',
                                label: t('ptsDetail.sortDescending'),
                                onClick: () => {
                                  dispatch(setSort('desc'))
                                },
                              },
                              {
                                key: 'none',
                                label: t('ptsDetail.sortIndex'),
                                onClick: () => {
                                  dispatch(setSort('none'))
                                },
                              },
                            ],
                            selectedKeys: [sort],
                          }}
                        >
                          <Space className="text-primary !text-base font-semibold border-r-[2px] border-[#99C3CE] px-6">
                            {t('common.sort')}
                            <CaretDownOutlined />
                          </Space>
                        </Dropdown>
                        <Button
                          type="text"
                          icon={<PlusOutlined />}
                          className="!text-primary !text-base !font-semibold !px-1 ml-5"
                          onClick={() => add()}
                        >
                          {t('ptsDetail.createPTS')}
                        </Button>
                      </div>
                    </div>
                    {fields.length <= 0 ? (
                      <div className="flex justify-center w-full flex-col items-center pb-4 gap-y-4">
                        <div>{t('ptsDetail.noPTS')}</div>
                        <Button
                          icon={<PlusOutlined />}
                          onClick={() => add()}
                          type="primary"
                        >
                          {t('ptsDetail.createPTS')}
                        </Button>
                      </div>
                    ) : (
                      (sort === 'none' ? fields : sortedFields).map(field => (
                        <div key={field.key}>
                          <div className="pl-[10px] pr-5 text-base font-medium flex flex-row w-full items-center justify-between">
                            <div>
                              <MoreOutlined /> {t('ptsDetail.pts')} #
                              {field.name + 1}
                            </div>
                            <div className="flex gap-x-2 items-center">
                              <Form.Item
                                name={[field.name, 'status']}
                                valuePropName="checked"
                                noStyle
                                initialValue={true}
                              >
                                <Switch />
                              </Form.Item>
                              <Button
                                type="text"
                                icon={<DeleteOutlined />}
                                onClick={() => {
                                  remove(field.name)
                                }}
                              />
                            </div>
                          </div>
                          <div className="px-5 py-3 w-full">
                            <div className="w-full flex justify-between gap-x-5">
                              <Form.Item
                                label={t('ptsDetail.jobCode_Name')}
                                className="w-1/5"
                                name={[field.name, 'ptsTaskId']}
                                rules={[
                                  {
                                    required: true,
                                    message: t('ptsDetail.jobCodeMessage'),
                                  },
                                ]}
                              >
                                <Select
                                  showSearch
                                  filterOption={selectFilterOption}
                                  loading={ptsTaskLoading}
                                  options={ptsTaskData?.items.map(
                                    (item: IPTSTask) => ({
                                      value: item.id,
                                      label: `${item.code} - ${item.description}`,
                                    })
                                  )}
                                  placeholder={t(
                                    'ptsDetail.jobCode_NamePlaceholder'
                                  )}
                                />
                              </Form.Item>

                              <Form.Item
                                label={
                                  <p className="line-clamp-1">
                                    {t('ptsDetail.completeTime')}
                                  </p>
                                }
                                className="w-1/5"
                                name={[field.name, 'completeTime']}
                                rules={[
                                  {
                                    required: true,
                                    message: t('ptsDetail.completeTimeMessage'),
                                  },
                                  // ({ getFieldValue }) => ({
                                  //   validator: (_, value) => {
                                  //     if (value > 0) {
                                  //       return Promise.reject(
                                  //         new Error(
                                  //           t('ptsDetail.completeTimeMessage2')
                                  //         )
                                  //       )
                                  //     }
                                  //     if (value < -getFieldValue('groundTime')) {
                                  //       return Promise.reject(
                                  //         new Error(
                                  //           t('ptsDetail.completeTimeMessage3')
                                  //         )
                                  //       )
                                  //     }
                                  //     return Promise.resolve()
                                  //   },
                                  // }),
                                ]}
                              >
                                <InputNumber
                                  className="!w-full"
                                  placeholder={t(
                                    'ptsDetail.completeTimePlaceholder'
                                  )}
                                />
                              </Form.Item>
                              <Form.Item
                                label={t('ptsDetail.executeOUIds')}
                                className="w-1/5"
                                name={[field.name, 'executeOUIds']}
                              >
                                <Select
                                  mode="multiple"
                                  allowClear
                                  showSearch
                                  filterOption={selectFilterOption}
                                  options={flattenedDepartureData.map(
                                    (item: any) => ({
                                      value: item.organizationUnitId,
                                      label: item.name,
                                    })
                                  )}
                                  placeholder={t(
                                    'ptsDetail.executeOUIdsPlaceholder'
                                  )}
                                />
                              </Form.Item>
                              <Form.Item
                                label={t('ptsDetail.managementResponsibility')}
                                className="w-1/5"
                                name={[field.name, 'organizationId']}
                              >
                                <Select
                                  allowClear
                                  showSearch
                                  filterOption={selectFilterOption}
                                  options={flattenedDepartureData.map(
                                    (item: any) => ({
                                      value: item.organizationUnitId,
                                      label: item.name,
                                    })
                                  )}
                                  placeholder={t(
                                    'ptsDetail.managementResponsibilityPlaceholder'
                                  )}
                                />
                              </Form.Item>
                              <Form.Item
                                label={t('ptsDetail.jobGroup')}
                                className="w-1/5"
                                name={[field.name, 'groupPtsTaskId']}
                              >
                                <Select
                                  allowClear
                                  showSearch
                                  filterOption={selectFilterOption}
                                  options={groupPtsTaskData?.items.map(
                                    (item: IGroupPTSTask) => ({
                                      value: item.id,
                                      label: item.name,
                                    })
                                  )}
                                  placeholder={t(
                                    'ptsDetail.jobGroupPlaceholder'
                                  )}
                                />
                              </Form.Item>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                )
              }}
            </Form.List>
          </div>
        </Form>
      </div>
    </Spin>
  )
}

export default PTSCopyPage
