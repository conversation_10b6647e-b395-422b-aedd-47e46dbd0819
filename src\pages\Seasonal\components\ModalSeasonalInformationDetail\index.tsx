/* eslint-disable react-hooks/exhaustive-deps */
import { DISPLAY_DATE, TIME_ONLY } from '@/src/constants'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getSeasonalFlightDetail } from '@/src/service/seasonal'
import { closeSeasonalInformationModal } from '@/src/store/SeasonalSlice'
import { useQuery } from '@tanstack/react-query'
import { Checkbox, Col, DatePicker, Form, Input, Modal, Row } from 'antd'
import dayjs from 'dayjs'
import { useEffect } from 'react'

const { RangePicker } = DatePicker

const ModalSeasonlInformationDetail = () => {
  const { visibleSeasonalInformationModal, selectedFlightId } = useAppSelector(
    state => state.seasonal
  )

  const [form] = Form.useForm()

  const dispatch = useAppDispatch()

  const { data: flightDetail } = useQuery({
    queryKey: ['flight-detail-seasonal', selectedFlightId],
    queryFn: () => getSeasonalFlightDetail(selectedFlightId as string),
    enabled: !!selectedFlightId && visibleSeasonalInformationModal,
  })

  useEffect(() => {
    if (flightDetail) {
      form.setFieldsValue({
        ...flightDetail,
        reference: flightDetail.reference,
        date: [dayjs(flightDetail.from), dayjs(flightDetail.to)],
        dow: flightDetail.dow ? flightDetail.dow.split('') : [],
        flightNo: flightDetail.fnCarrier + flightDetail.fnNumber,
        routing: flightDetail.depApSched + ' - ' + flightDetail.arrApSched,
        aircraft: flightDetail.acSubType,
        registerNo: flightDetail.acRegistration,
        std: dayjs(flightDetail.depSchedDt).format(TIME_ONLY),
        sta: dayjs(flightDetail.arrSchedDt).format(TIME_ONLY),
        nature: flightDetail.fltType,
        codeShare: flightDetail.codeShare,
        remark: flightDetail.remark,
        configuration: `${flightDetail?.seatsC + 'J' + flightDetail?.seatsW + 'W' + flightDetail?.seatsY + 'Y'}`,
      })
    }
  }, [flightDetail])

  return (
    <Modal
      width={1000}
      open={visibleSeasonalInformationModal}
      onCancel={() => {
        dispatch(closeSeasonalInformationModal())
      }}
      title="Information Detail"
      footer={null}
    >
      <Form
        labelCol={{ flex: '120px' }}
        labelAlign="left"
        colon={false}
        form={form}
      >
        <Form.Item name="reference" label="Reference">
          <Input className="pointer-events-none" />
        </Form.Item>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="date" label="Date" className="!w-full">
              <RangePicker
                className="!w-full pointer-events-none"
                format={DISPLAY_DATE}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item className="w-full" name="dow" label="DOW">
              <Checkbox.Group className="!gap-x-5 flex pointer-events-none">
                <Col span={2}>
                  <Checkbox value="1" style={{ lineHeight: '32px' }}>
                    1
                  </Checkbox>
                </Col>
                <Col span={2}>
                  <Checkbox value="2" style={{ lineHeight: '32px' }}>
                    2
                  </Checkbox>
                </Col>
                <Col span={2}>
                  <Checkbox value="3" style={{ lineHeight: '32px' }}>
                    3
                  </Checkbox>
                </Col>
                <Col span={2}>
                  <Checkbox value="4" style={{ lineHeight: '32px' }}>
                    4
                  </Checkbox>
                </Col>
                <Col span={2}>
                  <Checkbox value="5" style={{ lineHeight: '32px' }}>
                    5
                  </Checkbox>
                </Col>
                <Col span={2}>
                  <Checkbox value="6" style={{ lineHeight: '32px' }}>
                    6
                  </Checkbox>
                </Col>
                <Col span={2}>
                  <Checkbox value="7" style={{ lineHeight: '32px' }}>
                    7
                  </Checkbox>
                </Col>
              </Checkbox.Group>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="flightNo" label="Flight No">
              <Input className="pointer-events-none" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="aircraft" label="Aircraft">
              <Input className="pointer-events-none" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="routing" label="Routing">
              <Input className="pointer-events-none" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="STD - STA">
              <div className="flex gap-x-4">
                <Form.Item name="std" noStyle>
                  <Input placeholder="STD" className="pointer-events-none" />
                </Form.Item>
                <Form.Item name="sta" noStyle>
                  <Input placeholder="STA" className="pointer-events-none" />
                </Form.Item>
              </div>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="fltType" label="Nature">
              <Input className="pointer-events-none" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="codeShare" label="Code share">
              <Input className="pointer-events-none" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="configuration" label="Configuration">
              <Input className="pointer-events-none" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="availableSeats" label="A. Seats">
              <Input className="pointer-events-none" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  )
}

export default ModalSeasonlInformationDetail
