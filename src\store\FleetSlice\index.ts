import { createSlice } from '@reduxjs/toolkit'

interface FleetState {
  visibleFleetModal: boolean
  selectedFleetId: string | null
  params: {
    SkipCount: number
    MaxResultCount: number
    Filter?: string
  }
  filterDebounce: string
}

const initialState: FleetState = {
  visibleFleetModal: false,
  selectedFleetId: null,
  params: {
    SkipCount: 0,
    MaxResultCount: 20,
  },
  filterDebounce: '',
}

const fleetSlice = createSlice({
  name: 'fleet',
  initialState,
  reducers: {
    openFleetModal(state) {
      state.visibleFleetModal = true
    },
    closeFleetModal(state) {
      state.visibleFleetModal = false
    },
    setSelectedFleetId(state, action) {
      state.selectedFleetId = action.payload
    },
    setParams(state, action) {
      state.params = { ...state.params, ...action.payload }
    },
    resetState() {
      return initialState
    },
    setFilterDebounce(state, action) {
      state.filterDebounce = action.payload
    },
  },
})

export const {
  openFleetModal,
  closeFleetModal,
  setSelectedFleetId,
  setParams,
  resetState,
  setFilterDebounce,
} = fleetSlice.actions
export default fleetSlice.reducer
