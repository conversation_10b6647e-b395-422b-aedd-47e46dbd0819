import { logo } from '@/src/assets/image'
import { ACCESS_TOKEN } from '@/src/constants'
import cookie from 'cookiejs'
import { Navigate, Outlet } from 'react-router'

const AuthLayout = () => {
  const access_token = cookie.get(ACCESS_TOKEN)

  if (access_token) {
    return <Navigate to={'/'} replace />
  } else {
    return (
      <div className="bg-image">
        <div className="w-full h-screen flex">
          <div className="p-10 w-1/2">
            <img src={logo} alt="image" />
          </div>
          <div className="w-1/2 flex items-center justify-center">
            <Outlet />
          </div>
        </div>
      </div>
    )
  }
}

export default AuthLayout
