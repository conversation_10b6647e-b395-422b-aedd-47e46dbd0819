/* eslint-disable @typescript-eslint/no-explicit-any */
import { ISO_DATE, ISO_DATETIME } from '@/src/constants'
import { DayPilotScheduler } from '@daypilot/daypilot-lite-react'
import dayjs from 'dayjs'
import moment from 'moment'
import { useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'

interface Props {
  data: any
}

const getTimezoneOffsetHours = (): number => {
  const offsetMinutes = new Date().getTimezoneOffset() // VD: -420
  return -offsetMinutes / 60 // Đổi dấu để đúng chiều UTC
}

const timezone = getTimezoneOffsetHours()

const findMinEstimateStart = (data: any): string | null => {
  // Fix: Handle both single object and array cases
  let ptsTasks: any[] = []

  if (Array.isArray(data)) {
    ptsTasks = data.reduce((acc: any[], flight: any) => {
      if (flight?.ptsTasks && Array.isArray(flight.ptsTasks)) {
        acc.push(...flight.ptsTasks)
      }
      return acc
    }, [])
  } else if (data?.ptsTasks) {
    ptsTasks = data.ptsTasks
  }

  if (!ptsTasks || ptsTasks.length === 0) {
    return null
  }

  // Use dayjs to find the minimum estimateStart
  const minTask = ptsTasks.reduce((min: any, task: any) => {
    const currentTime = dayjs(task.estimateStart)
    const minTime = dayjs(min.estimateStart)
    return currentTime.isBefore(minTime) ? task : min
  })

  return minTask.estimateStart
}

const GanttChart = (props: Props) => {
  const { data } = props

  const schedulerRef = useRef<any>(null)

  const { t } = useTranslation()

  const processFlightData = (data: any) => {
    // Handle array of flight data - get ptsTasks from all flights
    let ptsTasks: any[] = []

    if (Array.isArray(data)) {
      // If data is an array, collect all ptsTasks from all flights
      ptsTasks = data.reduce((acc: any[], flight: any) => {
        if (flight?.ptsTasks && Array.isArray(flight.ptsTasks)) {
          acc.push(...flight.ptsTasks)
        }
        return acc
      }, [])
    } else if (data?.ptsTasks) {
      // If data is a single flight object
      ptsTasks = data.ptsTasks
    }

    // Early return if no tasks
    if (!Array.isArray(ptsTasks) || ptsTasks.length === 0) {
      return { resources: [], events: [] }
    }

    const groupedByTaskId = ptsTasks.reduce((acc: any, task: any) => {
      // Nếu groupPtsTaskId null, sử dụng ptsDetailId làm groupId
      const groupId = task.groupPtsTaskId || `single-task-${task.ptsDetailId}`
      if (!acc[groupId]) acc[groupId] = []
      acc[groupId].push(task)
      return acc
    }, {})

    const resources: any[] = []
    const events: any[] = []

    Object.entries(groupedByTaskId).forEach(
      ([groupId, tasks]: [string, any], index) => {
        // Nếu groupPtsTaskId null, sử dụng ptsDescription làm tên nhóm
        const groupName = tasks[0].groupPtsTaskName || tasks[0].ptsDescription

        // Tạo 2 resources riêng biệt cho kế hoạch và thực tế
        resources.push({
          id: `group-${index}-plan`,
          name: `${groupName} (${t('common.plan')})`,
          groupName,
          type: 'plan-row',
        })

        resources.push({
          id: `group-${index}-actual`,
          name: `${groupName} (${t('common.actual')})`,
          groupName,
          type: 'actual-row',
        })

        const sortedTasks = tasks.sort((a: any, b: any) =>
          dayjs(a.estimateStart).diff(dayjs(b.estimateStart))
        )

        const planStart = dayjs(sortedTasks[0].estimateStart).add(
          timezone,
          'hours'
        )
        const planEnd = dayjs(sortedTasks[sortedTasks.length - 1].estimateStart)
          .add(1, 'minutes')
          .add(timezone, 'hours')

        const firstTaskNumber = 1
        const lastTaskNumber = sortedTasks.length
        const displayText =
          lastTaskNumber > 1
            ? `${firstTaskNumber}...${lastTaskNumber}`
            : `${firstTaskNumber}`

        // Event cho hàng kế hoạch
        events.push({
          id: `${groupId}-plan`,
          text: displayText,
          start: planStart.toISOString(),
          end: planEnd.toISOString(),
          resource: `group-${index}-plan`,
          type: 'plan',
          layer: 1,
          groupData: {
            groupId,
            groupName,
            tasks: sortedTasks,
            taskCount: sortedTasks.length,
            firstTaskNumber,
            lastTaskNumber,
          },
        })

        // Event cho hàng thực tế - tạo segments cho từng đoạn
        const hasActualData = sortedTasks.some(
          (task: any) => task.actualStart !== null
        )

        if (hasActualData) {
          // Lọc ra các task có actualStart và sắp xếp theo actualStart
          const actualTasks = sortedTasks
            .filter((task: any) => task.actualStart !== null)
            .sort((a: any, b: any) =>
              dayjs(a.actualStart).diff(dayjs(b.actualStart))
            )

          if (actualTasks.length > 0) {
            // Tạo segments cho actual line
            for (let i = 0; i < actualTasks.length; i++) {
              const currentTask = actualTasks[i]
              const nextTask = actualTasks[i + 1]

              // Xác định start và end cho segment hiện tại
              const segmentStart = dayjs(currentTask.actualStart).add(
                timezone,
                'hours'
              )
              let segmentEnd

              if (nextTask) {
                // Có task tiếp theo - segment kết thúc tại task tiếp theo
                segmentEnd = dayjs(nextTask.actualStart).add(timezone, 'hours')
              } else {
                // Task cuối cùng - segment kết thúc sau 1 phút
                segmentEnd = segmentStart.add(1, 'minutes')
              }

              // Xác định màu sắc dựa trên trạng thái của task đích (nextTask)
              let segmentColor = '#69c0ff' // Blue - đúng kế hoạch (default)
              let borderColor = '#40a9ff'

              if (nextTask) {
                // Có task tiếp theo - màu dựa trên trạng thái của nextTask
                const nextActualTime = dayjs(nextTask.actualStart)
                const nextEstimateTime = dayjs(nextTask.estimateStart)

                if (nextActualTime.isBefore(nextEstimateTime)) {
                  segmentColor = '#95de64' // Green - task đích sớm hơn kế hoạch
                  borderColor = '#73d13d'
                } else if (nextActualTime.isAfter(nextEstimateTime)) {
                  segmentColor = '#ff7875' // Red - task đích trễ hơn kế hoạch
                  borderColor = '#ff4d4f'
                }
              } else {
                // Task cuối cùng - màu dựa trên chính task hiện tại
                const actualTime = dayjs(currentTask.actualStart)
                const estimateTime = dayjs(currentTask.estimateStart)

                if (actualTime.isBefore(estimateTime)) {
                  segmentColor = '#95de64' // Green - sớm hơn kế hoạch
                  borderColor = '#73d13d'
                } else if (actualTime.isAfter(estimateTime)) {
                  segmentColor = '#ff7875' // Red - trễ hơn kế hoạch
                  borderColor = '#ff4d4f'
                }
              }

              // Tìm task number trong danh sách gốc
              const originalTaskIndex = sortedTasks.findIndex(
                (t: any) => t.ptsDetailId === currentTask.ptsDetailId
              )
              const taskNumber = originalTaskIndex + 1

              events.push({
                id: `${groupId}-actual-segment-${i}`,
                text: taskNumber.toString(),
                start: segmentStart.toISOString(),
                end: segmentEnd.toISOString(),
                resource: `group-${index}-actual`,
                type: 'actual-segment',
                layer: 2,
                segmentColor,
                borderColor,
                taskNumber,
                currentTask,
                estimateStart: dayjs(currentTask.estimateStart)
                  .add(timezone, 'hours')
                  .toISOString(),
                actualStart: segmentStart.toISOString(),
                groupData: {
                  groupId,
                  groupName,
                  tasks: sortedTasks,
                  actualTasks: actualTasks,
                  taskCount: sortedTasks.length,
                  actualTaskCount: actualTasks.length,
                  firstTaskNumber,
                  lastTaskNumber,
                },
              })
            }
          }
        }
      }
    )

    return { resources, events }
  }

  const countUniqueDays = (startISO: string, endISO: string): number => {
    const startDate = new Date(startISO)
    const endDate = new Date(endISO)

    const startDay = new Date(
      startDate.getFullYear(),
      startDate.getMonth(),
      startDate.getDate()
    )
    const endDay = new Date(
      endDate.getFullYear(),
      endDate.getMonth(),
      endDate.getDate()
    )

    const diffTime = endDay.getTime() - startDay.getTime()
    const diffDays = diffTime / (1000 * 60 * 60 * 24)

    return diffDays + 1
  }

  useEffect(() => {
    if (!schedulerRef.current) return

    // Add error handling for data processing
    let processedData
    try {
      processedData = processFlightData(data)
    } catch (error) {
      console.error('Error processing flight data:', error)
      processedData = { resources: [], events: [] }
    }

    const scheduler = schedulerRef.current.control
    const { resources, events } = processedData

    // Fix: Get ptsStart and ptsEnd from the correct data structure
    let startDate, endDate
    if (Array.isArray(data)) {
      startDate = data[0]?.ptsStart
      endDate = data[0]?.ptsEnd
    } else {
      startDate = data?.ptsStart
      endDate = data?.ptsEnd
    }

    const config = {
      startDate: dayjs(startDate).format(ISO_DATE),
      days: countUniqueDays(
        moment(startDate).format(ISO_DATE),
        moment(endDate).format(ISO_DATE)
      ),
      scale: 'CellDuration',
      timeZone: undefined,
      useEventBoxes: 'Never',
      cellDuration: 1,
      cellWidth: 20,
      rowHeight: 35,
      rowHeaderWidth: 350,
      visibleStart: dayjs(startDate).format(ISO_DATETIME),
      visibleEnd: dayjs(endDate).format(ISO_DATETIME),
      timeHeaders: [
        { groupBy: 'Hour', format: 'HH:mm' },
        { groupBy: 'Cell', format: 'mm' },
      ],
      onTimeRangeSelected: (args: any) => {
        args.preventDefault()
      },
      resources,
      events,
      onBeforeEventRender: (args: any) => {
        const {
          type,
          groupData,
          segmentColor,
          borderColor,
          taskNumber,
          currentTask,
        } = args.data

        args.data.barHidden = true
        args.data.fontColor = '#ffffff'
        args.data.fontSize = '10px'
        args.data.fontWeight = 'normal'
        args.data.textAlign = 'center'
        args.data.cssClass = 'custom-tooltip'

        // Enhanced tooltip với formatting tốt hơn
        if (type === 'plan' && groupData?.tasks?.length) {
          const taskList = groupData.tasks
            .map((task: any, index: number) => {
              const taskNum = index + 1
              const estimatedTime = dayjs(task.estimateStart).format('HH:mm')
              return `${taskNum}. ${task.ptsDescription}\n   • ${t('common.plan')}: ${estimatedTime}`
            })
            .join('\n\n')

          const tooltipHeader = `${t('pts.jobGroup')}: ${groupData.groupName}\n${t('ptsDetail.managementResponsibility')}: ${groupData.tasks[0].departmentName}\n(${t('common.plan')})\n\n`
          args.data.toolTip = tooltipHeader + taskList
        } else if (type === 'actual-segment' && currentTask) {
          const estimatedTime = dayjs(currentTask.estimateStart).format('HH:mm')
          const actualTime = dayjs(currentTask.actualStart).format('HH:mm')

          // Tooltip hiển thị thông tin về segment này
          const tooltipHeader = `${t('pts.jobGroup')}: ${groupData.groupName}\n${t('ptsDetail.managementResponsibility')}: ${currentTask.departmentName || 'N/A'}\n(${t('common.actual')} - ${t('common.line')} ${taskNumber})\n\n`

          // Tìm task tiếp theo trong actualTasks để hiển thị thông tin segment
          const currentIndex = groupData.actualTasks.findIndex(
            (t: any) => t.ptsDetailId === currentTask.ptsDetailId
          )
          const nextTask = groupData.actualTasks[currentIndex + 1]

          let segmentInfo = `${t('common.from')}: ${taskNumber}. ${currentTask.ptsDescription}\n   • ${t('common.actual')}: ${actualTime}`

          if (nextTask) {
            const nextTaskIndex =
              groupData.tasks.findIndex(
                (t: any) => t.ptsDetailId === nextTask.ptsDetailId
              ) + 1
            const nextActualTime = dayjs(nextTask.actualStart).format('HH:mm')
            const nextEstimateTime = dayjs(nextTask.estimateStart).format(
              'HH:mm'
            )
            const nextStatus = dayjs(nextTask.actualStart).isBefore(
              dayjs(nextTask.estimateStart)
            )
              ? `✓ (${t('common.early')})`
              : dayjs(nextTask.actualStart).isAfter(
                    dayjs(nextTask.estimateStart)
                  )
                ? `⚠ (${t('common.late')})`
                : `= (${t('common.onTime')})`

            segmentInfo += `\n${t('common.to')}: ${nextTaskIndex}. ${nextTask.ptsDescription}\n   • ${t('common.plan')}: ${nextEstimateTime} | ${t('common.actual')}: ${nextActualTime} ${nextStatus}`
          } else {
            const currentStatus = dayjs(actualTime, 'HH:mm').isBefore(
              dayjs(estimatedTime, 'HH:mm')
            )
              ? `✓ (${t('common.early')})`
              : dayjs(actualTime, 'HH:mm').isAfter(
                    dayjs(estimatedTime, 'HH:mm')
                  )
                ? `⚠ (${t('common.late')})`
                : `= (${t('common.onTime')})`
            segmentInfo += `\n   • ${t('common.plan')}: ${estimatedTime} ${currentStatus}\n(${t('common.last')} ${t('common.line')})`
          }

          args.data.toolTip = tooltipHeader + segmentInfo
        }

        // Styling cho plan events
        if (type === 'plan') {
          const { taskCount, tasks } = groupData || {}

          args.data.backColor = '#d9d9d9' // gray-4
          args.data.borderColor = '#bfbfbf' // gray-5
          args.data.borderWidth = 1
          args.data.height = 25
          args.data.marginTop = 5

          // Logic hiển thị số thứ tự task cho plan
          if (taskCount && taskCount > 1 && tasks) {
            const sortedTasks = [...tasks].sort((a, b) =>
              dayjs(a.estimateStart).diff(dayjs(b.estimateStart))
            )

            const groupStartTime = dayjs(sortedTasks[0].estimateStart)
            const groupEndTime = dayjs(
              sortedTasks[sortedTasks.length - 1].estimateStart
            )
            const totalDuration = groupEndTime.diff(groupStartTime, 'minutes')

            const positionedNumbers = sortedTasks.map((task, index) => {
              const taskTime = dayjs(task.estimateStart)
              const timeFromStart = taskTime.diff(groupStartTime, 'minutes')

              let position = 0
              if (totalDuration > 0) {
                position = (timeFromStart / totalDuration) * 100
              } else {
                position =
                  sortedTasks.length > 1
                    ? (index / (sortedTasks.length - 1)) * 100
                    : 0
              }

              return {
                number: index + 1,
                position: Math.max(0, Math.min(100, position)),
              }
            })

            positionedNumbers.sort((a, b) => a.position - b.position)

            args.data.html = `
              <div style="position: relative; width: 100%; height: 100%; padding: 0px;">
                ${positionedNumbers
                  .map(
                    ({ number, position }) =>
                      `<span style="
                    position: absolute;
                    left: ${position}%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 10px;
                    color: #ffffff;
                    white-space: nowrap;
                    z-index: 10;
                  ">${number}</span>`
                  )
                  .join('')}
              </div>
            `
          } else if (taskCount === 1) {
            args.data.html = `
              <div style="position: relative; width: 100%; height: 100%; padding: 0px;">
                <span style="
                  position: absolute;
                  left: 5px;
                  top: 50%;
                  transform: translateY(-50%);
                  font-size: 10px;
                  color: #ffffff;
                  white-space: nowrap;
                  z-index: 10;
                ">1</span>
              </div>
            `
          }
        }

        // Styling cho actual-segment events
        if (type === 'actual-segment') {
          args.data.backColor = segmentColor
          args.data.borderColor = borderColor
          args.data.borderWidth = 2
          args.data.height = 25
          args.data.marginTop = 5

          // Hiển thị số task ở vị trí bắt đầu của segment
          args.data.html = `
            <div style="position: relative; width: 100%; height: 100%; padding: 0px;">
              <span style="
                position: absolute;
                left: 5px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 10px;
                color: #ffffff;
                white-space: nowrap;
                z-index: 10;
              ">${taskNumber}</span>
            </div>
          `
        }
      },
      onBeforeRowHeaderRender: (args: any) => {
        if (args.row.data.type === 'plan-row') {
          args.row.backColor = '#f8f9fa'
          args.row.fontColor = '#495057'
          args.row.fontWeight = 'normal'
          args.row.fontSize = '12px'
        } else if (args.row.data.type === 'actual-row') {
          args.row.backColor = '#e3f2fd'
          args.row.fontColor = '#1565c0'
          args.row.fontWeight = 'normal'
          args.row.fontSize = '12px'
        }
      },
      timeRangeSelectedHandling: 'Disabled',
      eventClickHandling: 'Disabled',
      eventMoveHandling: 'Disabled',
      eventResizeHandling: 'Disabled',
    }

    scheduler.update(config)

    setTimeout(() => {
      // Fix: Use the same data structure logic for findMinEstimateStart
      const minStart = Array.isArray(data)
        ? findMinEstimateStart(data[0])
        : findMinEstimateStart(data)

      if (minStart) {
        scheduler.scrollTo(dayjs(minStart).local().format(ISO_DATETIME))
      }
    }, 100)
  }, [data])

  return (
    <div>
      <DayPilotScheduler ref={schedulerRef} height={1000} />
    </div>
  )
}

export default GanttChart
