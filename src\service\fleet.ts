/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1/fleets`

const getFleet = async (params?: any) => {
  const api = `${prefix}`
  const response = await axios.get(api, { params })
  return response.data
}

const createFleet = async (body: any) => {
  const api = `${prefix}`

  const response = await axios.post(api, body)
  return response.data
}

const updateFleet = async (body: any) => {
  const api = `${prefix}/${body.id}`

  const response = await axios.put(api, body)
  return response.data
}

const removeFleet = async (id: string) => {
  const api = `${prefix}/${id}`

  const response = await axios.delete(api)
  return response.data
}

export { createFleet, getFleet, removeFleet, updateFleet }
