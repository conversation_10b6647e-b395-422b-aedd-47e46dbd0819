/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'
const prefix = `api/identity/roles`

const getRole = async (params: any) => {
  const api = `${prefix}`
  const response = await axios.get(api, { params })
  return response.data
}
const createRole = async (body: any) => {
  const api = `${prefix}`

  const response = await axios.post(api, body)
  return response.data
}

const updateRole = async (body: any) => {
  const api = `${prefix}/${body.id}`

  const response = await axios.put(api, body)
  return response.data
}

const removeRole = async (id: string) => {
  const api = `${prefix}/${id}`

  const response = await axios.delete(api)
  return response.data
}

const getRoleAll = async () => {
  const api = `${prefix}/all`
  const response = await axios.get(api)
  return response.data
}

export { createRole, getRole, removeRole, updateRole, getRoleAll }
