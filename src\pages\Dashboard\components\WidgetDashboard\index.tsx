import {
  alarm,
  clock,
  clockOSP,
  clockOTP,
  infor,
  pts,
} from '@/src/assets/image'
import {
  WIDGET_ALL_FLIGHT,
  WIDGET_GROUP_TASK_LATE_OSP,
  WIDGET_GROUP_TASK_LATE_OTP,
  WIDGET_LATE_GT,
  WIDGET_NO_RECORD_PTS,
  WIDGET_ON_TIME_GT,
} from '@/src/constants'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  getDashboardFlight,
  getDashboardGroupTaskPTSLateOTP,
} from '@/src/service/dashboard'
import { faPlaneDeparture } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useQuery } from '@tanstack/react-query'
import { Card, Tooltip } from 'antd'
import QueryString from 'qs'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

const WidgetDashboard = () => {
  const { t } = useTranslation()

  const { params } = useAppSelector(state => state.dashboard)

  const navigate = useNavigate()

  const { data: dashboardFlightLate, isLoading: isLoadingLate } = useQuery({
    queryKey: ['get-dashboard-flight-3', params],
    queryFn: () =>
      getDashboardFlight({
        type: WIDGET_LATE_GT,
        ...params,
      }),
  })

  const { data: dashboardFlightOnTime, isLoading: isLoadingOnTime } = useQuery({
    queryKey: ['get-dashboard-flight-2', params],
    queryFn: () =>
      getDashboardFlight({
        type: WIDGET_ON_TIME_GT,
        ...params,
      }),
  })

  const { data: dashboardFlightAll, isLoading: isLoadingAll } = useQuery({
    queryKey: ['get-dashboard-flight-1', params],
    queryFn: () =>
      getDashboardFlight({
        type: WIDGET_ALL_FLIGHT,
        ...params,
      }),
  })

  const { data: dashboardFlightNoRecordPts, isLoading: isLoadingNoRecordPts } =
    useQuery({
      queryKey: ['get-dashboard-flight-4', params],
      queryFn: () =>
        getDashboardFlight({
          type: WIDGET_NO_RECORD_PTS,
          ...params,
        }),
    })

  // const { data: dashboardTaskPTSLate, isLoading: isLoadingTaskPTSLate } =
  //   useQuery({
  //     queryKey: ['get-dashboard-task-pts-late', params],
  //     queryFn: () =>
  //       getDashboardTaskPTSLate({
  //         type: WIDGET_TASK_LATE,
  //         ...params,
  //       }),
  //   })

  const {
    data: dashboardGroupTaskPTSLateOTP,
    isLoading: isLoadingGroupTaskPTSLateOTP,
  } = useQuery({
    queryKey: ['get-dashboard-group-task-pts-late-otp', params],
    queryFn: () =>
      getDashboardGroupTaskPTSLateOTP({
        type: WIDGET_GROUP_TASK_LATE_OTP,
        ...params,
      }),
  })

  const {
    data: dashboardGroupTaskPTSLateOSP,
    isLoading: isLoadingGroupTaskPTSLateOSP,
  } = useQuery({
    queryKey: ['get-dashboard-group-task-pts-late-osp', params],
    queryFn: () =>
      getDashboardGroupTaskPTSLateOTP({
        type: WIDGET_GROUP_TASK_LATE_OSP,
        ...params,
      }),
  })

  return (
    <div className="grid grid-cols-12 gap-4">
      <Card
        size="small"
        className="hover:shadow-md cursor-pointer col-span-4"
        classNames={{ body: 'h-full' }}
        loading={isLoadingLate}
        onClick={() => {
          navigate(`/report/report-late-gt?${QueryString.stringify(params)}`)
        }}
      >
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <img src={alarm} />
            <span className="text-sm font-semibold">
              {t('dashboard.lateGT')}
            </span>
          </div>
          <Tooltip
            title={
              <div className="text-black">{t('dashboard.gtExceeded')}</div>
            }
            color="#fff"
            placement="topRight"
          >
            <img src={infor} className="cursor-pointer" />
          </Tooltip>
        </div>
        <div className="w-full justify-between flex items-center">
          <div className="flex items-center h-[calc(100%-22px)] gap-x-2 mt-2">
            <span className="text-center text-[#FB4E4EFF] text-xl font-bold">
              {dashboardFlightLate?.value
                ? dashboardFlightLate?.value.toLocaleString()
                : 0}
            </span>
            <span className="text-sm text-center">{t('dashboard.flight')}</span>
          </div>
          <div className="!rounded-full !text-xl !text-[#FB4E4EFF] border px-1 border-neutral-300 bg-neutral-300">
            {Number.isInteger(dashboardFlightLate?.percent)
              ? dashboardFlightLate?.percent
              : (dashboardFlightLate?.percent || 0).toFixed(1)}
            %
          </div>
        </div>
      </Card>
      <Card
        size="small"
        className="hover:shadow-md cursor-pointer col-span-4"
        classNames={{ body: 'h-full' }}
        loading={isLoadingOnTime}
        onClick={() => {
          navigate(`/report/report-on-time-gt?${QueryString.stringify(params)}`)
        }}
      >
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <img src={clock} />
            <span className="text-sm font-semibold">
              {t('dashboard.onTimeGT')}
            </span>
          </div>
          <Tooltip
            title={
              <div className="text-black">{t('dashboard.gtStandard')}</div>
            }
            placement="topRight"
            color="#fff"
          >
            <img src={infor} className="cursor-pointer" />
          </Tooltip>
        </div>
        <div className="w-full justify-between flex items-center">
          <div className="flex items-center h-[calc(100%-22px)] gap-x-2 mt-2">
            <span className="text-center text-[#327DFFFF] text-xl font-bold">
              {dashboardFlightOnTime?.value
                ? dashboardFlightOnTime?.value.toLocaleString()
                : 0}
            </span>
            <span className="text-sm text-center">{t('dashboard.flight')}</span>
          </div>
          <div className="!rounded-full !text-xl !text-[#327DFFFF] border px-1 border-neutral-300 bg-neutral-300">
            {Number.isInteger(dashboardFlightOnTime?.percent)
              ? dashboardFlightOnTime?.percent
              : (dashboardFlightOnTime?.percent || 0).toFixed(1)}
            %
          </div>
        </div>
      </Card>
      <Card
        size="small"
        className="hover:shadow-md cursor-pointer col-span-4"
        classNames={{ body: 'h-full' }}
        loading={isLoadingNoRecordPts}
        onClick={() => {
          navigate(
            `/report/report-no-record-pts?${QueryString.stringify(params)}`
          )
        }}
      >
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <img src={pts} className="w-4 h-4" />
            <span className="text-sm font-semibold">
              {t('dashboard.noPtsRecorded')}
            </span>
          </div>
          <Tooltip
            title={
              <div className="text-black">{t('dashboard.gtExceeded')}</div>
            }
            color="#fff"
            placement="topRight"
          >
            <img src={infor} className="cursor-pointer" />
          </Tooltip>
        </div>
        <div className="w-full justify-between flex items-center">
          <div className="flex items-center h-[calc(100%-22px)] gap-x-2 mt-2">
            <span className="text-center text-[#FB4E4EFF] text-xl font-bold">
              {dashboardFlightNoRecordPts?.value
                ? dashboardFlightNoRecordPts?.value.toLocaleString()
                : 0}
            </span>
            <span className="text-sm text-center">{t('dashboard.flight')}</span>
          </div>
          <div className="!rounded-full !text-xl !text-[#FB4E4EFF] border px-1 border-neutral-300 bg-neutral-300">
            {Number.isInteger(dashboardFlightNoRecordPts?.percent)
              ? dashboardFlightNoRecordPts?.percent
              : (dashboardFlightNoRecordPts?.percent || 0).toFixed(1)}
            %
          </div>
        </div>
      </Card>
      <Card
        size="small"
        className="hover:shadow-md cursor-pointer col-span-4"
        classNames={{ body: 'h-full' }}
        loading={isLoadingAll}
        onClick={() => {
          const newParams = {
            fromDate: params.fromDate,
            toDate: params.toDate,
            depApSched: params.airport,
          }
          navigate(
            `/flight-schedule/departure?${QueryString.stringify(newParams)}`
          )
        }}
      >
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <FontAwesomeIcon icon={faPlaneDeparture} color="#8B909B" />
            <span className="text-sm font-semibold">
              {t('dashboard.output')}
            </span>
          </div>
          <Tooltip
            title={<div className="text-black">{t('dashboard.departure')}</div>}
            placement="topRight"
            color="#fff"
          >
            <img src={infor} className="cursor-pointer" />
          </Tooltip>
        </div>
        <div className="flex items-center h-[calc(100%-22px)] gap-x-2 mt-2">
          <span className="text-center text-[#327DFFFF] text-xl font-bold">
            {dashboardFlightAll?.value
              ? dashboardFlightAll?.value.toLocaleString()
              : 0}
          </span>
          <span className="text-sm text-center">{t('dashboard.flight')}</span>
        </div>
      </Card>
      {/* <Card
        size="small"
        className="hover:shadow-md cursor-pointer col-span-3"
        classNames={{ body: 'h-full' }}
        loading={isLoadingTaskPTSLate}
        onClick={() => {
          navigate(
            `/report/report-pts-task-late?${QueryString.stringify(params)}`
          )
        }}
      >
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <img src={task} className="w-4 h-4" />
            <span className="text-sm font-semibold">{t('dashboard.task')}</span>
          </div>
          <Tooltip
            title={<div className="text-black">{t('dashboard.task')}</div>}
            placement="topRight"
            color="#fff"
          >
            <img src={infor} className="cursor-pointer" />
          </Tooltip>
        </div>
        <div className="flex items-center h-[calc(100%-22px)] gap-x-2 mt-2">
          <span className="text-center text-[#FB4E4EFF] text-xl font-bold">
            {dashboardTaskPTSLate?.value
              ? dashboardTaskPTSLate?.value.toLocaleString()
              : 0}
          </span>
          <span className="text-sm text-center">{t('dashboard.taskLate')}</span>
        </div>
      </Card> */}
      <Card
        size="small"
        className="hover:shadow-md cursor-pointer col-span-4"
        classNames={{ body: 'h-full' }}
        loading={isLoadingGroupTaskPTSLateOTP}
        onClick={() => {
          navigate(
            `/report/group-task-late?${QueryString.stringify({
              ...params,
              type: WIDGET_GROUP_TASK_LATE_OTP,
            })}`
          )
        }}
      >
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <img src={clockOTP} className="w-4 h-4" />
            <span className="text-sm font-semibold">
              {t('dashboard.lateOTP')}
            </span>
          </div>
          <Tooltip
            title={<div className="text-black">{t('dashboard.lateOTP')}</div>}
            color="#fff"
            placement="topRight"
          >
            <img src={infor} className="cursor-pointer" />
          </Tooltip>
        </div>
        <div className="flex items-center h-[calc(100%-22px)] gap-x-2 mt-2">
          <span className="text-center text-[#FB4E4EFF] text-xl font-bold">
            {dashboardGroupTaskPTSLateOTP?.value
              ? dashboardGroupTaskPTSLateOTP?.value.toLocaleString()
              : 0}
          </span>
          <span className="text-sm text-center">
            {t('dashboard.groupPtsTask')}
          </span>
        </div>
      </Card>
      <Card
        size="small"
        className="hover:shadow-md cursor-pointer col-span-4"
        classNames={{ body: 'h-full' }}
        loading={isLoadingGroupTaskPTSLateOSP}
        onClick={() => {
          navigate(
            `/report/group-task-late?${QueryString.stringify({
              ...params,
              type: WIDGET_GROUP_TASK_LATE_OSP,
            })}`
          )
        }}
      >
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <img src={clockOSP} className="w-4 h-4" />
            <span className="text-sm font-semibold">
              {t('dashboard.lateOSP')}
            </span>
          </div>
          <Tooltip
            title={<div className="text-black">{t('dashboard.lateOSP')}</div>}
            placement="topRight"
            color="#fff"
          >
            <img src={infor} className="cursor-pointer" />
          </Tooltip>
        </div>
        <div className="flex items-center h-[calc(100%-22px)] gap-x-2 mt-2">
          <span className="text-center text-[#FB4E4EFF] text-xl font-bold">
            {dashboardGroupTaskPTSLateOSP?.value
              ? dashboardGroupTaskPTSLateOSP?.value.toLocaleString()
              : 0}
          </span>
          <span className="text-sm text-center">
            {t('dashboard.groupPtsTask')}
          </span>
        </div>
      </Card>
    </div>
  )
}

export default WidgetDashboard
