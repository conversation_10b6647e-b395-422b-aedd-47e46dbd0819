/* eslint-disable react-hooks/exhaustive-deps */
import {
  DISPLAY_DATE,
  ISO_DATETIME,
  ISO_DATETIME_NOSECOND,
} from '@/src/constants'
import { disabledDaysDate } from '@/src/helper/disabledDayDate'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import LineChartDashboard from '@/src/pages/Dashboard/components/LineChartDashboard'
import PieChartDashboard from '@/src/pages/Dashboard/components/PieChartDashboard'
import WidgetDashboard from '@/src/pages/Dashboard/components/WidgetDashboard'
import type { IAirport } from '@/src/schema/IAirport'
import { getAirport } from '@/src/service/airport'
import { resetState, setParams } from '@/src/store/DashboardSlice'
import { useQuery } from '@tanstack/react-query'
import type { TimeRangePickerProps } from 'antd'
import { DatePicker, Select } from 'antd'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import moment from 'moment'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
const { RangePicker } = DatePicker

const DashboardPage = () => {
  const { t } = useTranslation()

  const dispatch = useAppDispatch()

  const { params } = useAppSelector(state => state.dashboard)

  // const formatSecondsToTime = (totalSeconds: number) => {
  //   const duration = moment.duration(totalSeconds, 'seconds')
  //   const hours = Math.floor(duration.asHours())
  //   const minutes = duration.minutes()
  //   const seconds = duration.seconds()

  //   let result = ''

  //   if (hours > 0) {
  //     result += `${hours} ${t('basic.hour')} `
  //   }
  //   if (minutes > 0) {
  //     result += `${minutes} ${t('basic.minute')} `
  //   }
  //   if (seconds > 0 || result === '') {
  //     result += `${seconds} ${t('basic.second')}`
  //   }

  //   return result.trim()
  // }
  const onRangeChange = (
    dates: null | (Dayjs | null)[]
    // dateStrings: string[]
  ) => {
    if (dates) {
      // console.log('From: ', dates[0], ', to: ', dates[1])
      // console.log('From: ', dateStrings[0], ', to: ', dateStrings[1])
      dispatch(
        setParams({
          ...params,
          fromDate: dayjs(dates[0]).startOf('day').format(ISO_DATETIME),
          toDate: dayjs(dates[1]).endOf('day').format(ISO_DATETIME),
        })
      )
      // const [start, end] = dates
      // if (start && end) {
      //   const diffDays = end.diff(start, 'day')
      //   if (diffDays > 7) {
      //     message.warning('Khoảng thời gian không được lớn hơn 7 ngày')
      //   }
      // }
    } else {
      // console.log('Clear')
    }
  }

  const rangePresets: TimeRangePickerProps['presets'] = [
    { label: t('dashboard.last7Days'), value: [dayjs().add(-6, 'd'), dayjs()] },
    // {
    //   label: t('dashboard.last14Days'),
    //   value: [dayjs().add(-14, 'd'), dayjs()],
    // },
    // {
    //   label: t('dashboard.last30Days'),
    //   value: [dayjs().add(-30, 'd'), dayjs()],
    // },
    // {
    //   label: t('dashboard.last90Days'),
    //   value: [dayjs().add(-90, 'd'), dayjs()],
    // },
  ]

  const { data: airportData } = useQuery({
    queryKey: ['airport-list'],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
  })

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <div className="w-full overflow-x-hidden">
      <div className="">
        {t('dashboard.lastestUpdated')} {moment().format(ISO_DATETIME_NOSECOND)}
      </div>

      <div className="grid grid-cols-6 gap-4 ">
        <div className="col-span-full flex flex-row w-full justify-between">
          <div className="col-span-4 text-black text-lg font-bold">
            {t('dashboard.title')}
          </div>
          <div className="col-span-2 flex gap-4 items-center">
            <div className="text-nowrap font-semibold">
              {t('dashboard.refreshDataAt')}:{' '}
              {moment().format(ISO_DATETIME_NOSECOND)}
            </div>
            <RangePicker
              presets={rangePresets}
              onChange={onRangeChange}
              allowClear={false}
              defaultValue={[dayjs().startOf('day'), dayjs().endOf('day')]}
              format={DISPLAY_DATE}
              disabledDate={disabledDaysDate(7)}
            />
            <Select
              className="w-52"
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={val => {
                dispatch(setParams({ ...params, airport: val }))
              }}
              placeholder={t('dashboard.allAirport')}
              options={[
                { value: null, label: `${t('dashboard.allAirport')}` },
                ...(airportData?.items.map((item: IAirport) => ({
                  value: item.iataCode,
                  label: item.iataCode,
                })) || []),
              ]}
            />
          </div>
        </div>
        <div className="w-full col-span-full flex gap-4 max-2xl:flex-col">
          <div className="w-2/3 max-2xl:w-full flex flex-col gap-y-4">
            <WidgetDashboard />
            <LineChartDashboard />
          </div>
          <div className="w-1/3 max-2xl:w-full h-max">
            <PieChartDashboard />
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
