/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import ModalDepoInadDetail from '@/src/pages/Arrival/components/ModalArrivalFlightDetail/TabModalDEPOINAD/ModalDepoInadDetail'
import {
  openModalDepoInadDetail,
  setParamsDepoInad,
  setSelectedDepoInadId,
} from '@/src/store/ModalArrivalFlightSlice'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { Button, message, Modal, Space, Table, Typography } from 'antd'
import type { TableProps } from 'antd/lib'
import styles from './index.module.scss'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { useMutation, useQuery } from '@tanstack/react-query'
import { getDepoInad, removeDepoInad } from '@/src/service/depo_inad'
import { handleApiError } from '@/src/helper/handleApiError'
import { getCountry, getProvinces } from '@/src/service/country'
import { GENDER } from '@/src/constants'

const TabModalDEPOINAD = () => {
  const dispatch = useAppDispatch()

  const { paramsDepoInad, visibleModalArrivalFlightDetail } = useAppSelector(
    state => state.modalArrivalFlight
  )

  const { selectedFlightId } = useAppSelector(state => state.arrival)

  const { data: depoInadList, refetch } = useQuery({
    queryKey: ['depo-inad-list', selectedFlightId, paramsDepoInad],
    queryFn: () => getDepoInad(selectedFlightId as string, paramsDepoInad),
    enabled: !!selectedFlightId && visibleModalArrivalFlightDetail,
  })

  const { data: provinceData } = useQuery({
    queryKey: ['province-list'],
    queryFn: () => getProvinces(),
  })

  const { data: countryData } = useQuery({
    queryKey: ['country-list'],
    queryFn: () => getCountry(),
  })

  const mutationDelete = useMutation({
    mutationFn: async (id: string) => {
      return removeDepoInad(id, selectedFlightId as string)
    },
    onSuccess: () => {
      refetch()
      dispatch(setSelectedDepoInadId(null))
      message.success('Depo Inad deleted successfully!')
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const columns: TableProps<any>['columns'] = [
    {
      key: 'surname',
      title: 'Surname',
      dataIndex: 'surname',
      width: 150,
    },
    {
      key: 'givenName',
      title: 'Given Name',
      dataIndex: 'givenName',
      width: 150,
    },
    {
      key: 'gender',
      title: 'Gender',
      dataIndex: 'gender',
      align: 'center',
      render: (gender: number) => {
        return GENDER[gender as keyof typeof GENDER] || 'Unknown'
      },
    },
    {
      key: 'passport',
      title: 'Passport',
      dataIndex: 'passport',
    },
    {
      key: 'nationality',
      title: 'Citizenship',
      dataIndex: 'citizenship',
      render: (pobCountry: any) => (
        <>
          {countryData?.find((item: any) => item.alpha2 === pobCountry)?.name}
        </>
      ),
    },
    {
      key: 'POB',
      title: 'POB',
      dataIndex: '',
      width: 200,
      render: record => {
        const province = provinceData?.find(
          (item: any) => item.code === record.pobCity
        )

        const country = countryData?.find(
          (item: any) => item.alpha2 === record.pobCountry
        )

        return (
          <>
            {record?.pobDetail}
            {record?.pobDetail && ', '}
            {province?.name}
            {province?.name && ', '}
            {country?.name}
          </>
        )
      },
    },
    {
      key: 'reason',
      title: 'Reason',
      dataIndex: 'reason',
      width: 200,
      render: value => (
        <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
          {value}
        </Typography.Paragraph>
      ),
    },
    {
      key: 'action',
      title: 'Action',
      fixed: 'right',
      render: record => {
        return (
          <Space>
            <Button
              type="text"
              onClick={() => {
                dispatch(openModalDepoInadDetail())
                dispatch(setSelectedDepoInadId(record.id))
              }}
            >
              <EditOutlined />
            </Button>
            <Button
              type="text"
              onClick={() => {
                Modal.confirm({
                  title: 'Delete Depo Inad?',
                  content: 'Do you want to remove this depo inad?',
                  okText: 'Yes',
                  cancelText: 'No',
                  closable: false,
                  onOk: () => mutationDelete.mutate(record.id),
                })
              }}
            >
              <DeleteOutlined />
            </Button>
          </Space>
        )
      },
    },
  ]

  return (
    <div className="bg-[#F0F1F3FF] p-4 min-h-[calc(65vh)] flex flex-col">
      <Table
        columns={columns}
        size="small"
        bordered
        className={`${styles.whiteHeader}`}
        scroll={{ x: 'max-content' }}
        dataSource={depoInadList?.items || []}
        rowKey={record => record.id}
        pagination={{
          total: depoInadList?.items.totalCount || 0,
          current: paramsDepoInad.skipCount / paramsDepoInad.maxResultCount + 1,
          pageSize: paramsDepoInad.maxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParamsDepoInad({
                ...paramsDepoInad,
                skipCount: (page - 1) * pageSize,
                maxResultCount: pageSize,
              })
            )
          },
        }}
      />
      <div className="flex justify-end">
        <Button
          type="primary"
          onClick={() => {
            dispatch(openModalDepoInadDetail())
          }}
        >
          Create Depo Inad
        </Button>
      </div>
      <ModalDepoInadDetail />
    </div>
  )
}

export default TabModalDEPOINAD
