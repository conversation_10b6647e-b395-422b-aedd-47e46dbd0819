/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import ShowTotal from '@/src/components/Showtotal'
import { convertTime } from '@/src/helper/convertTime'
import { handleApiError } from '@/src/helper/handleApiError'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import {
  getFlightDetail,
  getLateGroupTask,
  updateReasonLateGroupTask,
} from '@/src/service/flight'
import {
  closeModalReasonLate,
  setParamsGroupTask,
  setSelectedPTSMasterId,
} from '@/src/store/DepartureSlice'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Checkbox, Modal, Table } from 'antd'
import type { TableProps } from 'antd/lib'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'

const ModalLateGroupTask = () => {
  const [tableData, setTableData] = useState<any[]>([])

  const {
    visibleModalReasonLate,
    paramsLateGroupTask,
    selectedFlightId,
    selectedPTSMasterId,
  } = useAppSelector(state => state.departure)

  const dispatch = useAppDispatch()
  const { t } = useTranslation()

  const { data, isFetching } = useQuery({
    queryKey: ['reason-late-group-task', paramsLateGroupTask],
    queryFn: async () => {
      const data = await getLateGroupTask(
        selectedFlightId as string,
        selectedPTSMasterId as string,
        paramsLateGroupTask
      )

      setTableData(data.items)

      return data
    },
    enabled:
      !!selectedFlightId && !!selectedPTSMasterId && visibleModalReasonLate,
  })

  const { data: flightDetail } = useQuery({
    queryKey: ['flight-detail', selectedFlightId],
    queryFn: () => selectedFlightId && getFlightDetail(selectedFlightId),
    enabled: !!selectedFlightId && visibleModalReasonLate,
  })

  const columns: TableProps<any>['columns'] = [
    {
      key: 'index',
      title: t('table.order'),
      width: '5%',
      dataIndex: 'key',
      align: 'center',
      render: (_value, _record, index) => index + 1,
    },
    {
      key: 'groupPtsTaskName',
      title: t('table.ptsGroupTask'),
      width: '40%',
      dataIndex: 'groupPtsTaskName',
    },
    {
      key: 'lateDuration',
      title: t('table.totalTimeDelay'),
      width: '20%',
      dataIndex: 'lateDuration',
      align: 'center',
      render: time => <>{convertTime(time)}</>,
    },
    {
      key: 'choose',
      title: t('table.choose'),
      width: '8%',
      align: 'center',
      render: (record: any) => (
        <Checkbox
          checked={record.isDelay}
          onChange={e => {
            const newData = tableData.map(item =>
              item.groupPtsTaskId === record.groupPtsTaskId
                ? { ...item, isDelay: e.target.checked }
                : item
            )
            setTableData(newData)
          }}
        />
      ),
    },
  ]

  const mutationSubmit = useMutation({
    mutationFn: async (values: any) => {
      return (
        selectedFlightId &&
        selectedPTSMasterId &&
        updateReasonLateGroupTask(selectedFlightId, selectedPTSMasterId, values)
      )
    },
    onSuccess: () => {
      dispatch(closeModalReasonLate())
      dispatch(setParamsGroupTask({ skipCount: 0, maxResultCount: 10 }))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const onSubmit = () => {
    mutationSubmit.mutate(tableData)
  }

  useEffect(() => {
    dispatch(setSelectedPTSMasterId(flightDetail?.ptsId))
  }, [selectedFlightId, visibleModalReasonLate])

  return (
    <Modal
      title={`${t('departure.reasonLateGT')}:`}
      width={1000}
      open={visibleModalReasonLate}
      onCancel={() => {
        setTableData([])
        dispatch(closeModalReasonLate())
        dispatch(setParamsGroupTask({ skipCount: 0, maxResultCount: 10 }))
      }}
      closable={false}
      onOk={onSubmit}
      okText={t('common.confirm')}
      cancelText={t('common.cancel')}
      confirmLoading={mutationSubmit.isPending}
    >
      <Table
        columns={columns}
        rowKey={record => record.groupPtsTaskId}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        dataSource={tableData}
        loading={isFetching}
        pagination={{
          pageSize: paramsLateGroupTask.maxResultCount,
          current:
            paramsLateGroupTask.skipCount / paramsLateGroupTask.maxResultCount +
            1,
          total: data?.totalCount,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            dispatch(
              setParamsGroupTask({
                ...paramsLateGroupTask,
                skipCount: (page - 1) * pageSize,
                maxResultCount: pageSize,
              })
            )
          },
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
    </Modal>
  )
}

export default ModalLateGroupTask
