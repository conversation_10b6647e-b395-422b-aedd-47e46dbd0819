import { EditFilled } from '@ant-design/icons'
import { Button, Col, Form, Input, Row } from 'antd'

const TabMealOrder = () => {
  return (
    <div className="w-full py-3 bg-[#F5F9FA]">
      <Form colon={false} labelAlign="left" labelCol={{ flex: '120px' }}>
        <div className="flex w-full justify-between">
          <div className="text-lg font-bold">Estimation</div>
          <Button
            icon={<EditFilled />}
            type="primary"
            onClick={() => {}}
            className="!hidden"
          >
            Chỉnh sửa
          </Button>
        </div>
        <div className="bg-[#E6F0F3] p-3 my-3 rounded-sm">
          <Row className="gap-x-8">
            <Col>
              <Row
                align="middle"
                className="items-center space-x-2 flex gap-x-4"
              >
                <div className="text-base font-semibold">
                  Est Meal (J.W.Y.F)
                </div>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">J</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">W</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">Y</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">F</span>
                  </div>
                </Form.Item>
              </Row>
            </Col>
            <Col>
              <Row
                align="middle"
                className="items-center space-x-2 flex gap-x-4"
              >
                <div className="text-base font-semibold">Est Crew Meal</div>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">J</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">W</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">Y</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">F</span>
                  </div>
                </Form.Item>
              </Row>
            </Col>
          </Row>
          <Col span={24} className="mt-6">
            <Form.Item label="Est special meals">
              <Input />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="Remark">
              <Input />
            </Form.Item>
          </Col>
        </div>
        <div className="text-lg font-bold">Actual</div>

        <div className="bg-[#E6F0F3] p-3 my-3 rounded-sm">
          <Row className="gap-x-8">
            <Col>
              <Row
                align="middle"
                className="items-center space-x-2 flex gap-x-4"
              >
                <div className="text-base font-semibold">
                  Act Meal (J.W.Y.F)
                </div>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">J</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">W</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">Y</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">F</span>
                  </div>
                </Form.Item>
              </Row>
            </Col>
            <Col>
              <Row
                align="middle"
                className="items-center space-x-2 flex gap-x-4"
              >
                <div className="text-base font-semibold">Act Crew Meal</div>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">J</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">W</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">Y</span>
                  </div>
                </Form.Item>
                <Form.Item className="!mb-0 w-24">
                  <div className="flex items-center">
                    <Input />
                    <span className="ml-1">F</span>
                  </div>
                </Form.Item>
              </Row>
            </Col>
          </Row>
          <Col span={24} className="mt-6">
            <Form.Item label="Act special meals">
              <Input />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="Remark">
              <Input />
            </Form.Item>
          </Col>
        </div>
      </Form>
    </div>
  )
}

export default TabMealOrder
