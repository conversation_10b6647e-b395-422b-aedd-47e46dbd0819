import { createSlice } from '@reduxjs/toolkit'

type UserState = {
  visibleUserModal: boolean
  visiblePopoverFilter: boolean
  selectedUser: string
  tabActive: string
  visibleChangePasswordModal: boolean
  visibleDeleteUserModal: boolean
  params: {
    SkipCount: number
    MaxResultCount: number
    Filter?: string
  }
  KeyWordDebounce: string
}

const initialState: UserState = {
  visibleUserModal: false,
  tabActive: 'user-info',
  selectedUser: '',
  visiblePopoverFilter: false,
  visibleChangePasswordModal: false,
  visibleDeleteUserModal: false,
  params: {
    SkipCount: 0,
    MaxResultCount: 20,
    Filter: '',
  },
  KeyWordDebounce: '',
}

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    openUserModal(state) {
      state.visibleUserModal = true
    },
    closeUserModal(state) {
      state.visibleUserModal = false
    },
    openPopoverFilter(state) {
      state.visiblePopoverFilter = true
    },
    closePopoverFilter(state) {
      state.visiblePopoverFilter = false
    },
    setParams(state, action) {
      state.params = { ...state.params, ...action.payload }
    },
    setSelectedUser(state, action) {
      state.selectedUser = action.payload
    },
    setTabActive(state, action) {
      state.tabActive = action.payload
    },
    openChangePasswordModal(state) {
      state.visibleChangePasswordModal = true
    },
    closeChangePasswordModal(state) {
      state.visibleChangePasswordModal = false
    },
    openDeleteUserModal(state) {
      state.visibleDeleteUserModal = true
    },
    closeDeleteUserModal(state) {
      state.visibleDeleteUserModal = false
    },
    setKeyWordDebounce(state, action) {
      state.KeyWordDebounce = action.payload
    },
    resetState() {
      return initialState
    },
  },
})

export const {
  openUserModal,
  closeUserModal,
  setParams,
  openPopoverFilter,
  closePopoverFilter,
  setSelectedUser,
  setTabActive,
  openChangePasswordModal,
  closeChangePasswordModal,
  openDeleteUserModal,
  closeDeleteUserModal,
  setKeyWordDebounce,
  resetState,
} = userSlice.actions
export default userSlice.reducer
