/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `api/v1/departments`

const getDepartments = async (params?: any) => {
  const api = `${prefix}`
  const response = await axios.get(api, { params })
  return response.data
}

const getDepartmentDetail = async (id: string) => {
  const api = `${prefix}/${id}`
  const response = await axios.get(api)
  return response.data
}

const createDepartment = async (body: any) => {
  const api = `${prefix}`
  const response = await axios.post(api, body)
  return response.data
}

const updateDepartment = async (body: any) => {
  const api = `${prefix}/${body.id}`
  const response = await axios.put(api, body)
  return response.data
}

const removeDepartment = async (id: string) => {
  const api = `${prefix}/${id}`
  const response = await axios.delete(api)
  return response.data
}

const getDepartmentChildren = async (id: string) => {
  const api = `${prefix}/${id}/children`
  const response = await axios.get(api)
  return response.data
}

const getUsersInDepartment = async (id: string) => {
  const api = `${prefix}/${id}/users`
  const response = await axios.get(api)
  return response.data
}

const assignUserToDepartment = async (id: string, body: any) => {
  const api = `${prefix}/${id}/users`
  const response = await axios.post(api, body)
  return response.data
}

const getUserRoleInDepartment = async (id: string, userId: string) => {
  const api = `${prefix}/${id}/users/${userId}`
  const response = await axios.get(api)
  return response.data
}

const updateUserRoleInDepartment = async (
  id: string,
  userId: string,
  body: any
) => {
  const api = `${prefix}/${id}/users/${userId}`
  const response = await axios.put(api, body)
  return response.data
}

const removeUserFromDepartment = async (id: string, userId: string) => {
  const api = `${prefix}/${id}/users/${userId}`
  const response = await axios.delete(api)
  return response.data
}

export {
  getDepartments,
  getDepartmentDetail,
  createDepartment,
  updateDepartment,
  removeDepartment,
  getDepartmentChildren,
  getUsersInDepartment,
  assignUserToDepartment,
  getUserRoleInDepartment,
  updateUserRoleInDepartment,
  removeUserFromDepartment,
}
