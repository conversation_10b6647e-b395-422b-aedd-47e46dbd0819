/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  UNUSUAL_INFO_GROUND_SERVICE,
  UPLOAD_UNUSUAL_INFO,
} from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { normFile } from '@/src/helper/normFile'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getUnusualCodes } from '@/src/service/unusualCodes'
import {
  createUnusualInfo,
  getUnusualInfoDetail,
  getUnusualInfoList,
  updateUnusualInfo,
} from '@/src/service/unusualInfo'
import { downloadFile, uploadFile } from '@/src/service/upload'
import {
  closeModalGroundService,
  setSelectedUnusualInfoId,
} from '@/src/store/ModalArrivalFlightSlice'
import { UploadOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Card, Form, Input, Modal, Select, Upload } from 'antd'
import FileSaver from 'file-saver'
import { useEffect } from 'react'

const ModalGroundService = () => {
  const [form] = Form.useForm()
  const {
    visibleModalGroundService,
    selectedUnusualInfoId,
    selectFlightModalId,
  } = useAppSelector(state => state.modalArrivalFlight)

  const { data: unusualInfoDetail, isFetching } = useQuery({
    queryKey: [
      'unusual-info-detail',
      selectFlightModalId,
      selectedUnusualInfoId,
    ],
    queryFn: () =>
      getUnusualInfoDetail(
        selectFlightModalId as string,
        selectedUnusualInfoId as string
      ),
    enabled:
      !!selectedUnusualInfoId &&
      !!selectFlightModalId &&
      !!visibleModalGroundService,
  })

  const { data: unusualCodes } = useQuery({
    queryKey: ['unusual-codes'],
    queryFn: () => getUnusualCodes(),
  })

  const mutation = useMutation({
    mutationFn: (values: any) => {
      return selectedUnusualInfoId && selectFlightModalId
        ? updateUnusualInfo(
            selectFlightModalId as string,
            selectedUnusualInfoId as string,
            values
          )
        : createUnusualInfo(selectFlightModalId as string, values)
    },
    onSuccess() {
      refetch()
      dispatch(setSelectedUnusualInfoId(null))
      dispatch(closeModalGroundService())
      form.resetFields()
    },
    onError(error) {
      handleApiError(error)
    },
  })

  const mutationUploadFile = useMutation({
    mutationFn: async (values: any) => {
      return uploadFile(values)
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationGetFile = useMutation({
    mutationFn: async (values: any) => {
      return downloadFile(values)
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const { refetch } = useQuery({
    queryKey: ['unusual-info-list', selectFlightModalId],
    queryFn: () => getUnusualInfoList(selectFlightModalId as string),
    enabled: !!selectFlightModalId,
  })
  const dispatch = useAppDispatch()

  const onSubmit = async (values: any) => {
    await form.validateFields()
    let fileName = ''
    let filePath = ''

    if (values.file?.length > 0) {
      const file = await mutationUploadFile.mutateAsync({
        file: values.file[0].originFileObj,
        fileName: values.file[0].name,
        flightId: selectFlightModalId,
        type: UPLOAD_UNUSUAL_INFO,
      })
      fileName = values.file[0].name
      filePath = file?.data?.filePath || file?.filePath || ''
    }
    await mutation.mutateAsync({
      ...values,
      type: UNUSUAL_INFO_GROUND_SERVICE,
      fileName,
      filePath,
    })
  }

  useEffect(() => {
    const loadFormData = async () => {
      if (!unusualInfoDetail || !visibleModalGroundService) return

      const hasFileData =
        unusualInfoDetail.filePath || unusualInfoDetail.fileName

      if (hasFileData) {
        try {
          const fileResponse = await mutationGetFile.mutateAsync({
            filePath: unusualInfoDetail.filePath,
            fileName: unusualInfoDetail.fileName,
          })
          const fileObject = {
            uid: `file-${Date.now()}`,
            name: unusualInfoDetail.fileName || 'Downloaded file',
            status: 'done',
            originFileObj: fileResponse, // Store the blob as originFileObj
            url: URL.createObjectURL(fileResponse), // Create object URL for preview
            response: fileResponse,
          }
          form.setFieldsValue({ ...unusualInfoDetail, file: [fileObject] })
        } catch (error) {
          console.error('Error loading file data:', error)
          form.setFieldsValue(unusualInfoDetail)
        }
      } else {
        form.setFieldsValue(unusualInfoDetail)
      }
    }

    loadFormData()
  }, [unusualInfoDetail, visibleModalGroundService])

  return (
    <Modal
      width="50%"
      title={
        unusualInfoDetail
          ? 'Update Unusual information'
          : 'Create Unusual information'
      }
      open={visibleModalGroundService}
      onCancel={() => {
        dispatch(closeModalGroundService())
        dispatch(setSelectedUnusualInfoId(null))
        form.resetFields()
      }}
      footer={null}
    >
      <Card size="small" variant="borderless" loading={isFetching}>
        <Form
          form={form}
          labelCol={{ flex: '120px' }}
          labelAlign="left"
          onFinish={onSubmit}
        >
          <Form.Item label="Code" name="code" rules={[{ required: true }]}>
            <Select
              options={unusualCodes?.map((code: any) => ({
                label: `${code.name} - ${code.displayName}`,
                value: String(code.value),
              }))}
            />
          </Form.Item>
          <Form.Item label="Description" name="description">
            <Input.TextArea rows={1} />
          </Form.Item>
          <Form.Item label="Solution" name="solution">
            <Input.TextArea rows={1} />
          </Form.Item>
          <Form.Item label="Result" name="result">
            <Input.TextArea rows={1} />
          </Form.Item>
          <Form.Item label="Remark" name="remark">
            <Input.TextArea rows={1} />
          </Form.Item>
          <Form.Item
            name="file"
            label="File"
            valuePropName="fileList"
            getValueFromEvent={normFile}
            extra="Support format"
          >
            <Upload
              maxCount={1}
              onPreview={async () => {
                const fileResponse = await mutationGetFile.mutateAsync({
                  filePath: unusualInfoDetail.filePath,
                  fileName: unusualInfoDetail.fileName,
                })
                return FileSaver.saveAs(
                  fileResponse,
                  unusualInfoDetail.fileName
                )
              }}
            >
              <Button icon={<UploadOutlined />}>Browser file</Button>
            </Upload>
          </Form.Item>
          <Form.Item className="flex justify-end">
            <Button
              htmlType="submit"
              type="primary"
              loading={mutation.isPending}
            >
              Save
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </Modal>
  )
}

export default ModalGroundService
