/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'
//Widget chuyến bay
const getDashboardFlight = async (params: any) => {
  const api = `/api/v1/flights/gt/count`
  const res = await axios.get(api, {
    params,
  })
  return res.data
}

const getDashboardFlightSummary = async (params: any) => {
  const api = `/api/v1/flights/summary`
  const res = await axios.get(api, {
    params,
  })
  return res
}
//line chart
const getDashboardOrganizationUnitLineChart = async (params: any) => {
  const api = `/api/v1/flights/summary/organization-unit`
  const res = await axios.get(api, {
    params,
  })
  return res.data
}
//widget  mốc việc chậm
const getDashboardTaskPTSLate = async (params: any) => {
  const api = `/api/v1/pts-tasks/count`
  const res = await axios.get(api, {
    params,
  })
  return res.data
}
//widget trễ OTP, OSP
const getDashboardGroupTaskPTSLateOTP = async (params: any) => {
  const api = `/api/v1/group-pts-tasks/count`
  const res = await axios.get(api, {
    params,
  })
  return res.data
}

export {
  getDashboardFlight,
  getDashboardFlightSummary,
  getDashboardOrganizationUnitLineChart,
  getDashboardTaskPTSLate,
  getDashboardGroupTaskPTSLateOTP,
}
