/* eslint-disable react-hooks/exhaustive-deps */
import { TIME_DEBOUNCE } from '@/src/constants'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import AirportUserModal from '@/src/pages/Airport/AiportUserModal'
import UserModal from '@/src/pages/Airport/UserModal'
import type { IAirport } from '@/src/schema/IAirport'
import { getAirport } from '@/src/service/airport'
import {
  openAirportUserModal,
  resetState,
  setFilterDebounce,
  setParams,
  setSelectedAirportId,
} from '@/src/store/AirportSlice'
import { SearchOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { Button, Input, Space, Table, type TableProps } from 'antd'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import ShowTotal from '@/src/components/Showtotal'
import { LAO_AIRPORTS_VIEW_AIRPORT_USERS } from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'

const AirportPage = () => {
  const { t } = useTranslation()
  const { hasPermission } = usePermission()
  const { params, filterDebounce } = useAppSelector(state => state.airport)

  const dispatch = useAppDispatch()

  const FilterDebounce = useDebounce(filterDebounce, TIME_DEBOUNCE)

  const { data, isLoading } = useQuery({
    queryKey: ['airport-list', params],
    queryFn: () => getAirport(params),
  })

  const columns: TableProps<IAirport>['columns'] = [
    {
      title: t('airport.iataCode'),
      dataIndex: 'iataCode',
      key: 'iataCode',
      align: 'center',
    },
    {
      title: t('airport.icaoCode'),
      dataIndex: 'icaoCode',
      key: 'icaoCode',
      align: 'center',
    },
    {
      title: t('airport.apName'),
      dataIndex: 'apName',
      key: 'apName',
    },
    {
      title: t('airport.countryCode'),
      dataIndex: 'countryCode',
      key: 'countryCode',
      align: 'center',
    },
    {
      title: t('table.action'),
      key: 'action',
      align: 'center',
      width: 100,
      hidden: !hasPermission(LAO_AIRPORTS_VIEW_AIRPORT_USERS),
      render: (record: IAirport) => (
        <Space>
          <Button
            type="primary"
            onClick={() => {
              dispatch(setSelectedAirportId(record.id))
              dispatch(openAirportUserModal())
            }}
          >
            {t('airport.employee')}
          </Button>
        </Space>
      ),
    },
  ]

  useEffect(() => {
    dispatch(setParams({ ...params, SkipCount: 0, Filter: FilterDebounce }))
  }, [FilterDebounce])

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex  w-full justify-between">
        <div className="text-lg font-bold text-black">
          {t('airport.airport_list')}
        </div>
        <Input
          prefix={<SearchOutlined />}
          className="!w-44"
          placeholder={t('airport.searchPlaceholder')}
          onChange={e => {
            dispatch(setFilterDebounce(e.target.value))
          }}
        />
      </div>
      <Table
        bordered
        loading={isLoading}
        columns={columns}
        dataSource={isLoading ? [] : data.items}
        className={styles.whiteHeader}
        size="small"
        pagination={{
          total: isLoading ? 0 : data.totalCount,
          current: params.SkipCount / params.MaxResultCount + 1,
          pageSize: params.MaxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                ...params,
                SkipCount: (page - 1) * pageSize,
                MaxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        rowKey={record => record.id}
      />
      <AirportUserModal />
      <UserModal />
    </div>
  )
}

export default AirportPage
