/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'
const prefix = `api/permission-management/permissions`

const getPermission = async (
  providerName: string,
  providerKey: string,
  culture?: string
) => {
  const api = `${prefix}`
  const response = await axios.get(api, {
    params: {
      providerName,
      providerKey,
      culture,
    },
  })
  return response.data
}

const updatePermission = async (
  providerName: string,
  providerKey: string,
  body: any
) => {
  const api = `${prefix}?providerName=${providerName}&providerKey=${providerKey}`

  const response = await axios.put(api, body)
  return response.data
}

const getCurrentUser = async () => {
  const api = 'api/v1/permissions/current-user'
  const response = await axios.get(api)
  return response.data
}

export { getPermission, updatePermission, getCurrentUser }
