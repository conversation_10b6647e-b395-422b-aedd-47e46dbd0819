/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1`

const getHistory = async (flightId: string, params?: any) => {
  const api = `${prefix}/flights/${flightId}/history`
  const response = await axios.get(api, { params })
  return response.data
}

const getHistoryType = async () => {
  const api = `${prefix}/flight-history-types`
  const response = await axios.get(api)
  return response.data
}

export { getHistory, getHistoryType }
