{"name": "fm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 4200", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier . --check", "prepare": "husky"}, "dependencies": {"@ant-design/charts": "^2.6.0", "@ant-design/icons": "5.x", "@daypilot/daypilot-lite-react": "^4.1.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.76.1", "@types/file-saver": "^2.0.7", "@types/qs": "^6.14.0", "antd": "^5.25.1", "axios": "^1.9.0", "axios-retry": "^4.5.0", "cookiejs": "^2.1.3", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "husky": "^9.1.7", "i18next": "^25.2.1", "lodash": "^4.17.21", "moment": "^2.30.1", "qs": "^6.14.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-redux": "^9.2.0", "react-router": "^7.6.0", "slugify": "^1.6.6", "tailwindcss": "^4.1.7", "usehooks-ts": "^3.1.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/lodash": "^4.17.17", "@types/node": "^22.15.19", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "lint-staged": "^16.1.2", "prettier": "^3.5.3", "sass-embedded": "^1.89.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}