import { EditFilled } from '@ant-design/icons'
import { Button, Form, Input } from 'antd'

const TabACTechInfo = () => {
  return (
    <div className="w-full pt-3 bg-[#F5F9FA]">
      <div className="flex w-full justify-between">
        <div className="text-lg font-bold">AC Tech Info</div>
        <Button
          className="!hidden"
          icon={<EditFilled />}
          type="primary"
          onClick={() => {}}
        >
          Chỉnh sửa
        </Button>
      </div>
      <div className="bg-[#E6F0F3] rounded-md p-3 flex flex-row mt-3">
        <Form className="flex flex-row w-full gap-x-8" colon={false}>
          <Form.Item label="A/C Status">
            <Input />
          </Form.Item>
          <Form.Item label="Cabin Status">
            <Input />
          </Form.Item>
          <Form.Item label="PSC">
            <Input />
          </Form.Item>
          <Form.Item className="w-full" label="Special meals">
            <Input />
          </Form.Item>
        </Form>
      </div>
    </div>
  )
}

export default TabACTechInfo
