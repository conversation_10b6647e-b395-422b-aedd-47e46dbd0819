import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

interface PermissionState {
  permissions: {
    [parentKey: string]: {
      [childKey: string]: boolean
    }
  }
  allPermissions: boolean
  expandedKeys: string[]
  isLoading: boolean
  page: number
  pageSize: number
  total: number
  visibleModalFunction: boolean
  visibleModalViewColumn: boolean
}

const initialState: PermissionState = {
  permissions: {
    lich_mua_bay: {
      create: false,
      update: false,
      delete: false,
      sync: false,
    },
    chuyen_bay_tong_hop: {
      create: false,
      update: false,
      delete: false,
      sync: false,
    },
  },
  allPermissions: false,
  expandedKeys: [],
  isLoading: false,
  page: 1,
  pageSize: 10,
  total: 100,
  visibleModalFunction: false,
  visibleModalViewColumn: false,
}

const permissionSlice = createSlice({
  name: 'permission',
  initialState,
  reducers: {
    togglePermission(
      state,
      action: PayloadAction<{ parentKey: string; childKey: string }>
    ) {
      const { parentKey, child<PERSON>ey } = action.payload
      if (state.permissions[parentKey]) {
        state.permissions[parentKey][childKey] =
          !state.permissions[parentKey][childKey]
      }
    },

    toggleAllChildPermissions(
      state,
      action: PayloadAction<{ parentKey: string; value: boolean }>
    ) {
      const { parentKey, value } = action.payload

      if (state.permissions[parentKey]) {
        const updatedPermissions: Record<string, boolean> = {}

        for (const childKey in state.permissions[parentKey]) {
          updatedPermissions[childKey] = value
        }

        state.permissions[parentKey] = updatedPermissions
      }
    },

    toggleAllPermissions(state, action: PayloadAction<boolean>) {
      state.allPermissions = action.payload
      for (const parentKey in state.permissions) {
        for (const childKey in state.permissions[parentKey]) {
          state.permissions[parentKey][childKey] = action.payload
        }
      }
    },

    setExpandedKeys(state, action: PayloadAction<string[]>) {
      state.expandedKeys = action.payload
    },

    toggleExpandKey(state, action: PayloadAction<string>) {
      const index = state.expandedKeys.indexOf(action.payload)
      if (index > -1) {
        state.expandedKeys.splice(index, 1)
      } else {
        state.expandedKeys.push(action.payload)
      }
    },

    setPage(state, action: PayloadAction<number>) {
      state.page = action.payload
    },

    setPageSize(state, action: PayloadAction<number>) {
      state.pageSize = action.payload
    },

    openModalFunction(state) {
      state.visibleModalFunction = true
    },

    closeModalFunction(state) {
      state.visibleModalFunction = false
    },

    openModalViewColumn(state) {
      state.visibleModalViewColumn = true
    },

    closeModalViewColumn(state) {
      state.visibleModalViewColumn = false
    },
  },
})

export const {
  togglePermission,
  toggleAllChildPermissions,
  toggleAllPermissions,
  setExpandedKeys,
  toggleExpandKey,
  setPage,
  setPageSize,
  openModalFunction,
  closeModalFunction,
  openModalViewColumn,
  closeModalViewColumn,
} = permissionSlice.actions
export default permissionSlice.reducer
