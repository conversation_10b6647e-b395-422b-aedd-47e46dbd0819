/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import type { IFleet } from '@/src/schema/IFleet'
import {
  createFleet,
  getFleet,
  removeFleet,
  updateFleet,
} from '@/src/service/fleet'
import {
  closeFleetModal,
  openFleetModal,
  resetState,
  setFilterDebounce,
  setParams,
  setSelectedFleetId,
} from '@/src/store/FleetSlice'
import {
  DeleteFilled,
  EditFilled,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Button, Form, Input, message, Modal, Space, Table } from 'antd'
import type { TableProps } from 'antd/lib'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import useDebounce from '@/src/hooks/useDebounce'
import { TIME_DEBOUNCE } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import ShowTotal from '@/src/components/Showtotal'
import {
  LAO_FLEETS_CREATE,
  LAO_FLEETS_DELETE,
  LAO_FLEETS_EDIT,
} from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'

const FleetPage = () => {
  const { t } = useTranslation()
  const { hasPermission } = usePermission()
  const dispatch = useAppDispatch()

  const [form] = Form.useForm()

  const { visibleFleetModal, selectedFleetId, filterDebounce, params } =
    useAppSelector(state => state.fleet)

  const FilterDebounce = useDebounce(filterDebounce, TIME_DEBOUNCE)

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['fleet-list', params],
    queryFn: () => getFleet(params),
  })

  const columns: TableProps<IFleet>['columns'] = [
    {
      title: t('fleet.code'),
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: t('fleet.description'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('table.action'),
      key: 'action',
      align: 'center',
      width: 100,
      hidden:
        !hasPermission(LAO_FLEETS_EDIT) && !hasPermission(LAO_FLEETS_DELETE),
      render: (record: IFleet) => (
        <Space>
          {hasPermission(LAO_FLEETS_EDIT) && (
            <EditFilled
              className="hover:bg-slate-200 p-1 rounded-sm"
              onClick={() => {
                dispatch(setSelectedFleetId(record.id))
                dispatch(openFleetModal())
                form.setFieldsValue(record)
              }}
            />
          )}
          {hasPermission(LAO_FLEETS_DELETE) && (
            <DeleteFilled
              className="hover:bg-slate-200 p-1 rounded-sm"
              onClick={() => {
                Modal.confirm({
                  title: t('common.confirmDeleteTitle'),
                  content: t('common.confirmDeleteMessage'),
                  okText: t('common.yes'),
                  cancelText: t('common.no'),
                  onOk: () => mutationDelete.mutate(record.id),
                })
              }}
            />
          )}
        </Space>
      ),
    },
  ]

  const mutation = useMutation({
    mutationFn: async (values: IFleet) => {
      return selectedFleetId ? updateFleet(values) : createFleet(values)
    },
    onSuccess: () => {
      refetch()
      dispatch(closeFleetModal())
      dispatch(setSelectedFleetId(''))
      message.success(
        selectedFleetId ? t('fleet.updateSuccess') : t('fleet.createSuccess')
      )
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const mutationDelete = useMutation({
    mutationFn: async (id: string) => removeFleet(id),
    onSuccess: () => {
      message.success(t('fleet.deleteSuccess'))
      refetch()
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const onSubmit = async () => {
    const values = await form.validateFields()
    const data = {
      ...values,
      id: selectedFleetId ?? '',
    }
    mutation.mutate(data)
  }

  useEffect(() => {
    dispatch(setParams({ ...params, SkipCount: 0, Filter: FilterDebounce }))
  }, [FilterDebounce])

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex flex-row w-full justify-between">
        <div className="text-lg font-bold text-black">{t('fleet.list')}</div>
        <div className="flex flex-row items-center gap-x-4">
          <Input
            prefix={<SearchOutlined />}
            className="!w-44"
            placeholder={t('fleet.searchPlaceholder')}
            onChange={e => {
              dispatch(setFilterDebounce(e.target.value))
            }}
          />
          {hasPermission(LAO_FLEETS_CREATE) && (
            <Button
              icon={<PlusOutlined />}
              onClick={() => {
                form.resetFields()
                dispatch(setSelectedFleetId(''))
                dispatch(openFleetModal())
              }}
              type="primary"
            >
              {t('fleet.add')}
            </Button>
          )}
        </div>
      </div>
      <Table
        loading={isLoading}
        dataSource={data?.items ?? []}
        columns={columns}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        pagination={{
          total: data?.totalCount ?? 0,
          current: params.SkipCount / params.MaxResultCount + 1,
          pageSize: params.MaxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                ...params,
                SkipCount: (page - 1) * pageSize,
                MaxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        rowKey={record => record.id}
      />
      <Modal
        open={visibleFleetModal}
        onCancel={() => dispatch(closeFleetModal())}
        title={selectedFleetId ? t('fleet.edit') : t('fleet.add')}
        onOk={onSubmit}
      >
        <Form form={form} labelCol={{ flex: '100px' }}>
          <Form.Item
            name="code"
            label={t('fleet.code')}
            rules={[{ required: true, message: t('fleet.codeRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label={t('fleet.description')}
            rules={[
              {
                required: true,
                message: t('fleet.descriptionRequired'),
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default FleetPage
