/* eslint-disable @typescript-eslint/no-explicit-any */
import type { IFlightParamsType } from '@/src/schema/IFlightType'
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

type DepartureState = {
  visibleDropdownViewColumn: boolean
  keyWord: string | undefined
  acSubType: string | undefined
  acRegistration: string | undefined
  params: IFlightParamsType
  selectedFlightId: string | null
  selectedPTSMasterId: string | null
  visibleUpdatePTSModal: boolean
  ptsUpdateId: string | null
  visibleModalSuitablePTS: boolean
  selectedSuitablePTSIds: string
  selectedFlight: any
  selectedPtsId: string | null
  activeCollapseKey: any
  reset: boolean
  visibleFilterModal: boolean
  sort: string
  visibleGantt: boolean
  checkListFlight: any[]
  visibleModalReasonLate: boolean
  paramsLateGroupTask: any
  flightName: string
  preData: any
  changes: any
}

const initialState: DepartureState = {
  visibleDropdownViewColumn: false,
  keyWord: undefined,
  acSubType: undefined,
  acRegistration: undefined,
  params: {
    skipCount: 0,
    maxResultCount: 1000,
  },
  selectedFlightId: null,
  selectedPTSMasterId: null,
  visibleUpdatePTSModal: false,
  ptsUpdateId: null,
  visibleModalSuitablePTS: false,
  selectedSuitablePTSIds: '',
  selectedFlight: null,
  selectedPtsId: null,
  activeCollapseKey: null,
  reset: false,
  visibleFilterModal: false,
  sort: 'asc',
  visibleGantt: false,
  checkListFlight: [],
  visibleModalReasonLate: false,
  paramsLateGroupTask: {
    skipCount: 0,
    maxResultCount: 20,
  },
  flightName: '',
  preData: null,
  changes: null,
}

const departureSlice = createSlice({
  name: 'departure',
  initialState,
  reducers: {
    setParams: (state, action) => {
      state.params = action.payload
    },
    setSearch: (
      state,
      action: { payload: { key: keyof DepartureState; value: string } }
    ) => {
      const { key, value } = action.payload
      if (key in state) {
        ;(state[key] as string) = value
      }
    },
    setSelectedFlightId: (state, action) => {
      state.selectedFlightId = action.payload
    },
    setSelectedPTSMasterId: (state, action) => {
      state.selectedPTSMasterId = action.payload
    },
    resetState() {
      return {
        ...initialState,
        params: {
          ...initialState.params,
        },
      }
    },
    openUpdatePTSModal: state => {
      state.visibleUpdatePTSModal = true
    },
    closeUpdatePTSModal: state => {
      state.visibleUpdatePTSModal = false
    },
    setPtsUpdateId: (state, action) => {
      state.ptsUpdateId = action.payload
    },
    openModalSuitablePTS: state => {
      state.visibleModalSuitablePTS = true
    },
    closeModalSuitablePTS: state => {
      state.visibleModalSuitablePTS = false
    },
    setSelectedSuitablePTSIds: (state, action) => {
      state.selectedSuitablePTSIds = action.payload
    },
    setSelectedFlight: (state, action) => {
      state.selectedFlight = action.payload
    },
    setSelectedPtsId: (state, action) => {
      state.selectedPtsId = action.payload
    },
    setActiveCollapseKey: (state, action: { payload: string | null }) => {
      state.activeCollapseKey = action.payload
    },
    canReset: state => {
      state.reset = true
    },
    cannotReset: state => {
      state.reset = false
    },
    openFilterModal: state => {
      state.visibleFilterModal = true
    },
    closeFilterModal: state => {
      state.visibleFilterModal = false
    },
    setSort(state, action: PayloadAction<string>) {
      state.sort = action.payload
    },
    openGantt: state => {
      state.visibleGantt = true
    },
    closeGantt: state => {
      state.visibleGantt = false
    },
    closeDropdownViewColumn: state => {
      state.visibleDropdownViewColumn = false
    },
    openDropdownViewColumn: state => {
      state.visibleDropdownViewColumn = true
    },
    setCheckListFlight(state, action: PayloadAction<any[]>) {
      state.checkListFlight = action.payload
    },
    openModalReasonLate: state => {
      state.visibleModalReasonLate = true
    },
    closeModalReasonLate: state => {
      state.visibleModalReasonLate = false
    },
    setParamsGroupTask(state, action: PayloadAction<any>) {
      state.paramsLateGroupTask = action.payload
    },
    setFlightName(state, action: PayloadAction<string>) {
      state.flightName = action.payload
    },
    setPreData(state, action: PayloadAction<any>) {
      state.preData = action.payload
    },
    setChanges(state, action: PayloadAction<any>) {
      state.changes = action.payload
    },
  },
})

export const {
  setParams,
  setSearch,
  setSelectedFlightId,
  setSelectedPTSMasterId,
  resetState,
  openUpdatePTSModal,
  closeUpdatePTSModal,
  setPtsUpdateId,
  openModalSuitablePTS,
  closeModalSuitablePTS,
  setSelectedSuitablePTSIds,
  setSelectedFlight,
  setSelectedPtsId,
  setActiveCollapseKey,
  canReset,
  cannotReset,
  openFilterModal,
  closeFilterModal,
  setSort,
  openGantt,
  closeGantt,
  closeDropdownViewColumn,
  openDropdownViewColumn,
  setCheckListFlight,
  openModalReasonLate,
  closeModalReasonLate,
  setParamsGroupTask,
  setFlightName,
  setPreData,
  setChanges,
} = departureSlice.actions
export default departureSlice.reducer
