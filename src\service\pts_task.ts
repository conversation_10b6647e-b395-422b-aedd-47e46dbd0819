/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'
import type { IPTSTask } from '@/src/schema/IPTSTask'

const prefix = `/api/v1/pts-tasks`

const getPTSTask = async (params: any, language?: any) => {
  const api = `${prefix}`
  const response = await axios.get(api, {
    params,
    headers: { 'Accept-Language': language === 'vi' ? 'vi-VN' : language },
  })
  return response.data
}

const createPTSTask = async (body: IPTSTask) => {
  const api = `${prefix}`

  const response = await axios.post(api, body)
  return response.data
}

const updatePTSTask = async (body: IPTSTask) => {
  const api = `${prefix}/${body.id}`

  const response = await axios.put(api, body)
  return response.data
}

const removePTSTask = async (id: string) => {
  const api = `${prefix}/${id}`

  const response = await axios.delete(api)
  return response.data
}

export { getPTSTask, createPTSTask, updatePTSTask, removePTSTask }
