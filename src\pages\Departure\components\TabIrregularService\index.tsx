import { EditFilled } from '@ant-design/icons'
import { Button, Col, Form, Input, Row } from 'antd'

const TabIrregularService = () => {
  return (
    <div className="w-full py-3 bg-[#F5F9FA] gap-y-4 flex flex-col">
      <div className="flex w-full justify-between mb-3">
        <div className="flex w-full justify-between">
          <div className="text-lg font-bold">Irregular Service</div>
          <Button
            icon={<EditFilled />}
            type="primary"
            onClick={() => {}}
            className="!hidden"
          >
            Chỉnh sửa
          </Button>
        </div>
      </div>
      <Form layout="vertical">
        <div className="p-3 rounded-sm">
          <div className="flex flex-col gap-x-8 mt-3">
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="BRKF" name="BRKF">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="MEAL" name="MEAL">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="BEV" name="BEV">
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="TAXI" name="TAXI">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="HOTEL" name="HOTEL">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="OFFLD" name="OFFLD">
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="DNG" name="DNG">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="IFE" name="IFE">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="FIM" name="FIM">
                  <Input />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </div>
      </Form>
    </div>
  )
}

export default TabIrregularService
