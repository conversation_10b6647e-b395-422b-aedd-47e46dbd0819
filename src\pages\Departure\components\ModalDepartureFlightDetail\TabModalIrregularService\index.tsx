/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { openModalEnterTotalValue } from '@/src/store/ModalDepartureFlightSlice'
import { Button, Card, Form } from 'antd'
import { useQuery } from '@tanstack/react-query'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getIrregularServiceDetail } from '@/src/service/irregular-service'
import { lazy, Suspense, useEffect } from 'react'
import Empty from '@/src/assets/notask.svg'
import {
  CabinCrew,
  NewTickets,
  FIM,
  IFE,
  Offload,
  Hotel,
  Restaurant,
  Taxi,
  CDM,
  Other,
} from './ModalEnterTotalValue'

type AnyObject = Record<string, any>

const IGNORED_FIELDS = [
  'creationTime',
  'creatorId',
  'id',
  'lastModificationTime',
  'lastModifierId',
] as const

const filterNonEmptyItems = (data: AnyObject): AnyObject => {
  const result: AnyObject = {}

  for (const [key, value] of Object.entries(data || {})) {
    if (typeof value === 'object' && value !== null) {
      let hasNumber = false
      let hasString = false

      for (const [fieldKey, fieldValue] of Object.entries(value)) {
        if (IGNORED_FIELDS.includes(fieldKey as any)) continue

        if (typeof fieldValue === 'number' && fieldValue !== 0) {
          hasNumber = true
          break
        }
        if (typeof fieldValue === 'string' && fieldValue.trim() !== '') {
          hasString = true
          break
        }
      }

      if (hasNumber || hasString) {
        result[key] = value
      }
    }
  }

  return result
}

const ModalEnterTotalValue = lazy(() =>
  import('./ModalEnterTotalValue').then(module => ({
    default: module.ModalEnterTotalValue,
  }))
)

const sectionMap: Record<
  string,
  { title: string; Component: (props: { readOnly: boolean }) => JSX.Element }
> = {
  restaurant: { title: 'Restaurant', Component: Restaurant },
  cabinCrew: { title: 'Cabin crew', Component: CabinCrew },
  taxi: { title: 'Taxi', Component: Taxi },
  hotel: { title: 'Hotel', Component: Hotel },
  offload: { title: 'Offload', Component: Offload },
  ife: { title: 'IFE', Component: IFE },
  fim: { title: 'FIM', Component: FIM },
  newTicket: { title: 'New tickets', Component: NewTickets },
  cdmCompensation: {
    title: 'C.D.M compensation (Bồi thường ứng trước không hoàn lại)',
    Component: CDM,
  },
  other: { title: 'Other', Component: Other },
}

const TabModalIrregularService = () => {
  const dispatch = useAppDispatch()
  const { selectFlightModalId } = useAppSelector(
    state => state.modalDepartureFlight
  )
  const [form] = Form.useForm()

  const { data } = useQuery({
    queryKey: ['irregular-service', selectFlightModalId],
    queryFn: () => getIrregularServiceDetail(selectFlightModalId as string),
  })

  const filtered = filterNonEmptyItems(data)

  useEffect(() => {
    if (data) {
      form.setFieldsValue(data)
    }
  }, [data])

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      <Form
        form={form}
        className="flex flex-col gap-y-4"
        colon={false}
        labelAlign="left"
      >
        {data?.id && Object.keys(filtered).length !== 0 ? (
          <>
            {Object.entries(sectionMap).map(([key, { title, Component }]) =>
              filtered[key] ? (
                <Card key={key} title={title} className="!border-primary">
                  <Component readOnly={true} />
                </Card>
              ) : null
            )}

            <div
              className="flex justify-end"
              onClick={() => {
                dispatch(openModalEnterTotalValue())
              }}
            >
              <Button type="primary">Update irregular service</Button>
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center min-h-[calc(65vh)] bg-[#F0F1F3FF] p-4">
            <img src={Empty} alt="" />
            <div
              className="flex justify-end"
              onClick={() => {
                dispatch(openModalEnterTotalValue())
              }}
            >
              <Button type="primary">Create irregular service</Button>
            </div>
          </div>
        )}
      </Form>
      <Suspense>
        <ModalEnterTotalValue />
      </Suspense>
    </div>
  )
}

export default TabModalIrregularService
