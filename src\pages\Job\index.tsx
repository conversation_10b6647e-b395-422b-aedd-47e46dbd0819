/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import { DISPLAY_DATE, TIME_DEBOUNCE } from '@/src/constants'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import type { IPTSTask } from '@/src/schema/IPTSTask'
import {
  createPTSTask,
  getPTSTask,
  removePTSTask,
  updatePTSTask,
} from '@/src/service/pts_task'
import {
  closeCreateJobModal,
  closeDeleteModal,
  closeFilterModal,
  openCreateJobModal,
  openDeleteModal,
  openFilterModal,
  resetState,
  selectJob,
  setParams,
  setSearchDebounce,
} from '@/src/store/JobSlice'
import {
  CopyOutlined,
  DeleteFilled,
  EditFilled,
  FilterFilled,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  Checkbox,
  Form,
  Input,
  message,
  Modal,
  Popover,
  Radio,
  Space,
  Table,
  Tag,
  type TableProps,
} from 'antd'
import dayjs from 'dayjs'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
import { handleApiError } from '@/src/helper/handleApiError'
import ShowTotal from '@/src/components/Showtotal'
import {
  LAO_PTS_TASKS_CREATE,
  LAO_PTS_TASKS_EDIT,
} from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'

const JobPage = () => {
  const [form] = Form.useForm()

  const { t } = useTranslation()
  const { hasPermission } = usePermission()
  const dispatch = useAppDispatch()

  const {
    visibleCreateJobModal,
    selectedJobId,
    visibleDeleteModal,
    visibleFilterModal,
    params,
    KeyWord,
  } = useAppSelector(state => state.job)

  const KeyWordDebounce = useDebounce(KeyWord, TIME_DEBOUNCE)

  const {
    data,
    isLoading,
    refetch: getPTSTaskList,
  } = useQuery({
    queryKey: ['pts-tasks-list', params],
    queryFn: () => getPTSTask(params),
  })

  const mutation = useMutation({
    mutationFn: async (values: IPTSTask) => {
      return values.id ? updatePTSTask(values) : createPTSTask(values)
    },
    onSuccess: () => {
      message.success(
        selectedJobId ? t('job.jobUpdateSuccess') : t('job.jobCreateSuccess')
      )
      getPTSTaskList()
      form.resetFields()
      dispatch(closeCreateJobModal())
      dispatch(selectJob(''))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const mutationDelete = useMutation({
    mutationFn: async (id: string) => removePTSTask(id),
    onSuccess: () => {
      getPTSTaskList()
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const columns: TableProps<IPTSTask>['columns'] = [
    {
      title: t('job.jobCode'),
      dataIndex: 'code',
      key: 'code',
      width: '5%',
      fixed: 'left',
    },
    {
      title: t('job.jobName'),
      dataIndex: 'description',
      key: 'description',
      width: '30%',
      render: (str: string) => <div className="line-clamp-4">{str}</div>,
    },
    {
      title: t('job.jobNameEng'),
      dataIndex: 'descriptionEn',
      key: 'descriptionEn',
      width: '30%',
      render: (str: string) => <div className="line-clamp-4">{str}</div>,
    },
    {
      title: t('job.jobCreatedDate'),
      dataIndex: 'creationTime',
      key: 'creationTime',
      width: '10%',
      align: 'center',
      render: (creationTime: string) => (
        <>{dayjs(creationTime).format(DISPLAY_DATE)}</>
      ),
    },
    {
      title: t('job.jobStatus'),
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: '10%',
      render: (status: number) => (
        <Tag
          color={`${status === 1 ? '#E6F7EC' : '#F7E6E6'}`}
          className={`${status === 1 ? '!text-positive' : '!text-negative'} !text-xs !font-medium !rounded-full`}
        >
          {status === 1 ? t('job.jobStatusUsing') : t('job.jobStatusNotUsing')}
        </Tag>
      ),
    },
    {
      title: t('job.jobAction'),
      key: 'thao_tac',
      align: 'center',
      width: '5%',
      hidden:
        !hasPermission(LAO_PTS_TASKS_CREATE) &&
        !hasPermission(LAO_PTS_TASKS_EDIT),
      render: (record: IPTSTask) => (
        <Space>
          {hasPermission(LAO_PTS_TASKS_CREATE) && (
            <CopyOutlined
              className="hover:bg-slate-200 p-1 rounded-sm"
              onClick={() => {
                dispatch(selectJob(''))
                dispatch(openCreateJobModal())
                form.setFieldsValue({
                  ...record,
                  id: '',
                  code: record.code + '_copy',
                  status: '1',
                })
              }}
            />
          )}
          {hasPermission(LAO_PTS_TASKS_EDIT) && (
            <EditFilled
              className="hover:bg-slate-200 p-1 rounded-sm"
              onClick={() => {
                dispatch(selectJob(record.id))
                dispatch(openCreateJobModal())
                form.setFieldsValue({
                  ...record,
                  status: String(record.status),
                })
              }}
            />
          )}
          <DeleteFilled
            className="hover:bg-slate-200 p-1 rounded-sm !hidden"
            onClick={() => {
              if (String(record.status) === '1') {
                message.error(t('job.jobDeleteMessage'))
                return
              }
              dispatch(openDeleteModal())
              dispatch(selectJob(record.id))
            }}
          />
        </Space>
      ),
    },
  ]

  const onFinish = async () => {
    try {
      const values = await form.validateFields()

      const data = {
        ...values,
        id: selectedJobId ?? '',
        status: Number(values.status ?? 1),
      }
      mutation.mutate(data)
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  useEffect(() => {
    dispatch(setParams({ ...params, SkipCount: 0, KeyWord: KeyWordDebounce }))
  }, [KeyWordDebounce])

  useEffect(() => {
    dispatch(resetState())
  }, [])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="w-full flex justify-between">
        <div className="text-lg font-bold text-black">{t('job.jobList')}</div>
        <div className="flex flex-row gap-x-2">
          {hasPermission(LAO_PTS_TASKS_CREATE) && (
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                form.resetFields()
                dispatch(openCreateJobModal())
              }}
            >
              {t('job.jobCreate')}
            </Button>
          )}
          <Input
            placeholder={t('job.jobSearchPlaceholder')}
            prefix={<SearchOutlined />}
            onChange={val => {
              dispatch(setSearchDebounce(val.target.value))
            }}
          />
          <Popover
            open={visibleFilterModal}
            placement="bottomRight"
            trigger={['click']}
            content={
              <Form className="p-3 w-full flex flex-col gap-y-2" form={form}>
                <div className="text-base font-normal text-[#525050]">
                  {t('job.jobStatus')}
                </div>
                <Form.Item name="Status">
                  <Checkbox.Group>
                    <Space>
                      <Checkbox value="1">{t('job.jobStatusUsing')}</Checkbox>
                      <Checkbox value="0">
                        {t('job.jobStatusNotUsing')}
                      </Checkbox>
                    </Space>
                  </Checkbox.Group>
                </Form.Item>
                <div className="flex w-full justify-end gap-x-2">
                  <Button
                    className="!font-bold"
                    type="text"
                    onClick={() => {
                      form.resetFields()
                      dispatch(setParams({ ...params, Status: '' }))
                      dispatch(closeFilterModal())
                      getPTSTaskList()
                    }}
                  >
                    {t('common.cancel')}
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => {
                      const status = form.getFieldValue('Status') || []
                      const shouldShowAll =
                        status.length === 0 || status.length === 2

                      dispatch(
                        setParams({
                          ...params,
                          SkipCount: 0,
                          Status: shouldShowAll ? '' : status.join(','),
                        })
                      )
                      dispatch(closeFilterModal())
                      getPTSTaskList()
                    }}
                  >
                    {t('common.apply')}
                  </Button>
                </div>
              </Form>
            }
          >
            <Button
              icon={<FilterFilled />}
              onClick={() => {
                if (visibleFilterModal) {
                  dispatch(closeFilterModal())
                } else {
                  dispatch(openFilterModal())
                  form.setFieldsValue({
                    Status: params.Status ? params.Status.split(',') : [],
                  })
                }
              }}
            >
              {t('job.jobFilter')}
            </Button>
          </Popover>
        </div>
      </div>
      <Table
        loading={isLoading}
        columns={columns}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        pagination={{
          total: isLoading ? 0 : data?.totalCount,
          current: params.SkipCount / params.MaxResultCount + 1,
          pageSize: params.MaxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                SkipCount: (page - 1) * pageSize,
                MaxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        dataSource={isLoading ? [] : data?.items}
        rowKey={record => record.id}
      />
      <Modal
        open={visibleDeleteModal}
        closable={false}
        onCancel={() => {
          dispatch(selectJob(''))
          dispatch(closeDeleteModal())
        }}
        onOk={() => {
          if (selectedJobId) {
            mutationDelete.mutate(selectedJobId)
          }
          dispatch(selectJob(''))
          dispatch(closeDeleteModal())
        }}
      >
        <div className="text-base font-bold text-black">
          {t('job.jobDeleteConfirm')}
        </div>
      </Modal>
      <Modal
        open={visibleCreateJobModal}
        okButtonProps={{ style: { display: 'none' } }}
        cancelButtonProps={{ style: { display: 'none' } }}
        closable={false}
        title={
          <div className="w-full flex text-2xl font-bold text-[#54595E]">
            {selectedJobId ? t('job.jobInforDetail') : t('job.jobInfoTitle')}
          </div>
        }
        footer={
          <Space className="w-full flex justify-end">
            <Button
              className="!text-primary"
              onClick={() => {
                dispatch(selectJob(''))
                form.resetFields()
                dispatch(closeCreateJobModal())
              }}
            >
              {t('common.cancel')}
            </Button>
            <Button type="primary" onClick={onFinish}>
              {t('job.jobSave')}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label={t('job.jobCodeLabel')}
            name="code"
            rules={[
              { required: true, message: t('job.jobCodeRequired') },
              {
                max: 50,
                message: t('job.jobCodeLength'),
              },
            ]}
          >
            <Input placeholder={t('job.jobCodePlaceholder')} showCount />
          </Form.Item>
          <Form.Item
            label={t('job.jobNameLabel')}
            name="description"
            rules={[
              { required: true, message: t('job.jobNameRequired') },
              {
                max: 500,
                message: t('job.jobNameLength'),
              },
            ]}
          >
            <Input placeholder={t('job.jobNamePlaceholder')} showCount />
          </Form.Item>
          <Form.Item
            label={t('job.jobNameLabelEng')}
            name="descriptionEn"
            rules={[
              { required: true, message: t('job.jobNameRequired') },
              {
                max: 500,
                message: t('job.jobNameEngLength'),
              },
            ]}
            required
          >
            <Input placeholder={t('job.jobNamePlaceholderEng')} showCount />
          </Form.Item>
          <Form.Item
            label={t('job.jobStatusLabel')}
            name="status"
            initialValue="1"
            rules={[{ required: true }]}
          >
            <Radio.Group>
              <Radio value="1">{t('job.jobStatusUsing')}</Radio>
              <Radio value="0">{t('job.jobStatusNotUsing')}</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default JobPage
