import { Col, Form, Input, Row } from 'antd'
import { useTranslation } from 'react-i18next'

const TabModalACTechInfo = () => {
  const { t } = useTranslation()
  const [form] = Form.useForm()

  return (
    <div className="bg-[#F0F1F3FF] p-4 h-full min-h-[calc(70vh)]">
      <div className="text-secondary flex justify-end">
        {t('common.noData')}
      </div>
      <Form form={form} layout="vertical">
        <Row>
          <Col span={24}>
            <Form.Item label="A/C Status">
              <Input.TextArea rows={2} className="pointer-events-none" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="Cabin Status">
              <Input.TextArea rows={2} className="pointer-events-none" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  )
}

export default TabModalACTechInfo
