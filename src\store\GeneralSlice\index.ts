/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

type GeneralState = {
  params: any
  selectedFlightId: string | null
  visibleFilterModal: boolean
  visibleDropdownViewColumn: boolean
  checkListFlight: any[]
  visibleModalArrivalGeneralFlightInformation: boolean
  visibleModalDepartureGeneralFlightInformation: boolean
  selectedPTSMasterId: string | null
  selectedArrivalFlightId: string | null
  combined: boolean
}

const initialState: GeneralState = {
  params: {
    skipCount: 0,
    maxResultCount: 20,
  },
  selectedFlightId: null,
  visibleFilterModal: false,
  visibleDropdownViewColumn: false,
  checkListFlight: [],
  visibleModalArrivalGeneralFlightInformation: false,
  visibleModalDepartureGeneralFlightInformation: false,
  selectedPTSMasterId: null,
  selectedArrivalFlightId: null,
  combined: true,
}

const generalSlice = createSlice({
  name: 'general',
  initialState,
  reducers: {
    setParams: (state, action) => {
      state.params = action.payload
    },
    setSelectedFlightId: (state, action) => {
      state.selectedFlightId = action.payload
    },
    openFilterModal: state => {
      state.visibleFilterModal = true
    },
    closeFilterModal: state => {
      state.visibleFilterModal = false
    },
    closeDropdownViewColumn: state => {
      state.visibleDropdownViewColumn = false
    },
    openDropdownViewColumn: state => {
      state.visibleDropdownViewColumn = true
    },
    setCheckListFlight(state, action: PayloadAction<any[]>) {
      state.checkListFlight = action.payload
    },
    openModalArrivalGeneralFlightInformation: state => {
      state.visibleModalArrivalGeneralFlightInformation = true
    },
    closeModalArrivalGeneralFlightInformation: state => {
      state.visibleModalArrivalGeneralFlightInformation = false
    },
    openModalDepartureGeneralFlightInformation: state => {
      state.visibleModalDepartureGeneralFlightInformation = true
    },
    closeModalDepartureGeneralFlightInformation: state => {
      state.visibleModalDepartureGeneralFlightInformation = false
    },
    setSelectedPTSMasterId: (state, action) => {
      state.selectedPTSMasterId = action.payload
    },
    setSelectedArrivalFlightId: (state, action) => {
      state.selectedArrivalFlightId = action.payload
    },
    setCombined: (state, action) => {
      state.combined = action.payload
    },
  },
})

export const {
  setParams,
  setSelectedFlightId,
  openFilterModal,
  closeFilterModal,
  closeDropdownViewColumn,
  openDropdownViewColumn,
  setCheckListFlight,
  openModalArrivalGeneralFlightInformation,
  closeModalArrivalGeneralFlightInformation,
  openModalDepartureGeneralFlightInformation,
  closeModalDepartureGeneralFlightInformation,
  setSelectedPTSMasterId,
  setSelectedArrivalFlightId,
  setCombined,
} = generalSlice.actions

export default generalSlice.reducer
