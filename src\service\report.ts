/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'

const prefix = `/api/v1`

const getReportFlightList = async (params: any) => {
  const api = `${prefix}/flights/summary/list`
  const response = await axios.get(api, { params })
  return response.data
}

const getReportGTList = async (params: any) => {
  const api = `${prefix}/flights/gt/list`
  const response = await axios.get(api, { params })
  return response.data
}

const getReportPTSTaskList = async (params: any) => {
  const api = `${prefix}/pts-tasks/list`
  const response = await axios.get(api, { params })
  return response.data
}

const getReportPTSGroupTaskList = async (params: any) => {
  const api = `${prefix}/group-pts-tasks/list`
  const response = await axios.get(api, { params })
  return response.data
}

const getReportOrganizationUnitLate = async (params: any) => {
  const api = `${prefix}/flights/summary/organization-unit/list`
  const response = await axios.get(api, { params })
  return response.data
}

const exportExcelReportFlightList = async (params: any) => {
  const api = `${prefix}/flights/summary/export-csv`
  const response = await axios.get(api, { params, responseType: 'blob' })
  return response.data
}

const exportExcelReportGTList = async (params: any) => {
  const api = `${prefix}/flights/gt/export-csv`
  const response = await axios.get(api, { params, responseType: 'blob' })
  return response.data
}

const exportExcelReportPTSGroupTaskList = async (params: any) => {
  const api = `${prefix}/group-pts-tasks/export-csv`
  const response = await axios.get(api, { params, responseType: 'blob' })
  return response.data
}

const exportExcelReportPTSTaskList = async (params: any) => {
  const api = `${prefix}/pts-tasks/export-csv`
  const response = await axios.get(api, { params, responseType: 'blob' })
  return response.data
}

const exportExcelReportOrganizationUnitLate = async (params: any) => {
  const api = `${prefix}/flights/summary/organization-unit/export-csv`
  const response = await axios.get(api, { params, responseType: 'blob' })
  return response.data
}

const exportExcelReportOrganizationUnitLateDetail = async (
  executeOUId: string,
  groupPtsTaskId: string,
  params: any
) => {
  const api = `${prefix}/flights/summary/organization-unit/${executeOUId}/${groupPtsTaskId}/export-csv`
  const response = await axios.get(api, {
    responseType: 'blob',
    params,
  })
  return response.data
}

export {
  getReportFlightList,
  getReportGTList,
  getReportPTSTaskList,
  getReportPTSGroupTaskList,
  getReportOrganizationUnitLate,
  exportExcelReportGTList,
  exportExcelReportPTSGroupTaskList,
  exportExcelReportPTSTaskList,
  exportExcelReportFlightList,
  exportExcelReportOrganizationUnitLate,
  exportExcelReportOrganizationUnitLateDetail,
}
