import { icon_flight } from '@/src/assets/image'
import { DISPLAY_DATE, TIME_ONLY } from '@/src/constants'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getFlightDetail } from '@/src/service/flight'
import {
  ArrowRightOutlined,
  EditFilled,
  MinusOutlined,
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { Button, Spin } from 'antd'
import moment from 'moment'
import { useTranslation } from 'react-i18next'

const TabFlightInfo = () => {
  const { selectedFlightId } = useAppSelector(state => state.departure)

  const { t } = useTranslation()

  const { data: flightDetail, isLoading } = useQuery({
    queryKey: ['flight-detail', selectedFlightId],
    queryFn: () => selectedFlightId && getFlightDetail(selectedFlightId),
    enabled: !!selectedFlightId,
  })

  return selectedFlightId ? (
    <Spin spinning={isLoading}>
      <div className="bg-[#F5F9FA] py-3 gap-y-3 flex flex-col">
        <div className="flex w-full justify-between">
          <div className="flex gap-x-4 h-full">
            <div className="bg-[#DF9F20] rounded-sm px-3 py-5 flex gap-x-3 items-center w-max justify-center">
              <img src={icon_flight} alt="icon_flight" />
              <div className="text-xl font-bold text-white">
                {flightDetail?.fnCarrier}
                {flightDetail?.fnNumber}
              </div>
            </div>
            <div className="flex flex-col h-full justify-between gap-y-2 text-sm font-semibold">
              <div className="flex gap-x-2">
                <div className="py-1 px-3 rounded-full bg-[#CCE1E7]">
                  {flightDetail?.acSubType}
                </div>
                <div className="py-1 px-3 rounded-full bg-[#CCE1E7]">
                  {flightDetail?.acRegistration}
                </div>
              </div>
              <div className="flex gap-x-2">
                <div className="py-1 px-3 rounded-full bg-[#CCE1E7]">
                  {flightDetail?.fltType}
                </div>
                <div className="py-1 px-3 rounded-full bg-[#CCE1E7]">
                  {flightDetail?.seatsC}C/
                  {flightDetail?.seatsW > 0 && `${flightDetail?.seatsW}W/`}
                  {flightDetail?.seatsY}Y
                </div>
                <div className="py-1 px-3 rounded-full bg-[#FF8000]">
                  {flightDetail?.legState}
                </div>
              </div>
            </div>
            {/* <div className="px-4 border-l-2 border-[#B5B4B4] flex flex-col h-full justify-between font-semibold text-sm">
            <div>
              Flight duty:&nbsp;<span className="font-normal">1</span>
            </div>
            <div> 
              Gate duty:&nbsp;<span className="font-normal">2</span>
            </div>
            <div>
              Reason Delay:&nbsp;<span className="font-normal">3</span>
            </div>
          </div> */}
          </div>
          <div className="flex my-auto">
            <Button
              className="!hidden"
              icon={<EditFilled />}
              type="primary"
              onClick={() => {}}
            >
              {t('common.edit')}
            </Button>
          </div>
        </div>
        <div className="flex flex-row gap-x-4">
          <div className="bg-[#E6F0F3] rounded-sm px-3 py-2 flex flex-col">
            <div className="font-bold flex gap-x-4 mb-3 text-2xl">
              {flightDetail?.depApSched}
              <ArrowRightOutlined /> {flightDetail?.arrApSched}
            </div>
            <p className="text-base font-bold mb-3">
              {moment(flightDetail?.dayOfOrigin).format(DISPLAY_DATE)}
            </p>
            <div className="grid grid-cols-3">
              <div>
                <div className="font-normal text-xs">STD</div>
                <div className="text-base font-bold">
                  {moment(flightDetail?.depSchedDt).format(TIME_ONLY)}
                </div>
              </div>
              <div className="justify-end flex flex-col h-full items-center pb-1">
                <MinusOutlined />
              </div>
              <div>
                <div className="font-normal text-xs">STA</div>
                <div className="text-base font-bold">
                  {moment(flightDetail?.arrSchedDt).format(TIME_ONLY)}
                </div>
              </div>
            </div>
            <div className="grid grid-cols-3">
              <div>
                <div className="font-normal text-xs">ATD</div>
                <div className="text-base font-bold">
                  {moment(flightDetail?.depDt).format(TIME_ONLY)}
                </div>
              </div>
              <div className="justify-end flex flex-col h-full items-center pb-1">
                <MinusOutlined />
              </div>
              <div>
                <div className="font-normal text-xs">ATA</div>
                <div className="text-base font-bold">
                  {moment(flightDetail?.arrDt).format(TIME_ONLY)}
                </div>
              </div>
            </div>
            {/* <div className="grid grid-cols-3">
            <div>
              <div className="font-normal text-xs">BAY</div>
              <div className="text-base font-bold"></div>
            </div>
            <div className="justify-end flex flex-col h-full items-center pb-1">
              <MinusOutlined />
            </div>
            <div>
              <div className="font-normal text-xs">GATE</div>
              <div className="text-base font-bold">07:25</div>
            </div>
          </div> */}
          </div>
          {/* <div className="bg-[#E6F0F3] rounded-sm px-3 py-2 flex flex-col w-full">
          <div className="text-sm font-semibold">AC Tech Info</div>
        </div> */}
        </div>
      </div>
    </Spin>
  ) : (
    <div className="flex justify-center items-center h-56 font-semibold text-primary">
      {t('departure.notFoundFlightInfo')}
    </div>
  )
}

export default TabFlightInfo
