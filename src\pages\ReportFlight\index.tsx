/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import DropdownChangeColumn from '@/src/components/DropdownColumn'
import ShowTotal from '@/src/components/Showtotal'
import {
  DISPLAY_DATE,
  ISO_DATETIME,
  ISO_DATETIME_NOSECOND,
  PIE_CHART_OSP,
  PIE_CHART_OTP,
} from '@/src/constants'
import { normalizeText } from '@/src/helper/normalizeText'
import { isValidRangeDate } from '@/src/helper/validDate'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import {
  exportExcelReportFlightList,
  getReportFlightList,
} from '@/src/service/report'
import {
  closeViewColumnFlight,
  openViewColumnFlight,
  setCheckListReportFlight,
} from '@/src/store/ReportSlice'
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  DatePicker,
  Input,
  Select,
  Table,
  type TableColumnsType,
} from 'antd'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import moment from 'moment'
import QueryString from 'qs'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import styles from './index.module.scss'

const { RangePicker } = DatePicker

const legState_ARR = ['DEP', 'ARR', 'ON', 'OFF', 'IN', 'OUT']

const ReportFlightPage = () => {
  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search
  const [paramReportFlight, setParamReportFlight] = useState<{
    skipCount: number
    maxResultCount: number
    type: number
    fromDate: string
    toDate: string
    airport: string
    keyWord: string
  }>(() => {
    const params = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof params.fromDate === 'string' ? dayjs(params.fromDate) : null
    const to = typeof params.toDate === 'string' ? dayjs(params.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...params,
      type: Number(params.type) || PIE_CHART_OSP,
      skipCount: Number(params.skipCount) || 0,
      maxResultCount: Number(params.maxResultCount) || 20,
      fromDate:
        valid && from
          ? from.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
    }

    return value
  })

  const navigate = useNavigate()

  const [flightKeyword, setFlightKeyword] = useState<undefined | string>(
    undefined
  )
  const debouncedFlightKeyword = useDebounce(flightKeyword, 500)

  const [airportKeyword, setAirportKeyword] = useState<undefined | string>(
    undefined
  )

  const debouncedAirportKeyword = useDebounce(airportKeyword, 500)
  const { t } = useTranslation()
  const dispatch = useAppDispatch()
  const { visibleViewColumnFlight, checkListReportFlight } = useAppSelector(
    state => state.report
  )

  const columns: TableColumnsType<any> = [
    {
      title: t('table.order'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_value, _record, index) =>
        paramReportFlight.skipCount + index + 1,
    },
    {
      title: t('table.aircraft'),
      dataIndex: 'acSubType',
      key: 'airCraft',
      width: 100,
      align: 'center',
    },
    {
      title: 'Register No',
      dataIndex: 'acRegistration',
      key: 'registerNo',
      width: 120,
      align: 'center',
    },
    {
      title: 'Flight No',
      key: 'flightNo',
      width: 120,
      align: 'center',
      render: (_value, record) => record.fnCarrier + record.fnNumber,
    },
    {
      title: 'Routing',
      key: 'routing',
      width: 120,
      align: 'center',
      render: (_value, record) => `${record.depApSched}-${record.arrApSched}`,
    },
    {
      title: 'STD',
      dataIndex: 'depSchedDt',
      key: 'std',
      width: 150,
      align: 'center',
      render: value => <>{moment(value).format(ISO_DATETIME_NOSECOND)}</>,
    },
    {
      title: 'ETD',
      dataIndex: 'depDt',
      key: 'etd',
      width: 150,
      align: 'center',
      render: (value: any) => moment(value).format(ISO_DATETIME_NOSECOND),
    },
    {
      title: 'BH',
      dataIndex: 'flightHrs',
      key: 'fht',
      width: 80,
      align: 'center',
    },
    {
      key: 'atd',
      dataIndex: 'depDt',
      title: 'ATD',
      align: 'center',
      width: 150,
      render: (value: any, record: any) =>
        legState_ARR.includes(record.legState)
          ? moment(value).format(ISO_DATETIME_NOSECOND)
          : null,
    },
    {
      title: t('table.status'),
      dataIndex: 'legState',
      key: 'status',
      width: 80,
      align: 'center',
    },
    {
      title: t('table.reason'),
      dataIndex: 'reason',
      key: 'reason',
      width: 120,
      align: 'center',
    },
  ]

  const { data, isLoading } = useQuery({
    queryKey: ['report-flight', paramReportFlight],
    queryFn: () => getReportFlightList(paramReportFlight),
  })

  const { refetch: exportExcelReport, isLoading: isLoadingExport } = useQuery({
    queryKey: ['report-flight-export', paramReportFlight],
    queryFn: () =>
      exportExcelReportFlightList({
        ...paramReportFlight,
        skipCount: 0,
      }),
    enabled: false,
  })

  const handleExport = async () => {
    const { data } = await exportExcelReport()
    const fileName = `${t('common.reportName')}_${normalizeText(`${t('report.reportFlight')}_${paramReportFlight.type === PIE_CHART_OTP ? 'OTP' : 'OSP'}`)}.csv`
    return FileSaver.saveAs(data, fileName)
  }

  const newColumns = columns.map(item => ({
    ...item,
    hidden: !checkListReportFlight.includes(item.key as string),
  }))

  useEffect(() => {
    dispatch(setCheckListReportFlight(columns.map(item => item.key as string)))
  }, [])

  useEffect(() => {
    if (flightKeyword !== undefined) {
      setParamReportFlight({
        ...paramReportFlight,
        skipCount: 0,
        keyWord: debouncedFlightKeyword as string,
      })
    }
  }, [debouncedFlightKeyword])

  useEffect(() => {
    if (airportKeyword !== undefined) {
      setParamReportFlight({
        ...paramReportFlight,
        skipCount: 0,
        airport: debouncedAirportKeyword as string,
      })
    }
  }, [debouncedAirportKeyword])

  useEffect(() => {
    navigate(
      `/report/flight-late?${QueryString.stringify(paramReportFlight)}`,
      {
        replace: true,
      }
    )
  }, [paramReportFlight])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex justify-between w-full">
        <div className="text-lg font-bold text-black">
          {paramReportFlight.type === PIE_CHART_OTP
            ? t('report.reportFlightOTP')
            : t('report.reportFlightOSP')}
        </div>
        <div className="flex flex-row gap-x-2 max-2xl:flex-wrap max-2xl:gap-y-2">
          <Select
            defaultValue={
              Number(searchParams.get('type'))
                ? Number(searchParams.get('type'))
                : PIE_CHART_OSP
            }
            options={[
              { value: PIE_CHART_OTP, label: 'OTP' },
              {
                value: PIE_CHART_OSP,
                label: 'OSP',
              },
            ]}
            onChange={val => {
              setParamReportFlight({
                ...paramReportFlight,
                type: val,
                skipCount: 0,
              })
            }}
          />
          <RangePicker
            className="!h-max"
            allowClear={false}
            defaultValue={[
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('fromDate'))
                : dayjs().startOf('day'),
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('toDate'))
                : dayjs().endOf('day'),
            ]}
            format={DISPLAY_DATE}
            onChange={value => {
              if (value) {
                const newParams = {
                  ...paramReportFlight,
                  skipCount: 0,
                  fromDate: dayjs(value[0]).startOf('day').format(ISO_DATETIME),
                  toDate: dayjs(value[1]).endOf('day').format(ISO_DATETIME),
                }
                return setParamReportFlight(newParams)
              }
            }}
          />
          <Input
            placeholder={t('report.searchAirportPlaceholder')}
            className="!w-48 h-max"
            prefix={<SearchOutlined />}
            onChange={e => {
              setAirportKeyword(e.target.value)
            }}
            defaultValue={searchParams.get('airport') || undefined}
          />

          <Input
            placeholder={t('report.searchFlightPlaceholder')}
            className="!w-48 h-max"
            prefix={<SearchOutlined />}
            onChange={e => {
              setFlightKeyword(e.target.value)
            }}
            defaultValue={searchParams.get('keyWord') || ''}
          />
          <Button
            icon={<DownloadOutlined />}
            loading={isLoadingExport}
            disabled={isLoadingExport}
            onClick={handleExport}
          >
            {t('report.download')}
          </Button>
          <DropdownChangeColumn
            columns={columns || []}
            onChangeColumn={val => {
              dispatch(setCheckListReportFlight(val))
            }}
            onOk={() => dispatch(openViewColumnFlight())}
            onCancel={() => dispatch(closeViewColumnFlight())}
            open={visibleViewColumnFlight}
          />
        </div>
      </div>
      <Table
        bordered
        dataSource={data?.items || []}
        columns={newColumns}
        className={`${styles.whiteHeader}`}
        size="small"
        loading={isLoading}
        rowKey={record => record.id}
        pagination={{
          pageSize: paramReportFlight.maxResultCount,
          current:
            paramReportFlight.skipCount / paramReportFlight.maxResultCount + 1,
          total: data?.totalCount,
          showSizeChanger: true,
          onChange(page, pageSize) {
            setParamReportFlight({
              ...paramReportFlight,
              skipCount: (page - 1) * pageSize,
              maxResultCount: pageSize,
            })
          },
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
    </div>
  )
}

export default ReportFlightPage
