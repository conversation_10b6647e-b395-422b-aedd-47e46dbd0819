/* eslint-disable @typescript-eslint/no-explicit-any */
import { Col, Row, Table } from 'antd'
import styles from './index.module.scss'
import type { TableProps } from 'antd/lib'
import { useQuery } from '@tanstack/react-query'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getGds } from '@/src/service/gds'
import { useDispatch } from 'react-redux'
import { setParamsGd } from '@/src/store/ModalDepartureFlightSlice'
import ShowTotal from '@/src/components/Showtotal'
import { useTranslation } from 'react-i18next'

const TabModalGD = () => {
  const { selectedFlightId } = useAppSelector(state => state.departure)

  const { t } = useTranslation()

  const { paramsGd, visibleModalDepartureFlightDetail } = useAppSelector(
    state => state.modalDepartureFlight
  )

  const dispatch = useDispatch()

  const { data, isLoading } = useQuery({
    queryKey: ['gd-list', selectedFlightId],
    queryFn: () => getGds(selectedFlightId as string, paramsGd),
    enabled: !!selectedFlightId && visibleModalDepartureFlightDetail,
  })

  const columns: TableProps<any>['columns'] = [
    {
      title: 'Order',
      width: '10%',
      dataIndex: 'order',
      align: 'center',
      render: (_, __, index) => paramsGd.skipCount + index + 1,
    },
    { title: 'Func.', width: '10%', dataIndex: 'func' },
    { title: 'Name', width: '20%', dataIndex: 'name' },
    { title: 'Information', width: '60%', dataIndex: 'information' },
  ]

  return (
    <div className="bg-[#F0F1F3FF] p-4 min-h-[calc(65vh)]">
      <div className="text-secondary flex justify-end">
        {t('common.demodata')}
      </div>
      <Row gutter={[16, 0]}>
        <Col span={24}>
          <Table
            loading={isLoading}
            size="small"
            columns={columns}
            bordered
            className={`${styles.whiteHeader}`}
            dataSource={data?.items ?? []}
            pagination={{
              total: data?.totalCount,
              current: paramsGd.skipCount / paramsGd.maxResultCount + 1,
              pageSize: paramsGd.maxResultCount,
              onChange: (page, pageSize) => {
                dispatch(
                  setParamsGd({
                    ...paramsGd,
                    skipCount: (page - 1) * pageSize,
                    maxResultCount: pageSize,
                  })
                )
              },
              showTotal: (total, range) => (
                <ShowTotal total={total} range={range} />
              ),
            }}
          />
        </Col>
      </Row>
    </div>
  )
}

export default TabModalGD
