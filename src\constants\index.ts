const TIME_OUT_REQUEST = 1

const ACCESS_TOKEN = 'access_token'

const REFRESH_TOKEN = 'refresh_token'

const TIME_EXPIRED = 7

const ISO_DATETIME = 'YYYY-MM-DDTHH:mm:ss'
const ISO_DATETIME_HH_MM = 'YYYY-MM-DDTHH:mm'

const ISO_DATETIME_NOSECOND = 'DD/MM/YYYY HH:mm'

const ISO_FULLDATETIME = 'HH:mm ddd DD/MM/YYYY'

const ISO_DATE = 'YYYY-MM-DD'
const DISPLAY_DATETIME = 'DD/MM/YYYY [T]HH:mm:ss'
const DISPLAY_DATETIME_NOSECOND = 'DD/MM/YYYY [T]HH:mm'
const DISPLAY_DATE = 'DD/MM/YYYY'
const DISPLAY_DATE_SHORT = 'lll'
const DISPLAY_DATE_FULL = 'llll'
const TIME_ONLY = 'HH:mm'
const MONTH_YEAR = 'MM/YYYY'
const TIME_LT = 'LT'
const FULLTIME = 'HH:mm:ss'

const TIME_DEBOUNCE = 500

const PROVIDER_NAME_ROLE = 'R'

const PHONE_NUMBER_PATTERN = /^0\d{9,10}$/

const WIDGET_LATE_GT = 3

const WIDGET_ON_TIME_GT = 2

const WIDGET_ALL_FLIGHT = 1

const WIDGET_NO_RECORD_PTS = 4

const WIDGET_TASK_LATE = 3

const WIDGET_GROUP_TASK_LATE_OTP = 2

const WIDGET_GROUP_TASK_LATE_OSP = 1

const PIE_CHART_OTP = 2

const PIE_CHART_OSP = 1

const REPORT_LATE_GT = 3

const REPORT_ON_TIME_GT = 2

const REPORT_NO_RECORD_PTS = 4

const REPORT_LATE_GROUP_TASK_OTP = 2

const REPORT_LATE_GROUP_TASK_OSP = 1

const REPORT_PTS_TASK_LATE = 3

const AIRPORT_GLOBAL = 'airportGlobal'

const UNUSUAL_INFO_GROUND_SERVICE = 0

const UNUSUAL_INFO_FLIGHT = 1

const UNUSUAL_INFO_RAMPS = 2

const REASON_TYPE_REASON_DELAY = 0

const REASON_TYPE_REASON_CANCEL = 1

const REASON_TYPE_REASON_FHT = 2

const UPLOAD_BRIEFING = 1

const UPLOAD_UNUSUAL_INFO = 2

const UPLOAD_DEPO_INAD = 3

const PROCESSING_BY_OPTIONS = [
  { label: 'mont - Mơ Nguyễn Thị', value: 'mont - Mơ Nguyễn Thị' },
  { label: 'loitq - Lợi Trần Quốc', value: 'loitq - Lợi Trần Quốc' },
  { label: 'huytq - Huy Tạ Quang', value: 'huytq - Huy Tạ Quang' },
  { label: 'phucpq - Phúc Phạm Quang', value: 'phucpq - Phúc Phạm Quang' },
  { label: 'vyld - Vỹ Lê Duy', value: 'vyld - Vỹ Lê Duy' },
  { label: 'lamngo - Lam Ngô Hồng', value: 'lamngo - Lam Ngô Hồng' },
  { label: 'hungdv - Hùng Đặng Văn', value: 'hungdv - Hùng Đặng Văn' },
  {
    label: 'chaunvp - Châu Nguyễn Phong',
    value: 'chaunvp - Châu Nguyễn Phong',
  },
]

const GENDER = {
  0: 'Male',
  1: 'Female',
  2: 'Other',
}

export {
  ACCESS_TOKEN,
  ISO_DATETIME,
  ISO_DATE,
  DISPLAY_DATETIME,
  DISPLAY_DATE,
  DISPLAY_DATE_SHORT,
  DISPLAY_DATE_FULL,
  TIME_ONLY,
  FULLTIME,
  MONTH_YEAR,
  REFRESH_TOKEN,
  TIME_OUT_REQUEST,
  TIME_EXPIRED,
  TIME_LT,
  TIME_DEBOUNCE,
  PROVIDER_NAME_ROLE,
  ISO_DATETIME_NOSECOND,
  PHONE_NUMBER_PATTERN,
  WIDGET_LATE_GT,
  WIDGET_ON_TIME_GT,
  WIDGET_ALL_FLIGHT,
  WIDGET_NO_RECORD_PTS,
  WIDGET_TASK_LATE,
  WIDGET_GROUP_TASK_LATE_OTP,
  WIDGET_GROUP_TASK_LATE_OSP,
  PIE_CHART_OTP,
  PIE_CHART_OSP,
  REPORT_LATE_GT,
  REPORT_ON_TIME_GT,
  REPORT_NO_RECORD_PTS,
  REPORT_LATE_GROUP_TASK_OTP,
  REPORT_LATE_GROUP_TASK_OSP,
  REPORT_PTS_TASK_LATE,
  AIRPORT_GLOBAL,
  UNUSUAL_INFO_GROUND_SERVICE,
  UNUSUAL_INFO_FLIGHT,
  UNUSUAL_INFO_RAMPS,
  REASON_TYPE_REASON_DELAY,
  REASON_TYPE_REASON_CANCEL,
  REASON_TYPE_REASON_FHT,
  ISO_FULLDATETIME,
  PROCESSING_BY_OPTIONS,
  UPLOAD_BRIEFING,
  UPLOAD_UNUSUAL_INFO,
  UPLOAD_DEPO_INAD,
  GENDER,
  DISPLAY_DATETIME_NOSECOND,
  ISO_DATETIME_HH_MM,
}
