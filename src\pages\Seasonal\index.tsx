/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import ShowTotal from '@/src/components/Showtotal'
import { DISPLAY_DATE, ISO_DATETIME, TIME_ONLY } from '@/src/constants'
import { LAO_ENDHOUR, LAO_STARTHOUR } from '@/src/constants/setting'
import { handleApiError } from '@/src/helper/handleApiError'
import { renderTimeWithSign } from '@/src/helper/renderTimeSign'
import { isValidRangeDate } from '@/src/helper/validDate'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import type { IAirport } from '@/src/schema/IAirport'
import { getAirport } from '@/src/service/airport'
import { getFleet } from '@/src/service/fleet'
import { getNetwork } from '@/src/service/network'
import { getSeasonalFlight } from '@/src/service/seasonal'
import { syncFlight } from '@/src/service/sync'
import {
  closeDropdownViewColumn,
  closeFilterModal,
  openDropdownViewColumn,
  openFilterModal,
  openSeasonalInformationModal,
  setCheckListFlight,
  setParams,
  setSelectedFlightId,
} from '@/src/store/SeasonalSlice'
import {
  DownloadOutlined,
  FilterFilled,
  ReloadOutlined,
  SearchOutlined,
  SyncOutlined,
} from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { TableColumnsType } from 'antd'
import {
  Button,
  DatePicker,
  Flex,
  Form,
  Input,
  message,
  Popover,
  Select,
  Table,
} from 'antd'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import QueryString from 'qs'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import DropdownChangeColumn from '../../components/DropdownColumn'
import ModalSeasonlInformationDetail from './components/ModalSeasonalInformationDetail'
import styles from './index.module.scss'

dayjs.extend(utc)

const SeasonalPage = () => {
  const { t } = useTranslation()

  const { airportGlobal, settings } = useAppSelector(state => state.global)

  const navigate = useNavigate()

  const [form] = Form.useForm()

  const [formHour] = Form.useForm()

  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search

  const [keyword, setKeyword] = useState<undefined | string>(undefined)
  const debouncedKeyword = useDebounce(keyword, 500)

  const [paramsSeasonal, setParamsSeasonal] = useState<{
    maxResultCount: number
    skipCount: number
    fromDate: string
    toDate: string
    fleetTypes: string[]
    networks: string[]
    depApSched: string
    arrApSched: string
    keyWord: string
  }>(() => {
    const queryParams = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof queryParams.fromDate === 'string'
        ? dayjs(queryParams.fromDate)
        : null

    const to =
      typeof queryParams.toDate === 'string' ? dayjs(queryParams.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...queryParams,
      skipCount: Number(queryParams.skipCount) || 0,
      maxResultCount: Number(queryParams.maxResultCount) || 1000,
      fromDate:
        valid && from
          ? from?.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to?.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
      fleetTypes: queryParams.fleetTypes || [],
      networks: queryParams.networks || [],
      depApSched: airportGlobal,
      arrApSched: queryParams.arrApSched || '',
    }

    return value
  })

  const {
    selectedFlightId,
    visibleFilterModal,
    visibleDropdownViewColumn,
    checkListFlight,
  } = useAppSelector(state => state.seasonal)

  const { params: airportParams } = useAppSelector(state => state.airport)

  const query = useQuery({
    queryKey: ['seasonal-list', paramsSeasonal, airportGlobal],
    queryFn: () => {
      return getSeasonalFlight({
        ...paramsSeasonal,
        depApSched: airportGlobal,
      })
    },
    enabled: !!airportGlobal,
  })

  const { data: airportData } = useQuery({
    queryKey: ['get-airport-list', airportParams],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
  })

  const { data: networkData } = useQuery({
    queryKey: ['get-network-list'],
    queryFn: () => getNetwork(),
  })

  const { data: fleetData } = useQuery({
    queryKey: ['get-fleet-list'],
    queryFn: () =>
      getFleet({
        MaxResultCount: 1000,
      }),
  })

  const mutationSync = useMutation({
    mutationFn: async () => {
      return syncFlight()
    },
    onSuccess: () => {
      message.success(t('departure.syncSuccess'))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const dispatch = useAppDispatch()

  const columns: TableColumnsType<any> = [
    {
      key: 'index',
      title: t('table.order'),
      align: 'center',
      render: (_, __, index) => paramsSeasonal.skipCount + index + 1,
    },
    {
      key: 'nature',
      dataIndex: 'fltType',
      title: 'Nature',
      hidden: false,
      align: 'center',
    },
    {
      key: 'flightNo',
      title: 'Flight No.',
      hidden: false,
      render: (_value, record) => record.fnCarrier + record.fnNumber,
      align: 'center',
    },
    {
      key: 'aircraft',
      dataIndex: 'acSubType',
      title: 'Aircraft',
      hidden: false,
      align: 'center',
    },
    {
      key: 'codeShare',
      dataIndex: 'codeShare',
      title: 'Code share',
      hidden: false,
      align: 'center',
    },
    {
      key: 'from',
      dataIndex: 'from',
      title: 'From',
      hidden: false,
      align: 'center',
      render: record => <>{dayjs(record).format(DISPLAY_DATE)}</>,
    },
    {
      key: 'to',
      dataIndex: 'to',
      title: 'To',
      hidden: false,
      align: 'center',
      render: record => <>{dayjs(record).format(DISPLAY_DATE)}</>,
    },
    {
      key: 'std',
      dataIndex: 'depSchedDt',
      title: 'STD',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    {
      key: 'sta',
      dataIndex: 'arrSchedDt',
      title: 'STA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    {
      key: 'dow',
      dataIndex: 'dow',
      title: 'DOW',
      hidden: false,
      align: 'center',
    },
    {
      key: 'routing',
      title: 'Routing',
      hidden: false,
      render: (_value, record) => `${record.depApSched}-${record.arrApSched}`,
      align: 'center',
    },
    {
      key: 'acConfig',
      title: 'Configuration',
      hidden: false,
      align: 'center',
      dataIndex: '',
      render: record => (
        <>{`${record.seatsC}J${record.seatsW}W${record.seatsY}Y`}</>
      ),
    },
    {
      key: 'availableSeats',
      title: 'Available Seats',
      hidden: false,
      align: 'center',
      dataIndex: 'availableSeats',
    },
    {
      key: 'updateDate',
      title: 'Update Date',
      hidden: false,
      align: 'center',
      dataIndex: 'updateDate',
      render: value => <>{dayjs(value).format(DISPLAY_DATE)}</>,
    },
    {
      key: 'remark',
      dataIndex: 'remark',
      title: 'Remark',
      hidden: false,
      width: 300,
    },
  ]

  const newColumns = columns.map(item => ({
    ...item,
    hidden: !checkListFlight.includes(item.key as string),
  }))

  const now = dayjs()
  const roundedHour = now.startOf('hour')

  const fromDate = settings
    ? roundedHour.add(Number(settings?.[LAO_STARTHOUR]), 'hour')
    : dayjs().startOf('day')
  const toDate = settings
    ? roundedHour.add(Number(settings?.[LAO_ENDHOUR]), 'hour')
    : dayjs().endOf('day')

  const renderTitleTable = () => {
    return (
      <div className="w-full flex justify-end">
        <div className="flex flex-row gap-x-2 mt-2 items-center">
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            className="!text-sm !font-bold"
            onClick={() => query.refetch()}
            loading={query.isRefetching}
          >
            {t('departure.refresh')}
          </Button>
          <Button
            type="primary"
            icon={<SyncOutlined />}
            className="!text-sm !font-bold"
            loading={mutationSync.isPending}
            disabled={mutationSync.isPending}
            onClick={() => mutationSync.mutate()}
          >
            {t('departure.sync')}
          </Button>
          <Input
            prefix={<SearchOutlined />}
            placeholder={t('departure.searchPlaceholder')}
            className="!w-[200px]"
            defaultValue={searchParams.get('keyWord') || ''}
            onChange={e => {
              setKeyword(e.target.value)
            }}
          />
          <Popover
            open={visibleFilterModal}
            arrow={false}
            placement="bottomRight"
            trigger={['click']}
            content={
              <Form
                className="w-[600px]"
                form={form}
                layout="vertical"
                initialValues={{
                  depApSched: airportGlobal,
                  arrApSched: paramsSeasonal.arrApSched,
                  network: paramsSeasonal.networks,
                  fleetType: paramsSeasonal.fleetTypes,
                }}
              >
                <Flex className="flex flex-row w-full gap-x-6">
                  <Form.Item
                    name="depApSched"
                    label={t('departure.depApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      className="pointer-events-none"
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        { value: null, label: `${t('common.all')}` },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>

                  <Form.Item
                    name="arrApSched"
                    label={t('departure.arrApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        { value: '', label: t('common.all') },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>

                <Flex className="flex flex-row w-full gap-x-6">
                  <Form.Item
                    name="network"
                    label={t('departure.network')}
                    className="w-1/2"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      defaultValue={paramsSeasonal.networks}
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        ...(networkData?.map((item: any) => ({
                          value: item.name,
                          label: item.name,
                        })) || []),
                      ]}
                    />
                  </Form.Item>

                  <Form.Item
                    name="fleetType"
                    label={t('departure.fleetType')}
                    className="w-1/2"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        ...(fleetData?.items.map((item: any) => ({
                          value: item.code,
                          label: item.code,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>

                <div className="flex justify-end gap-x-2">
                  <Button
                    onClick={() => {
                      dispatch(closeFilterModal())
                    }}
                  >
                    {t('common.cancel')}
                  </Button>
                  <Button
                    onClick={() => {
                      form.resetFields()
                      setParamsSeasonal({
                        ...paramsSeasonal,
                        depApSched: airportGlobal,
                        arrApSched: '',
                        networks: [],
                        fleetTypes: [],
                        fromDate: dayjs().startOf('day').format(ISO_DATETIME),
                        toDate: dayjs().endOf('day').format(ISO_DATETIME),
                      })
                      form.setFieldsValue({
                        rangeDate: [
                          dayjs().startOf('day'),
                          dayjs().endOf('day'),
                        ],
                        depApSched: airportGlobal,
                        arrApSched: '',
                        network: [],
                        fleetType: [],
                      })
                      dispatch(closeFilterModal())
                    }}
                  >
                    {t('common.reset')}
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => {
                      const rangeDate = form.getFieldValue('rangeDate') || [
                        dayjs().startOf('day'),
                        dayjs().endOf('day'),
                      ]

                      const fromDate = dayjs(rangeDate[0])
                      const toDate = dayjs(rangeDate[1])

                      let fromHour = paramsSeasonal.fromHour || '00:00'
                      let toHour = paramsSeasonal.toHour || '00:00'

                      if (!fromDate.isSame(toDate, 'day')) {
                        fromHour = '00:00'
                        toHour = '00:00'

                        formHour.setFieldsValue({
                          rangeHour: [
                            dayjs('00:00', TIME_ONLY),
                            dayjs('00:00', TIME_ONLY),
                          ],
                        })
                      }

                      const arrApSched = form.getFieldValue('arrApSched')
                      const newParams = {
                        ...paramsSeasonal,
                        depApSched: airportGlobal,
                        arrApSched,
                        skipCount: 0,
                        networks: form.getFieldValue('network') || [],
                        fleetTypes: form.getFieldValue('fleetType') || [],
                        fromDate: fromDate.format(ISO_DATETIME),
                        toDate: toDate.format(ISO_DATETIME),
                        fromHour,
                        toHour,
                      }
                      setParamsSeasonal(newParams)
                      dispatch(closeFilterModal())
                    }}
                  >
                    {t('common.apply')}
                  </Button>
                </div>
              </Form>
            }
          >
            <Button
              icon={<FilterFilled />}
              onClick={() => {
                if (visibleFilterModal) {
                  dispatch(closeFilterModal())
                } else {
                  dispatch(openFilterModal())
                }
              }}
            >
              {t('common.filter')}
            </Button>
          </Popover>
          <Button icon={<DownloadOutlined />} className="!hidden">
            Download
          </Button>
          <DropdownChangeColumn
            columns={columns || []}
            onChangeColumn={val => dispatch(setCheckListFlight(val))}
            onOk={() => dispatch(openDropdownViewColumn())}
            onCancel={() => dispatch(closeDropdownViewColumn())}
            open={visibleDropdownViewColumn}
          />
        </div>
      </div>
    )
  }

  useEffect(() => {
    const data = query.data?.items
    const isSelectedFlightInData = (data: any[], selectedId: string | null) => {
      return data?.some(item => item.id === selectedId)
    }
    if (!isSelectedFlightInData(data, selectedFlightId)) {
      dispatch(setSelectedFlightId(query.data?.items[0]?.id))
    }
  }, [query.data])

  useEffect(() => {
    if (keyword !== undefined) {
      setParamsSeasonal({
        ...paramsSeasonal,
        skipCount: 0,
        keyWord: debouncedKeyword as string,
      })
    }
  }, [debouncedKeyword])

  useEffect(() => {
    dispatch(setCheckListFlight(columns.map(item => item.key as string)))
  }, [])

  useEffect(() => {
    navigate(
      `/flight-schedule/seasonal?${QueryString.stringify(paramsSeasonal)}`,
      { replace: true }
    )
    dispatch(setParams(paramsSeasonal))
  }, [paramsSeasonal])

  useEffect(() => {
    setParamsSeasonal({
      ...paramsSeasonal,
      skipCount: 0,
    })
    dispatch(closeFilterModal())
    form.setFieldsValue({ depApSched: airportGlobal })
  }, [airportGlobal])

  return (
    <div className="flex flex-col gap-y-4">
      {/* <div className="bg-[#F5F9FA] p-4 rounded-sm flex flex-col">
        <Tabs
          defaultActiveKey="1"
          items={tabs.filter(item => !item.hidden)}
          tabPosition="top"
          className={`${styles.tab_active} rounded-sm bg-[#E6F0F3]`}
        />
      </div> */}
      {/* <div className="text-black text-base font-bold">
        {t('departure.listFlight')}
      </div> */}
      <ModalSeasonlInformationDetail />
      <Table
        columns={newColumns}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        title={renderTitleTable}
        scroll={{ x: 'max-content' }}
        dataSource={query.data?.items || []}
        onRow={record => ({
          onClick: () => {
            dispatch(setSelectedFlightId(record.id))
          },
          onDoubleClick: () => {
            dispatch(openSeasonalInformationModal())
          },
        })}
        rowHoverable={false}
        rowClassName={record => {
          return record.id === selectedFlightId ? '!bg-[#E6F0F3]' : ''
        }}
        pagination={{
          total: query.data?.totalCount || 0,
          current: paramsSeasonal.skipCount / paramsSeasonal.maxResultCount + 1,
          pageSize: paramsSeasonal.maxResultCount,
          onChange: (page, pageSize) => {
            setParamsSeasonal({
              ...paramsSeasonal,
              skipCount: (page - 1) * pageSize,
              maxResultCount: pageSize,
            })
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        rowKey={record => record.id}
        loading={query.isLoading}
      />
    </div>
  )
}

export default SeasonalPage
