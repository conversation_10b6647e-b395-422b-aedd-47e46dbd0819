/* eslint-disable @typescript-eslint/no-explicit-any */
import { configPie } from '@/src/config/configChart'
import { PIE_CHART_OSP, PIE_CHART_OTP } from '@/src/constants'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getDashboardFlightSummary } from '@/src/service/dashboard'
import { Pie } from '@ant-design/charts'
import { useQuery } from '@tanstack/react-query'
import { Button, Card, Empty, Tag } from 'antd'
import QueryString from 'qs'
import { useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'
import { useResizeObserver } from 'usehooks-ts'

const PieChartDashboard = () => {
  const { t } = useTranslation()

  const ref = useRef<HTMLDivElement>(null)

  const { width = 0 } = useResizeObserver({
    ref,
    box: 'border-box',
  })

  const { params } = useAppSelector(state => state.dashboard)

  const navigate = useNavigate()

  const { data: flightOSPData, isLoading: isLoadingOSP } = useQuery({
    queryKey: ['get-dashboard-flight-summary-osp', params],
    queryFn: () =>
      getDashboardFlightSummary({
        type: PIE_CHART_OSP,
        ...params,
      }),
  })

  const { data: flightOTPData, isLoading: isLoadingOTP } = useQuery({
    queryKey: ['get-dashboard-flight-summary-otp', params],
    queryFn: () =>
      getDashboardFlightSummary({
        type: PIE_CHART_OTP,
        ...params,
      }),
  })

  const configPieOTP = configPie({
    data: [
      {
        type: 'LATE',
        value: flightOTPData?.data[0].datas[0].value,
        color: '#FB4E4EFF',
      },
      {
        type: 'ON_TIME',
        value: flightOTPData?.data[0].datas[1].value,
        color: '#327DFFFF',
      },
    ],
    width: width / 2,
  })

  const configPieOSP = configPie({
    data: [
      {
        type: 'LATE',
        value: flightOSPData?.data[0].datas[0].value,
        color: '#FB4E4EFF',
      },
      {
        type: 'ON_TIME',
        value: flightOSPData?.data[0].datas[1].value,
        color: '#327DFFFF',
      },
    ],
    width: width / 2,
  })

  const totalOTP = flightOTPData?.data[0].datas.reduce(
    (sum: any, item: any) => sum + item.value,
    0
  )

  const totalOSP = flightOSPData?.data[0].datas.reduce(
    (sum: any, item: any) => sum + item.value,
    0
  )

  return (
    <div className="gap-y-4 flex flex-col max-2xl:flex-row max-2xl:gap-x-4 max-2xl:w-full">
      <Card
        size="small"
        className="max-2xl:col-span-1 max-2xl:w-full"
        loading={isLoadingOTP}
        ref={ref}
      >
        <span className="text-xl font-bold">{t('dashboard.ratioOTP')}</span>
        <div className="grid grid-cols-6 items-center">
          <div className="col-span-3 flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-[#FB4E4EFF] rounded-full"></div>
                <span>{t('dashboard.departureNotMetOTP')}</span>
              </div>
              <Tag color="red" className="!rounded-full">
                {Math.round(flightOTPData?.data[0].datas[0].percent)}%
              </Tag>
            </div>
            <span>
              {t('dashboard.count')}:&nbsp;
              <span className="text-[#FB4E4EFF] font-semibold">
                {flightOTPData?.data[0].datas[0].value.toLocaleString()}
              </span>
              &nbsp;
              {t('dashboard.flight')}
            </span>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-[#327DFFFF] rounded-full"></div>
                <span>{t('dashboard.departureOTPCompliant')}</span>
              </div>
              <Tag color="red" className="!rounded-full">
                {Math.round(flightOTPData?.data[0].datas[1].percent)}%
              </Tag>
            </div>
            <span>
              {t('dashboard.count')}:&nbsp;
              <span className="text-[#327DFFFF] font-semibold">
                {flightOTPData?.data[0].datas[1].value.toLocaleString()}
              </span>
              &nbsp;
              {t('dashboard.flight')}
            </span>
          </div>
          <div className="col-span-3">
            {totalOTP > 0 ? (
              <Pie height={300} className="col-span-3" {...configPieOTP} />
            ) : (
              <Empty description="No data" />
            )}
          </div>
        </div>
        <Button
          type="primary"
          onClick={() => {
            const newParams = {
              ...params,
              type: PIE_CHART_OTP,
            }
            navigate(`/report/flight-late?${QueryString.stringify(newParams)}`)
          }}
        >
          {t('dashboard.listDepartureNotMetOTP')}
        </Button>
      </Card>
      <Card
        size="small"
        className="max-2xl:col-span-1 max-2xl:w-full"
        loading={isLoadingOSP}
      >
        <span className="text-xl font-bold">{t('dashboard.ratioOSP')}</span>
        <div className="grid grid-cols-6 items-center">
          <div className="col-span-3 flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-[#FB4E4EFF] rounded-full"></div>
                <span>{t('dashboard.departureNotMetOSP')}</span>
              </div>
              <Tag color="red" className="!rounded-full">
                {Math.round(flightOSPData?.data[0].datas[0].percent)}%
              </Tag>
            </div>
            <span>
              {t('dashboard.count')}:&nbsp;
              <span className="text-[#FB4E4EFF] font-semibold">
                {flightOSPData?.data[0].datas[0].value.toLocaleString()}&nbsp;
              </span>
              {t('dashboard.flight')}
            </span>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-[#327DFFFF] rounded-full"></div>
                <span>{t('dashboard.departureOSPCompliant')}</span>
              </div>
              <Tag color="red" className="!rounded-full">
                {Math.round(flightOSPData?.data[0].datas[1].percent)}%
              </Tag>
            </div>
            <span>
              {t('dashboard.count')}:&nbsp;
              <span className="text-[#327DFFFF] font-semibold">
                {flightOSPData?.data[0].datas[1].value.toLocaleString()}
                &nbsp;
              </span>
              {t('dashboard.flight')}
            </span>
          </div>
          <div className="col-span-3">
            {totalOSP > 0 ? (
              <Pie height={300} className="col-span-3" {...configPieOSP} />
            ) : (
              <Empty description="No data" />
            )}
          </div>
        </div>
        <Button
          onClick={() => {
            const newParams = {
              ...params,
              type: PIE_CHART_OSP,
            }
            navigate(`/report/flight-late?${QueryString.stringify(newParams)}`)
          }}
          type="primary"
        >
          {t('dashboard.listDepartureNotMetOSP')}
        </Button>
      </Card>
    </div>
  )
}

export default PieChartDashboard
