/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/src/config/axios-interceptor'
const prefix = `/api/identity/users`

const getUser = async (params: any) => {
  const api = `${prefix}`
  const response = await axios.get(api, { params })
  return response.data
}

const getUserById = async (id: string) => {
  const api = `${prefix}/${id}`
  const response = await axios.get(api)
  return response.data
}

const getUserByIdWithRoles = async (id: string) => {
  const api = `${prefix}/${id}/roles`
  const response = await axios.get(api)
  return response.data
}

const createUser = async (params: any) => {
  const api = `${prefix}`
  const response = await axios.post(api, params)
  return response.data
}

const updateUser = async (params: any) => {
  const api = `${prefix}/${params.id}`
  const response = await axios.put(api, params)
  return response.data
}

const updateUserRole = async (params: any) => {
  const api = `${prefix}/${params.id}/roles`
  const response = await axios.put(api, params)
  return response.data
}

const deleteUser = async (id: string) => {
  const api = `${prefix}/${id}`
  const response = await axios.delete(api)
  return response.data
}

const getLookUpRole = async () => {
  const api = `${prefix}/lookup/roles`
  const response = await axios.get(api)
  return response.data
}

export {
  getUser,
  getLookUpRole,
  createUser,
  getUserById,
  getUserByIdWithRoles,
  updateUser,
  deleteUser,
  updateUserRole,
}
