/* eslint-disable @typescript-eslint/no-explicit-any */
import { ISO_DATETIME } from '@/src/constants'
import { createSlice } from '@reduxjs/toolkit'
import dayjs from 'dayjs'

type ReportState = {
  visibleViewColumnFlight: boolean
  paramReportFlight: {
    skipCount: number
    maxResultCount: number
    type: number
    fromDate: string
    toDate: string
    iataCode: string
  }
  paramReportDepartment: {
    skipCount: number
    maxResultCount: number
    type: number
    fromDate: string
    toDate: string
  }
  paramReportGroupTask: {
    skipCount: number
    maxResultCount: number
    type: number
    fromDate: string
    toDate: string
  }
  checkListReportFlight: any[]
  visibleViewColumnOnTimeGT: boolean
  paramsReportOnTimeGT: {
    skipCount: number
    maxResultCount: number
    fromDate: string
    toDate: string
  }
  checkListReportOnTimeGT: any[]
  visibleViewColumnLateGT: boolean
  paramsReportLateGT: {
    skipCount: number
    maxResultCount: number
    fromDate: string
    toDate: string
  }
  checkListReportLateGT: any[]
  visibleViewColumnNoRecordPTS: boolean
  paramsReportNoRecordPTS: {
    skipCount: number
    maxResultCount: number
    fromDate: string
    toDate: string
  }
  checkListReportNoRecordPTS: any[]
  visibleModalDepartment: boolean
  selectedDepartmentId: string | null
  paramsModalDepartment: {
    skipCount: number
    maxResultCount: number
  }
  selectedUnit: any
  paramDepartmentDetail: {
    maxResultCount: number
    fromDate: string
    toDate: string
    executeOUId: string
    airport: string
  }
}

const initialState: ReportState = {
  visibleViewColumnFlight: false,
  paramReportFlight: {
    skipCount: 0,
    maxResultCount: 20,
    type: 1,
    fromDate: dayjs().startOf('day').format(ISO_DATETIME),
    toDate: dayjs().endOf('day').format(ISO_DATETIME),
    iataCode: '',
  },
  paramReportDepartment: {
    skipCount: 0,
    maxResultCount: 20,
    type: 1,
    fromDate: dayjs().startOf('day').format(ISO_DATETIME),
    toDate: dayjs().endOf('day').format(ISO_DATETIME),
  },
  paramReportGroupTask: {
    skipCount: 0,
    maxResultCount: 20,
    type: 1,
    fromDate: dayjs().startOf('day').format(ISO_DATETIME),
    toDate: dayjs().endOf('day').format(ISO_DATETIME),
  },
  checkListReportFlight: [],
  visibleViewColumnOnTimeGT: false,
  paramsReportOnTimeGT: {
    skipCount: 0,
    maxResultCount: 20,
    fromDate: dayjs().startOf('day').format(ISO_DATETIME),
    toDate: dayjs().endOf('day').format(ISO_DATETIME),
  },
  checkListReportOnTimeGT: [],
  visibleViewColumnLateGT: false,
  paramsReportLateGT: {
    skipCount: 0,
    maxResultCount: 20,
    fromDate: dayjs().startOf('day').format(ISO_DATETIME),
    toDate: dayjs().endOf('day').format(ISO_DATETIME),
  },
  checkListReportLateGT: [],
  visibleViewColumnNoRecordPTS: false,
  paramsReportNoRecordPTS: {
    skipCount: 0,
    maxResultCount: 20,
    fromDate: dayjs().startOf('day').format(ISO_DATETIME),
    toDate: dayjs().endOf('day').format(ISO_DATETIME),
  },
  checkListReportNoRecordPTS: [],
  visibleModalDepartment: false,
  selectedDepartmentId: null,
  paramsModalDepartment: {
    skipCount: 0,
    maxResultCount: 20,
  },
  selectedUnit: null,
  paramDepartmentDetail: {
    maxResultCount: 20,
    fromDate: dayjs().startOf('day').format(ISO_DATETIME),
    toDate: dayjs().endOf('day').format(ISO_DATETIME),
    executeOUId: '',
    airport: '',
  },
}

const reportSlice = createSlice({
  name: 'report',
  initialState,
  reducers: {
    openViewColumnFlight(state) {
      state.visibleViewColumnFlight = true
    },
    closeViewColumnFlight(state) {
      state.visibleViewColumnFlight = false
    },
    setParamsReportFlight(state, action) {
      state.paramReportFlight = {
        ...state.paramReportFlight,
        ...action.payload,
      }
    },
    setParamsReportDepartment(state, action) {
      state.paramReportDepartment = {
        ...state.paramReportDepartment,
        ...action.payload,
      }
    },
    setParamsReportGroupTask(state, action) {
      state.paramReportGroupTask = {
        ...state.paramReportGroupTask,
        ...action.payload,
      }
    },
    setCheckListReportFlight(state, action) {
      state.checkListReportFlight = action.payload
    },
    setCheckListReportOnTimeGT(state, action) {
      state.checkListReportOnTimeGT = action.payload
    },
    openViewColumnOnTimeGT(state) {
      state.visibleViewColumnOnTimeGT = true
    },
    closeViewColumnOnTimeGT(state) {
      state.visibleViewColumnOnTimeGT = false
    },
    setCheckListReportLateGT(state, action) {
      state.checkListReportLateGT = action.payload
    },
    openViewColumnLateGT(state) {
      state.visibleViewColumnLateGT = true
    },
    closeViewColumnLateGT(state) {
      state.visibleViewColumnLateGT = false
    },
    setCheckListReportNoRecordPTS(state, action) {
      state.checkListReportNoRecordPTS = action.payload
    },
    openViewColumnNoRecordPTS(state) {
      state.visibleViewColumnNoRecordPTS = true
    },
    closeViewColumnNoRecordPTS(state) {
      state.visibleViewColumnNoRecordPTS = false
    },
    openModalDepartment(state) {
      state.visibleModalDepartment = true
    },
    closeModalDepartment(state) {
      state.visibleModalDepartment = false
    },
    setSelectedDepartmentId(state, action) {
      state.selectedDepartmentId = action.payload
    },
    setParamsModalDepartment(state, action) {
      state.paramsModalDepartment = {
        ...state.paramsModalDepartment,
        ...action.payload,
      }
    },
    setSelectedUnit(state, action) {
      state.selectedUnit = action.payload
    },
    resetState() {
      return initialState
    },
    setParamsDepartmentDetail(state, action) {
      state.paramDepartmentDetail = {
        ...state.paramDepartmentDetail,
        ...action.payload,
      }
    },
  },
})

export const {
  openViewColumnFlight,
  closeViewColumnFlight,
  setParamsReportFlight,
  setParamsReportDepartment,
  setParamsReportGroupTask,
  setCheckListReportFlight,
  resetState,
  setCheckListReportOnTimeGT,
  openViewColumnOnTimeGT,
  closeViewColumnOnTimeGT,
  setCheckListReportLateGT,
  openViewColumnLateGT,
  closeViewColumnLateGT,
  setCheckListReportNoRecordPTS,
  openViewColumnNoRecordPTS,
  closeViewColumnNoRecordPTS,
  openModalDepartment,
  closeModalDepartment,
  setSelectedDepartmentId,
  setParamsModalDepartment,
  setSelectedUnit,
  setParamsDepartmentDetail,
} = reportSlice.actions
export default reportSlice.reducer
