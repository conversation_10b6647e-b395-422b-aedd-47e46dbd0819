/* eslint-disable react-hooks/exhaustive-deps */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getRoleAll } from '@/src/service/role'
import {
  createUser,
  deleteUser,
  getUser,
  getUserById,
  getUserByIdWithRoles,
  updateUser,
  updateUserRole,
} from '@/src/service/user'
import {
  closeChangePasswordModal,
  closeDeleteUserModal,
  closeUserModal,
  openChangePasswordModal,
  openDeleteUserModal,
  openUserModal,
  resetState,
  setKeyWordDebounce,
  setParams,
  setSelectedUser,
  setTabActive,
} from '@/src/store/UserSlice'
import { PlusOutlined, SearchOutlined, SettingFilled } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  But<PERSON>,
  Checkbox,
  Col,
  Dropdown,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Row,
  Table,
  Tabs,
  type TabsProps,
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss'
// import { ISO_DATE } from '@/src/constants'
import ShowTotal from '@/src/components/Showtotal'
import { PHONE_NUMBER_PATTERN, TIME_DEBOUNCE } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import useDebounce from '@/src/hooks/useDebounce'
// import type { ILoginProvider } from '@/src/schema/ILoginProvider'
// import { getLoginProvider } from '@/src/service/login_provider'
import {
  ABP_IDENTITY_USERS_CHANGE_PASSWORD,
  ABP_IDENTITY_USERS_CREATE,
  ABP_IDENTITY_USERS_DELETE,
  ABP_IDENTITY_USERS_UPDATE,
} from '@/src/constants/permission'
import usePermission from '@/src/hooks/usePermission'
import type { ItemType } from 'antd/es/menu/interface'
import { useEffect } from 'react'

interface Role {
  id: string
  name: string
}

interface User {
  id: string
  userName: string
  email: string
  phoneNumber: string
  name: string
  isActive: boolean
  isLockedOut: boolean
  emailConfirmed: boolean
  twoFactorEnabled: boolean
  accessFailedCount: number
  creationTime: string
  lastModificationTime: string
  roleNames: string[]
}

interface UserFormValues {
  userName: string
  name: string
  password?: string
  email: string
  phoneNumber: string
  isActive: boolean
  lockoutEnabled: boolean
  emailConfirmed: boolean
  phoneNumberConfirmed: boolean
  roleNames: string[]
}

const UserPage = () => {
  const { t } = useTranslation()
  const [userForm] = Form.useForm<UserFormValues>()
  // const [filterForm] = Form.useForm()
  const [changePasswordForm] = Form.useForm<{ password: string }>()
  const {
    visibleUserModal,
    params,
    selectedUser,
    tabActive,
    visibleChangePasswordModal,
    visibleDeleteUserModal,
    KeyWordDebounce,
  } = useAppSelector(state => state.user)

  const dispatch = useAppDispatch()
  const { hasPermission } = usePermission()
  const filterDebounce = useDebounce(KeyWordDebounce, TIME_DEBOUNCE)

  const queryUsers = useQuery({
    queryKey: ['users', params],
    queryFn: () => getUser(params),
  })

  const queryUserById = useQuery({
    queryKey: ['users', selectedUser],
    queryFn: () => getUserById(selectedUser),
    enabled: !!selectedUser,
  })

  const queryUserByIdWithRoles = useQuery({
    queryKey: ['users', selectedUser, 'roles'],
    queryFn: () => getUserByIdWithRoles(selectedUser),
    enabled: !!selectedUser,
  })

  const queryRole = useQuery({
    queryKey: ['roles'],
    queryFn: getRoleAll,
  })

  // const { data: loginProviderData } = useQuery({
  //   queryKey: ['login-provider'],
  //   queryFn: () => getLoginProvider(),
  //   enabled: !!visibleUserModal,
  // })

  const mutationCreateUser = useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      queryUsers.refetch()
      userForm.resetFields()
      dispatch(closeUserModal())
      message.success({ content: t('user.userCreateSuccess') })
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationUpdateUser = useMutation({
    mutationFn: updateUser,
    onSuccess: () => {
      queryUsers.refetch()
      userForm.resetFields()
      dispatch(closeUserModal())
      dispatch(setSelectedUser(''))
      message.success({ content: t('user.userUpdateSuccess') })
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationUpdateUserRole = useMutation({
    mutationFn: updateUserRole,
    onSuccess: () => {
      // queryUsers.refetch()
      // userForm.resetFields()
      // dispatch(closeUserModal())
      // dispatch(setSelectedUser(''))
      // message.success({ content: t('user.userUpdateSuccess') })
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const mutationDeleteUser = useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      queryUsers.refetch()
      dispatch(closeDeleteUserModal())
      message.success({ content: t('user.userDeleteSuccess') })
      dispatch(setSelectedUser(''))
      userForm.resetFields()
    },
    onError: error => {
      handleApiError(error)
    },
  })

  const columns: ColumnsType<User> = [
    {
      title: t('user.action'),
      align: 'center',
      width: 100,
      hidden: !hasPermission([
        ABP_IDENTITY_USERS_UPDATE,
        ABP_IDENTITY_USERS_CHANGE_PASSWORD,
        ABP_IDENTITY_USERS_DELETE,
      ]),
      render: (_value, record) => (
        <Dropdown
          menu={{
            items: [
              hasPermission(ABP_IDENTITY_USERS_UPDATE) && {
                label: t('common.edit'),
                key: 'edit',
                onClick: () => {
                  dispatch(setSelectedUser(record.id))
                  dispatch(openUserModal())
                },
              },
              hasPermission(ABP_IDENTITY_USERS_CHANGE_PASSWORD) && {
                label: t('user.changePassword'),
                key: 'change-password',
                onClick: () => {
                  dispatch(setSelectedUser(record.id))
                  dispatch(openChangePasswordModal())
                },
              },
              hasPermission(ABP_IDENTITY_USERS_DELETE) && {
                label: t('common.delete'),
                key: 'delete',
                onClick: () => {
                  dispatch(setSelectedUser(record.id))
                  dispatch(openDeleteUserModal())
                },
              },
            ].filter(Boolean) as ItemType[],
          }}
          placement="bottomLeft"
        >
          <Button icon={<SettingFilled />} type="primary">
            {t('user.action')}
          </Button>
        </Dropdown>
      ),
    },
    {
      title: t('user.userName'),
      dataIndex: 'userName',
    },
    {
      title: t('user.email'),
      dataIndex: 'email',
    },
    {
      title: t('user.phoneNumber'),
      dataIndex: 'phoneNumber',
      align: 'center',
    },
    {
      title: t('user.name'),
      dataIndex: 'name',
    },
    // {
    //   title: t('user.active'),
    //   dataIndex: 'isActive',
    //   render: (text: boolean) =>
    //     text ? <CheckOutlined className="text-green-500" /> : <CloseOutlined />,
    //   align: 'center',
    // },
    // {
    //   title: t('user.accountLockout'),
    //   dataIndex: 'isLockedOut',
    //   render: (text: boolean) =>
    //     text ? (
    //       <CloseOutlined className="text-red-500" />
    //     ) : (
    //       <CheckOutlined className="text-green-500" />
    //     ),
    //   align: 'center',
    // },
    // {
    //   title: t('user.emailConfirmed'),
    //   dataIndex: 'emailConfirmed',
    //   render: (text: boolean) =>
    //     text ? (
    //       <CheckOutlined className="text-green-500" />
    //     ) : (
    //       <CloseOutlined className="text-red-500" />
    //     ),
    //   align: 'center',
    // },
    // {
    //   title: t('user.twoFactorEnabled'),
    //   dataIndex: 'twoFactorEnabled',
    //   render: (text: boolean) =>
    //     text ? (
    //       <CheckOutlined className="text-green-500" />
    //     ) : (
    //       <CloseOutlined className="text-red-500" />
    //     ),
    //   align: 'center',
    // },
    // {
    //   title: t('user.accessFailedCount'),
    //   dataIndex: 'accessFailedCount',
    //   align: 'center',
    // },
    {
      title: t('user.creationTime'),
      dataIndex: 'creationTime',
      render: (text: string) => dayjs(text).format('DD/MM/YYYY HH:mm:ss'),
      align: 'center',
    },
    {
      title: t('user.lastModificationTime'),
      dataIndex: 'lastModificationTime',
      render: (text: string) => dayjs(text).format('DD/MM/YYYY HH:mm:ss'),
      align: 'center',
    },
  ]

  const tabs = [
    {
      key: 'user-info',
      label: t('user.userInfo'),
      children: (
        <div>
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item
                label={t('user.userName')}
                name="userName"
                rules={[
                  { required: true, message: t('user.userNameRequired') },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label={t('user.name')} name="name">
                <Input />
              </Form.Item>
            </Col>
            {!selectedUser && (
              <Flex className="w-full">
                <Col span={24}>
                  <Form.Item
                    label={t('user.password')}
                    name="password"
                    rules={[
                      { required: true, message: t('user.passwordRequired') },
                    ]}
                  >
                    <Input.Password />
                  </Form.Item>
                </Col>
                {/* <Col span={8}>
                  <Form.Item
                    label=" "
                    name="loginProvider"
                    className="w-full"
                    initialValue={0}
                  >
                    <Select
                      className="!w-full"
                      options={loginProviderData?.map(
                        (item: ILoginProvider) => ({
                          value: item.value,
                          label: item.displayName,
                        })
                      )}
                    />
                  </Form.Item>
                </Col> */}
              </Flex>
            )}
            <Col span={24}>
              <Form.Item
                label={t('user.email')}
                name="email"
                rules={[
                  { required: true, message: t('user.emailRequired') },
                  { type: 'email', message: t('user.emailInvalid') },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label={t('user.phoneNumber')}
                name="phoneNumber"
                rules={[
                  {
                    pattern: PHONE_NUMBER_PATTERN,
                    message: t('user.phoneNumberInvalid'),
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name="isActive" valuePropName="checked">
                <Checkbox>{t('user.active')}</Checkbox>
              </Form.Item>
            </Col>
            {/* {selectedUser && (
              <Col span={24}>
                <Form.Item name="loginProvider" valuePropName="checked">
                  <Checkbox>{t('user.loginLdap')}</Checkbox>
                </Form.Item>
              </Col>
            )} */}
            {/*<Col span={12}>
              <Form.Item name="lockoutEnabled" valuePropName="checked">
                <Checkbox>
                  <Flex gap={4}>
                    {t('user.accountLockout')}
                    <Tooltip title={t('user.accountLockoutTooltip')}>
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Flex>
                </Checkbox>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="emailConfirmed" valuePropName="checked">
                <Checkbox>{t('user.emailConfirmed')}</Checkbox>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="phoneNumberConfirmed" valuePropName="checked">
                <Checkbox>{t('user.phoneNumberConfirmed')}</Checkbox>
              </Form.Item>
            </Col> */}
          </Row>
        </div>
      ),
    },
    hasPermission(ABP_IDENTITY_USERS_UPDATE) && {
      key: 'roles',
      label: t('user.roles'),
      children: (
        <div>
          <Form.Item name="roleNames">
            <Checkbox.Group
              className="flex flex-col gap-1"
              options={queryRole.data?.items.map((item: Role) => ({
                label: item.name,
                value: item.name,
              }))}
            />
          </Form.Item>
        </div>
      ),
    },
  ]

  // const yesNoOptions = [
  //   { label: 'Yes', value: true },
  //   { label: 'No', value: false }
  // ]

  // const handleFilterApply = () => {
  //   const value = filterForm.getFieldsValue()
  //   dispatch(setParams({
  //     ...params,
  //     roleId: value.roleId,
  //     organizationUnitId: value.organizationUnitId,
  //     userName: value.userName,
  //     name: value.name,
  //     creationTimeFrom: dayjs(value.creationTime[0]).format(ISO_DATE),
  //     creationTimeTo: dayjs(value.creationTime[1]).format(ISO_DATE),
  //     lastModificationTimeFrom: dayjs(value.lastModificationTime[0]).format(ISO_DATE),
  //     lastModificationTimeTo: dayjs(value.lastModificationTime[1]).format(ISO_DATE),
  //     phoneNumber: value.phoneNumber,
  //     emailAddress: value.emailAddress,
  //     notActive: value.notActive,
  //     emailConfirmed: value.emailConfirmed,
  //     isLockedOut: value.isLockedOut,
  //     isExternal: value.isExternal,
  //   }))
  //   dispatch(closePopoverFilter())
  // }

  const onSubmit = async () => {
    await userForm.validateFields()
    const values: UserFormValues = userForm.getFieldsValue()

    if (selectedUser) {
      if (values.roleNames) {
        await mutationUpdateUserRole.mutateAsync({
          ...values,
          id: selectedUser,
        })
      }
      mutationUpdateUser.mutateAsync({
        ...values,
        id: selectedUser,
        lockoutEnabled: true,
      })
      dispatch(setTabActive('user-info'))
    } else {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const res: any = await mutationCreateUser.mutateAsync({
        ...values,
        lockoutEnabled: true,
      })
      await mutationUpdateUserRole.mutateAsync({
        id: res.id,
        roleNames: values.roleNames ?? [],
      })
      dispatch(setTabActive('user-info'))
    }
  }

  const onChangePassword = async () => {
    const values = await changePasswordForm.validateFields()
    mutationUpdateUser.mutate({
      ...queryUserById.data,
      ...values,
      id: selectedUser,
    })
    dispatch(closeChangePasswordModal())
    changePasswordForm.resetFields()
  }

  const handleCloseUserModal = () => {
    dispatch(closeUserModal())
    dispatch(setSelectedUser(''))
    dispatch(setTabActive('user-info'))
    userForm.resetFields()
  }

  useEffect(() => {
    if (queryUserById.data) {
      userForm.setFieldsValue({
        ...queryUserById.data,
        roleNames:
          queryUserByIdWithRoles.data?.items.map((item: Role) => item.name) ||
          [],
      })
    }
  }, [queryUserById.data, queryUserByIdWithRoles.data])

  useEffect(() => {
    dispatch(resetState())
  }, [])

  useEffect(() => {
    dispatch(setParams({ ...params, SkipCount: 0, Filter: filterDebounce }))
  }, [filterDebounce])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="w-full flex justify-between">
        <div className="text-lg font-bold text-black">{t('user.userList')}</div>
        <div className="flex flex-row gap-x-2">
          {hasPermission(ABP_IDENTITY_USERS_CREATE) && (
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => dispatch(openUserModal())}
            >
              {t('user.userCreate')}
            </Button>
          )}
          <Input
            prefix={<SearchOutlined />}
            placeholder={t('user.searchPlaceholder')}
            onChange={e => {
              dispatch(setKeyWordDebounce(e.target.value))
            }}
          />
          {/* <Popover
            open={visiblePopoverFilter}
            placement="bottomRight"
            trigger={['click']}
            content={
              <Form form={filterForm} layout="vertical" className="w-[500px]">
                <Row gutter={[16, 0]}>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.role')} name="roleId">
                      <Select
                        options={queryRole.data?.items.map((item: Role) => ({
                          label: item.name,
                          value: item.id
                        }))}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.organizationUnit')} name="organizationUnitId">
                      <Select />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.userName')} name="userName">
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.name')} name="name">
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.creationDate')} name="creationTime">
                      <DatePicker.RangePicker />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.modificationDate')} name="lastModificationTime">
                      <DatePicker.RangePicker />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.phoneNumber')} name="phoneNumber">
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.email')} name="emailAddress">
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.notActive')} name="notActive">
                      <Select options={yesNoOptions} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.emailConfirmed')} name="emailConfirmed">
                      <Select options={yesNoOptions} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.lock')} name="isLockedOut">
                      <Select options={yesNoOptions} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('user.filter.externalUser')} name="isExternal">
                      <Select options={yesNoOptions} />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Flex justify="flex-end" gap={16}>
                      <Button onClick={() => dispatch(closePopoverFilter())}>
                        {t('common.cancel')}
                      </Button>
                      <Button type="primary" onClick={handleFilterApply}>
                        {t('common.apply')}
                      </Button>
                    </Flex>
                  </Col>
                </Row>
              </Form>
            }
          >
            <Button
              icon={<FilterFilled />}
              onClick={() => dispatch(openPopoverFilter())}
            >
              {t('common.filter')}
            </Button>
          </Popover> */}
        </div>
      </div>
      <Table
        dataSource={queryUsers.data?.items || []}
        columns={columns}
        bordered
        size="small"
        className={styles.whiteHeader}
        pagination={{
          total: queryUsers.data?.totalCount || 0,
          current: params.SkipCount / params.MaxResultCount + 1,
          pageSize: params.MaxResultCount,
          onChange: (page, pageSize) => {
            dispatch(
              setParams({
                SkipCount: (page - 1) * pageSize,
                MaxResultCount: pageSize,
              })
            )
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        rowKey="id"
        loading={queryUsers.isLoading}
      />
      <Modal
        loading={queryUserById.isLoading}
        open={visibleUserModal}
        title={selectedUser ? t('user.userEdit') : t('user.userCreate')}
        onOk={onSubmit}
        onCancel={handleCloseUserModal}
        closable={false}
        className="!h-56"
      >
        <Form form={userForm} layout="vertical">
          <Tabs
            items={tabs.filter(Boolean) as TabsProps['items']}
            activeKey={tabActive}
            onChange={key => dispatch(setTabActive(key))}
          />
        </Form>
      </Modal>
      <Modal
        open={visibleChangePasswordModal}
        title={t('user.changePassword')}
        onOk={onChangePassword}
        onCancel={() => {
          dispatch(closeChangePasswordModal())
          changePasswordForm.resetFields()
          dispatch(setSelectedUser(''))
          userForm.resetFields()
        }}
        closable={false}
        className="!h-56"
      >
        <Form form={changePasswordForm} layout="vertical">
          <Form.Item label={t('user.password')} name="password">
            <Input.Password />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        open={visibleDeleteUserModal}
        title={t('user.userDelete')}
        onOk={() => mutationDeleteUser.mutate(selectedUser)}
        onCancel={() => {
          dispatch(closeDeleteUserModal())
          dispatch(setSelectedUser(''))
          userForm.resetFields()
        }}
        closable={false}
      >
        <div>{t('user.confirmDelete')}</div>
      </Modal>
    </div>
  )
}

export default UserPage
