import Breadcrumb from '@/src/components/Breadcrumb'
import ErrorBoundaryWrapper from '@/src/components/ErrorBoundary'
import { ACCESS_TOKEN } from '@/src/constants'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import HeaderComponent from '@/src/layout/Main/HeaderComponent'
import ModalSelectAirport from '@/src/layout/Main/ModalSelectAirport'
import SiderComponent from '@/src/layout/Main/SiderComponent'
import Splash from '@/src/pages/Splash'
import { getCurrentUser } from '@/src/service/permission'
import { setCurrentUser } from '@/src/store/AuthSlice'
import { useQuery } from '@tanstack/react-query'
import { Layout } from 'antd'
import cookie from 'cookiejs'
import { useEffect, useState } from 'react'
import { Navigate, Outlet } from 'react-router'

const { Content } = Layout

const MainLayout = () => {
  const dispatch = useAppDispatch()
  const [loading, setIsLoading] = useState<boolean>(false)

  const { data: currentUser } = useQuery({
    queryKey: ['currentUser'],
    queryFn: getCurrentUser,
  })

  const access_token =
    cookie.get(ACCESS_TOKEN) || sessionStorage.getItem(ACCESS_TOKEN)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(true)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    if (currentUser) {
      dispatch(setCurrentUser({ permission: currentUser }))
    }
  }, [currentUser])

  if (!loading) {
    return <Splash />
  }

  if (!access_token) {
    return <Navigate to={'/login-ldap'} replace />
  }

  return (
    <Layout className="!bg-white">
      <SiderComponent />
      <Layout>
        <Content className="py-2 px-6 bg-white h-screen overflow-y-auto scrollbar">
          <HeaderComponent />
          <Breadcrumb />
          <ModalSelectAirport />
          <ErrorBoundaryWrapper>
            <Outlet />
          </ErrorBoundaryWrapper>
        </Content>
      </Layout>
    </Layout>
  )
}

export default MainLayout
