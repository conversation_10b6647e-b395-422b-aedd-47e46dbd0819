import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { toggleOrganizationUnitRoleModal } from '@/src/store/OrganizationUnitSlice'
import { Modal, Table } from 'antd'
import Search from 'antd/es/input/Search'
import type { TableRowSelection } from 'antd/es/table/interface'
import type { TableProps } from 'antd/lib'
import { useState } from 'react'

interface Role {
  key: string
  roleName: string
}

const memberColumns: TableProps<Role>['columns'] = [
  {
    title: 'Vai trò',
    dataIndex: 'roleName',
    key: 'roleName',
  },
]

const memberData: Role[] = [
  { key: '1', roleName: 'Quản trị viên' },
  { key: '2', roleName: 'Kế toán trưởng' },
  { key: '3', roleName: 'Nhân viên bán hàng' },
  { key: '4', roleName: 'Nhân viên kho' },
  { key: '5', roleName: 'Quản lý dự án' },
  { key: '6', roleName: 'Nhân viên hỗ trợ' },
  { key: '7', roleName: 'Trưởng phòng nhân sự' },
  { key: '8', roleName: 'Nhân viên IT' },
  { key: '9', roleName: 'Giám đốc' },
  { key: '10', roleName: 'Nhân viên marketing' },
]

const AddRoleModal = () => {
  const { organizationUnitRoles } = useAppSelector(
    state => state.organizationUnit
  )

  const dispatch = useAppDispatch()

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys)
  }

  const rowSelection: TableRowSelection<Role> = {
    selectedRowKeys,
    onChange: onSelectChange,
  }

  const toggleModal = () => {
    dispatch(toggleOrganizationUnitRoleModal())
  }

  const handleOk = () => {
    toggleModal()
  }

  return (
    <Modal
      open={organizationUnitRoles.visibleAddModal}
      title="Phân quyền"
      okText="Xác nhận"
      cancelText="Huỷ"
      onCancel={toggleModal}
      onOk={handleOk}
      width={800}
    >
      <Search enterButton size="large" style={{ paddingBottom: 10 }} />
      <Table
        rowSelection={rowSelection}
        columns={memberColumns}
        dataSource={memberData}
        size="small"
        bordered
        pagination={false}
      />
    </Modal>
  )
}

export default AddRoleModal
