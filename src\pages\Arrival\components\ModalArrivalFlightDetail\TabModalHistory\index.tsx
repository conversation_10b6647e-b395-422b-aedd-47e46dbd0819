/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getHistory, getHistoryType } from '@/src/service/history'
import { SearchOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { Col, Input, Select, Table, type TableProps } from 'antd'
import moment from 'moment'
import styles from './index.module.scss'
import { useState, useEffect } from 'react'
import useDebounce from '@/src/hooks/useDebounce'
import ShowTotal from '@/src/components/Showtotal'
import { setParamsHistory } from '@/src/store/ModalArrivalFlightSlice'
import dayjs from 'dayjs'

const formatLogString = (log: string): string => {
  const regex = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/g
  return log.replace(regex, match => dayjs(match).format('DD/MM/YYYY HH:mm'))
}

const TabModalHistory = () => {
  const { selectedFlightId } = useAppSelector(state => state.arrival)
  const { paramsHistory } = useAppSelector(state => state.modalArrivalFlight)
  const dispatch = useAppDispatch()

  const [searchValue, setSearchValue] = useState('')
  const debouncedSearch = useDebounce(searchValue, 500)

  useEffect(() => {
    dispatch(
      setParamsHistory({
        ...paramsHistory,
        keyWord: debouncedSearch || undefined,
        skipCount: 0,
      })
    )
  }, [debouncedSearch])

  const columns: TableProps<any>['columns'] = [
    {
      title: 'Date',
      width: '15%',
      dataIndex: 'date',
      align: 'center',
      render: (date: any) => moment(date).format('DD/MM/YYYY HH:mm:ss'),
    },
    { title: 'Update by', width: '15%', dataIndex: 'updatedBy' },
    { title: 'Action', width: '10%', dataIndex: 'action', align: 'center' },
    {
      title: 'Content',
      dataIndex: 'content',
      render: (text: any) => formatLogString(text),
    },
    { title: 'Update reason', dataIndex: 'updateReason', width: '20%' },
    // { title: '', width: '48px' },
  ]

  const { data, isLoading } = useQuery({
    queryKey: ['history-list', selectedFlightId, paramsHistory],
    queryFn: () => getHistory(selectedFlightId as string, paramsHistory),
  })

  const { data: historyType } = useQuery({
    queryKey: ['history-type'],
    queryFn: () => getHistoryType(),
  })

  return (
    <div className="bg-[#F0F1F3FF] p-4 min-h-[calc(65vh)] flex flex-col">
      <div className="flex flex-col gap-y-4">
        <div className="flex gap-x-4">
          <Input
            prefix={<SearchOutlined />}
            className="!w-1/4"
            placeholder="Search"
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
          />
          <Select
            className="!w-44"
            placeholder="Type"
            allowClear
            options={historyType?.map((field: string) => ({
              label: field,
              value: field,
            }))}
            onChange={value => {
              dispatch(
                setParamsHistory({
                  ...paramsHistory,
                  updatedField: value || undefined,
                  skipCount: 0,
                })
              )
            }}
          />
        </div>
        <Col span={24}>
          <Table
            size="small"
            bordered
            loading={isLoading}
            columns={columns}
            className={`${styles.whiteHeader}`}
            dataSource={data?.items || []}
            pagination={{
              total: data?.totalCount,
              current:
                paramsHistory.skipCount / paramsHistory.maxResultCount + 1,
              pageSize: paramsHistory.maxResultCount,
              onChange: (page, pageSize) => {
                dispatch(
                  setParamsHistory({
                    ...paramsHistory,
                    skipCount: (page - 1) * pageSize,
                    maxResultCount: pageSize,
                  })
                )
              },
              showTotal: (total, range) => (
                <ShowTotal total={total} range={range} />
              ),
            }}
          />
        </Col>
      </div>
    </div>
  )
}

export default TabModalHistory
