/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import ShowTotal from '@/src/components/Showtotal'
import { DISPLAY_DATE, ISO_DATETIME } from '@/src/constants'
import { normalizeText } from '@/src/helper/normalizeText'
import { isValidRangeDate } from '@/src/helper/validDate'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import useDebounce from '@/src/hooks/useDebounce'
import ModalDepartment from '@/src/pages/ReportDepartmentLate/ModalDepartment'
import { getDepartments } from '@/src/service/organization_unit'
import {
  exportExcelReportOrganizationUnitLate,
  getReportOrganizationUnitLate,
} from '@/src/service/report'
import {
  openModalDepartment,
  setParamsDepartmentDetail,
  setSelectedUnit,
} from '@/src/store/ReportSlice'
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  DatePicker,
  Input,
  Select,
  Space,
  Table,
  type TableColumnsType,
} from 'antd'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import QueryString from 'qs'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import styles from './index.module.scss'
import { convertTime } from '@/src/helper/convertTime'

const { RangePicker } = DatePicker

type Unit = {
  organizationUnitId: string
  name: string
  children: Unit[]
}

const flattenUnits = (units: Unit[]): { label: string; value: string }[] => {
  return units.flatMap(unit => [
    { label: unit.name, value: unit.organizationUnitId },
    ...flattenUnits(unit.children || []),
  ])
}

const ReportDepartmentLatePage = () => {
  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search
  const navigate = useNavigate()

  const [paramDepartmentLate, setParamDepartmentLate] = useState<{
    skipCount: number
    maxResultCount: number
    fromDate: string
    toDate: string
    executeOUId: string
    airport: string
  }>(() => {
    const params = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof params.fromDate === 'string' ? dayjs(params.fromDate) : null
    const to = typeof params.toDate === 'string' ? dayjs(params.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...params,
      skipCount: Number(params.skipCount) || 0,
      maxResultCount: Number(params.maxResultCount) || 20,
      fromDate:
        valid && from
          ? from.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
    }

    return value
  })

  const { data: unitData, isLoading: isLoadingUnit } = useQuery({
    queryKey: ['get-unit-list'],
    queryFn: () => getDepartments(),
  })

  const flattenedUnits = unitData ? flattenUnits(unitData) : []

  const [airportKeyword, setAirportKeyword] = useState<undefined | string>(
    undefined
  )
  const debouncedAirportKeyword = useDebounce(airportKeyword, 500)
  const { t } = useTranslation()
  const dispatch = useAppDispatch()

  const columns: TableColumnsType<any> = [
    {
      title: t('table.order'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_value, _record, index) =>
        paramDepartmentLate.skipCount + index + 1,
    },
    {
      title: t('table.department'),
      dataIndex: 'organizationName',
      key: 'organizationName',
      width: 150,
      align: 'center',
    },
    {
      title: t('table.ptsGroupTask'),
      dataIndex: 'groupPtsTaskName',
      key: 'groupPtsTaskName',
      width: 150,
      align: 'center',
    },
    {
      title: t('table.overTimeCount'),
      dataIndex: 'lateCount',
      key: 'lateCount',
      width: 100,
      align: 'center',
    },
    {
      title: t('table.totalTimeDelay'),
      key: 'totalLateDuration',
      dataIndex: 'totalLateDuration',
      width: 120,
      align: 'center',
      render: time => <>{convertTime(time)}</>,
    },
    {
      title: t('table.action'),
      key: 'action',
      width: 120,
      align: 'center',
      render: (_value, record) => (
        <Space>
          <Button
            type="primary"
            onClick={() => {
              dispatch(setSelectedUnit(record))
              dispatch(openModalDepartment())
            }}
          >
            {t('common.detail')}
          </Button>
        </Space>
      ),
    },
  ]

  const { data, isLoading } = useQuery({
    queryKey: ['report-flight-unit-late', paramDepartmentLate],
    queryFn: () => getReportOrganizationUnitLate(paramDepartmentLate),
  })

  const { refetch: exportExcelReport, isLoading: isLoadingExport } = useQuery({
    queryKey: ['report-unit-late', paramDepartmentLate],
    queryFn: () =>
      exportExcelReportOrganizationUnitLate({
        ...paramDepartmentLate,
        skipCount: 0,
      }),
    enabled: false,
  })

  const handleExport = async () => {
    const { data } = await exportExcelReport()
    const fileName = `${t('common.reportName')}_${normalizeText(t('report.reportDepartmentLate'))}.csv`
    return FileSaver.saveAs(data, fileName)
  }

  useEffect(() => {
    if (airportKeyword !== undefined) {
      setParamDepartmentLate({
        ...paramDepartmentLate,
        skipCount: 0,
        airport: debouncedAirportKeyword as string,
      })
    }
  }, [debouncedAirportKeyword])

  useEffect(() => {
    navigate(
      `/report/department-late?${QueryString.stringify(paramDepartmentLate)}`,
      { replace: true }
    )
    dispatch(setParamsDepartmentDetail(paramDepartmentLate))
  }, [paramDepartmentLate])

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex justify-between w-full">
        <div className="text-lg font-bold text-black">
          {t('report.reportDepartmentLate')}
        </div>
        <div className="flex flex-row gap-x-2">
          <RangePicker
            className="!h-max"
            allowClear={false}
            defaultValue={[
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('fromDate'))
                : dayjs().startOf('day'),
              isValidRangeDate(
                dayjs(searchParams.get('fromDate')),
                dayjs(searchParams.get('toDate'))
              )
                ? dayjs(searchParams.get('toDate'))
                : dayjs().endOf('day'),
            ]}
            format={DISPLAY_DATE}
            onChange={value => {
              if (value) {
                const newParams = {
                  ...paramDepartmentLate,
                  skipCount: 0,
                  fromDate: dayjs(value[0]).startOf('day').format(ISO_DATETIME),
                  toDate: dayjs(value[1]).endOf('day').format(ISO_DATETIME),
                }
                return setParamDepartmentLate(newParams)
              }
            }}
          />
          <Input
            placeholder={t('report.searchAirportPlaceholder')}
            className="!w-48 h-max"
            prefix={<SearchOutlined />}
            onChange={e => {
              setAirportKeyword(e.target.value)
            }}
            defaultValue={searchParams.get('airport') || ''}
          />
          <Select
            loading={isLoadingUnit}
            showSearch
            defaultValue={paramDepartmentLate.executeOUId}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            className="w-40"
            placeholder={t('report.allDepartmentPlaceholder')}
            options={[
              { value: '', label: `${t('report.allDepartmentPlaceholder')}` },
              ...flattenedUnits.map((item: any) => ({
                label: item.label,
                value: item.value,
              })),
            ]}
            onSelect={val => {
              setParamDepartmentLate({
                ...paramDepartmentLate,
                executeOUId: val,
              })
            }}
          />
          <Button
            icon={<DownloadOutlined />}
            loading={isLoadingExport}
            disabled={isLoadingExport}
            onClick={handleExport}
          >
            {t('report.download')}
          </Button>
        </div>
      </div>
      <Table
        bordered
        dataSource={data?.items || []}
        columns={columns}
        className={`${styles.whiteHeader}`}
        size="small"
        loading={isLoading}
        rowKey={record => record.id}
        pagination={{
          pageSize: paramDepartmentLate.maxResultCount,
          current:
            paramDepartmentLate.skipCount / paramDepartmentLate.maxResultCount +
            1,
          total: data?.totalCount,
          showSizeChanger: true,
          onChange(page, pageSize) {
            setParamDepartmentLate({
              ...paramDepartmentLate,
              skipCount: (page - 1) * pageSize,
              maxResultCount: pageSize,
            })
          },
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
      />
      <ModalDepartment />
    </div>
  )
}

export default ReportDepartmentLatePage
