/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import ModalReasonDetail from '@/src/pages/Departure/components/ModalDepartureFlightDetail/TabModalReasons/ModalReasonDetail'
import {
  openModalFTH,
  openModalReasonDetail,
  setSelectedReasonId,
} from '@/src/store/ModalDepartureFlightSlice'
import { DeleteFilled, EditFilled } from '@ant-design/icons'
import {
  Button,
  Flex,
  message,
  Modal,
  Table,
  Typography,
  type TableProps,
} from 'antd'
import styles from './index.module.scss'
// import { DeleteFilled } from '@ant-design/icons'
import {
  REASON_TYPE_REASON_CANCEL,
  REASON_TYPE_REASON_DELAY,
  REASON_TYPE_REASON_FHT,
} from '@/src/constants'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getAttribute } from '@/src/service/attribute'
import { getReasonGroupType } from '@/src/service/reasonGroupType'
import { deleteReason, getReasonList } from '@/src/service/reasons'
import { getReasonType } from '@/src/service/reasonType'
import { getServiceDept } from '@/src/service/serviceDept'
import { useMutation, useQuery } from '@tanstack/react-query'
import ModalFTH from './ModalFTH'
import toHourMinute from '@/src/helper/convertHM'
import { handleApiError } from '@/src/helper/handleApiError'

const flattenReasons = (nodes: any[]): any[] => {
  let result: any[] = []
  nodes.forEach(node => {
    result.push({ code: node.code, description: node.description })
    if (node.children && node.children.length > 0) {
      result = result.concat(flattenReasons(node.children))
    }
  })
  return result
}

const TabModalReasons = () => {
  const dispatch = useAppDispatch()
  const { selectFlightModalId, selectedReasonId } = useAppSelector(
    state => state.modalDepartureFlight
  )
  const {
    data: reasonsList,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ['reasons', selectFlightModalId],
    queryFn: () => getReasonList(selectFlightModalId as string),
    enabled: !!selectFlightModalId,
  })

  const reasonsListReasonDelayAndCancel = reasonsList?.filter(
    (item: any) =>
      item.type === REASON_TYPE_REASON_DELAY ||
      item.type === REASON_TYPE_REASON_CANCEL
  )
  const reasonsListFHT = reasonsList?.filter(
    (item: any) => item.type === REASON_TYPE_REASON_FHT
  )

  const { data: reasonsType }: any = useQuery({
    queryKey: ['reasons-types'],
    queryFn: () => getReasonType(),
  })

  const { data: reasonGroupType }: any = useQuery({
    queryKey: ['reason-group-types'],
    queryFn: () => getReasonGroupType(),
  })

  const { data: serviceDept }: any = useQuery({
    queryKey: ['service-dept'],
    queryFn: () => getServiceDept(),
  })

  const { data: attribute }: any = useQuery({
    queryKey: ['attribute'],
    queryFn: () => getAttribute(),
  })

  const mutationDelete = useMutation({
    mutationFn: () =>
      deleteReason(selectFlightModalId as string, selectedReasonId as string),
    onSuccess() {
      dispatch(setSelectedReasonId(null))
      message.success('Reason deleted successfully!')
      refetch()
    },
    onError(error) {
      handleApiError(error)
    },
  })

  const reasonsFlat = reasonsType ? flattenReasons(reasonsType) : []

  const getReasonDescription = (code: number | string) => {
    const reason = reasonsFlat?.find(
      (item: any) => String(item.code) === String(code)
    )
    return reason ? reason.description : code
  }

  const columnsReason: TableProps<any>['columns'] = [
    {
      title: (
        <div>
          Delay time <span className="text-slate-400 font-normal">(hh:mm)</span>
        </div>
      ),
      width: '20%',
      dataIndex: 'delayTime',
      render: (text: any) => toHourMinute(text),
      align: 'center',
    },
    {
      title: 'Content',
      width: '40%',
      render: (record: any) => (
        <div>
          <div>
            Reason:{' '}
            <div className="pl-4">{getReasonDescription(record.reasons)}</div>
          </div>
          {
            <div>
              Remark:
              <div className="pl-4">
                <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                  {record.remark}
                </Typography.Paragraph>
              </div>
            </div>
          }
        </div>
      ),
    },
    {
      title: 'Service dept',
      width: '20%',
      dataIndex: 'serviceDept',
      render: (text: any) => {
        const dept = serviceDept?.find((item: any) => item.value === text)
        return dept ? dept.displayName : text
      },
    },
    {
      title: 'Attribute',
      width: '20%',
      dataIndex: 'attribute',
      render: (text: any) => {
        const attr = attribute?.find((item: any) => item.value === text)
        return attr ? attr.displayName : text
      },
    },
    {
      title: 'Action',
      width: '10%',
      align: 'center',
      render: (record: any) => (
        <Flex justify="center" gap={4}>
          <Button
            icon={<EditFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedReasonId(record.id))
              dispatch(openModalReasonDetail())
            }}
          />
          <Button
            icon={<DeleteFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedReasonId(record.id))
              Modal.confirm({
                title: 'Delete Reason?',
                content: 'Do you want to remove this reason?',
                okText: 'Yes',
                cancelText: 'No',
                closable: false,
                onOk: () => mutationDelete.mutate(),
              })
            }}
          />
        </Flex>
      ),
    },
  ]

  const columnsFHT: TableProps<any>['columns'] = [
    {
      title: (
        <div>
          NETD <span className="text-slate-400 font-normal">(hh:mm)</span>
        </div>
      ),
      width: '20%',
      dataIndex: 'fhtnetdTime',
      render: (text: any) => toHourMinute(text),
      align: 'center',
    },
    {
      title: 'Content',
      width: '40%',
      render: (record: any) => {
        return (
          <div>
            <p>
              Reason Groups:{' '}
              <div className="pl-4">
                {
                  reasonGroupType?.find(
                    (item: any) => item.value === record.reasonGroup
                  )?.displayName
                }
              </div>
            </p>
            <div className="flex flex-col">
              <div className="">Reasons: </div>
              <div className="pl-4">{getReasonDescription(record.reasons)}</div>
            </div>
            {record?.remark && (
              <p>
                Remark:{' '}
                {
                  <div className="pl-4">
                    <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                      {record.remark}
                    </Typography.Paragraph>
                  </div>
                }
              </p>
            )}
            {record?.event && (
              <div>
                <div>Event:</div>{' '}
                <div className="pl-4">
                  <Typography.Paragraph style={{ whiteSpace: 'pre-line' }}>
                    {record.event}
                  </Typography.Paragraph>
                </div>
              </div>
            )}
          </div>
        )
      },
    },
    {
      title: 'Service dept',
      width: '20%',
      dataIndex: 'serviceDept',
      render: (text: any) => {
        const dept = serviceDept?.find((item: any) => item.value === text)
        return dept ? dept.displayName : text
      },
    },
    {
      title: 'Attribute',
      width: '20%',
      dataIndex: 'attribute',
      render: (text: any) => {
        const attr = attribute?.find((item: any) => item.value === text)
        return attr ? attr.displayName : text
      },
    },
    {
      title: 'Action',
      width: '10%',
      align: 'center',
      render: (record: any) => (
        <Flex justify="center" gap={4}>
          <Button
            icon={<EditFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedReasonId(record.id))
              dispatch(openModalFTH())
            }}
          />
          <Button
            icon={<DeleteFilled />}
            type="text"
            onClick={() => {
              dispatch(setSelectedReasonId(record.id))
              Modal.confirm({
                title: 'Delete Reason?',
                content: 'Do you want to remove this reason?',
                okText: 'Yes',
                cancelText: 'No',
                closable: false,
                onOk: () => mutationDelete.mutate(),
              })
            }}
          />
        </Flex>
      ),
    },
  ]

  return (
    <div className="bg-[#F0F1F3FF] p-4">
      <div className="flex flex-col gap-y-4">
        <div className="font-bold text-primary uppercase">REASON</div>
        <Table
          dataSource={reasonsListReasonDelayAndCancel}
          bordered
          size="small"
          columns={columnsReason}
          className={`${styles.whiteHeader}`}
          rowKey={record => record.id}
          pagination={false}
          loading={isFetching}
        />
        <Flex justify="flex-end" className="items-center flex !py-1 ">
          {reasonsListReasonDelayAndCancel?.length < 3 && (
            <Button
              type="primary"
              onClick={() => {
                dispatch(openModalReasonDetail())
              }}
            >
              Create reason DELAY/CANCEL
            </Button>
          )}
        </Flex>
        <ModalReasonDetail />
        <ModalReasonDetail />
        <div className="font-bold text-primary uppercase">FHT</div>
        <Table
          dataSource={reasonsListFHT}
          bordered
          size="small"
          rowKey={record => record.id}
          columns={columnsFHT}
          className={`${styles.whiteHeader}`}
          pagination={false}
          loading={isFetching}
        />
        <Flex justify="flex-end" className="items-center flex !py-1 ">
          <Button
            type="primary"
            onClick={() => {
              dispatch(openModalFTH())
            }}
          >
            Create reason FHT
          </Button>
        </Flex>
        <ModalFTH />
      </div>
    </div>
  )
}

export default TabModalReasons
